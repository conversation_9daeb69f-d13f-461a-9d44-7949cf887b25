{"abi": [{"type": "constructor", "inputs": [{"name": "_logic", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "UPGRADE_DELAY", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "executeUpgrade", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getImplementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initiateUpgrade", "inputs": [{"name": "_newImplementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isPaused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "newImplementation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeValidAt", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "whitelistSelectors", "inputs": [{"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "whitelistedSelectors", "inputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "UpgradeInitiated", "inputs": [{"name": "newImplementation", "type": "address", "indexed": true, "internalType": "address"}, {"name": "upgradeValidAt", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedCall", "inputs": []}, {"type": "error", "name": "InvalidUpgrade", "inputs": []}, {"type": "error", "name": "Paused", "inputs": []}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x608060405234801561000f575f5ffd5b5060405161147438038061147483398101604081905261002e9161024b565b818161003a8282610043565b50505050610330565b61004c826100a1565b6040516001600160a01b038316907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b905f90a280511561009557610090828261011c565b505050565b61009d61018f565b5050565b806001600160a01b03163b5f036100db57604051634c9c8ce360e01b81526001600160a01b03821660048201526024015b60405180910390fd5b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80546001600160a01b0319166001600160a01b0392909216919091179055565b60605f5f846001600160a01b031684604051610138919061031a565b5f60405180830381855af49150503d805f8114610170576040519150601f19603f3d011682016040523d82523d5f602084013e610175565b606091505b5090925090506101868583836101b0565b95945050505050565b34156101ae5760405163b398979f60e01b815260040160405180910390fd5b565b6060826101c5576101c08261020f565b610208565b81511580156101dc57506001600160a01b0384163b155b1561020557604051639996b31560e01b81526001600160a01b03851660048201526024016100d2565b50805b9392505050565b80511561021e57805160208201fd5b60405163d6bda27560e01b815260040160405180910390fd5b634e487b7160e01b5f52604160045260245ffd5b5f5f6040838503121561025c575f5ffd5b82516001600160a01b0381168114610272575f5ffd5b60208401519092506001600160401b0381111561028d575f5ffd5b8301601f8101851361029d575f5ffd5b80516001600160401b038111156102b6576102b6610237565b604051601f8201601f19908116603f011681016001600160401b03811182821017156102e4576102e4610237565b6040528181528282016020018710156102fb575f5ffd5b8160208401602083015e5f602083830101528093505050509250929050565b5f82518060208501845e5f920191825250919050565b6111378061033d5f395ff3fe6080604052600436106100bf575f3560e01c8063629379e411610073578063aaf10f421161004d578063aaf10f4214610259578063b187bd261461026d578063b9ff0a39146102b2576100c6565b8063629379e4146101965780638456cb59146101e45780638b677b03146101f8576100c6565b8063479aa927116100a4578063479aa9271461010357806347fe8b1d14610122578063524f783a14610152576100c6565b806308490e80146100d05780633f4ba83a146100ef576100c6565b366100c657005b6100ce6102d1565b005b3480156100db575f5ffd5b506100ce6100ea366004610f1c565b610391565b3480156100fa575f5ffd5b506100ce61052e565b34801561010e575f5ffd5b506100ce61011d366004610fc3565b61064f565b34801561012d575f5ffd5b5061013862093a8081565b60405163ffffffff90911681526020015b60405180910390f35b34801561015d575f5ffd5b50700100000000000000000000000000000001546101389074010000000000000000000000000000000000000000900463ffffffff1681565b3480156101a1575f5ffd5b506101d46101b0366004610fde565b7001000000000000000000000000000000006020525f908152604090205460ff1681565b6040519015158152602001610149565b3480156101ef575f5ffd5b506100ce610887565b348015610203575f5ffd5b50700100000000000000000000000000000001546102349073ffffffffffffffffffffffffffffffffffffffff1681565b60405173ffffffffffffffffffffffffffffffffffffffff9091168152602001610149565b348015610264575f5ffd5b506102346109c3565b348015610278575f5ffd5b50700100000000000000000000000000000001546101d4907801000000000000000000000000000000000000000000000000900460ff1681565b3480156102bd575f5ffd5b506100ce6102cc36600461101d565b6109d1565b700100000000000000000000000000000001547801000000000000000000000000000000000000000000000000900460ff16801561035057505f80357fffffffff00000000000000000000000000000000000000000000000000000000168152700100000000000000000000000000000000602052604090205460ff16155b15610387576040517f9e87fac800000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b61038f610beb565b565b735615deb798bb3e4dfa0139dfa1b3d433cc23b72f73ffffffffffffffffffffffffffffffffffffffff16632f11d6536040518163ffffffff1660e01b8152600401602060405180830381865afa1580156103ee573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610412919061108b565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff161461047d576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024015b60405180910390fd5b5f5b8281101561052857817001000000000000000000000000000000005f8686858181106104ad576104ad6110a6565b90506020020160208101906104c29190610fde565b7fffffffff0000000000000000000000000000000000000000000000000000000016815260208101919091526040015f2080547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001691151591909117905560010161047f565b50505050565b735615deb798bb3e4dfa0139dfa1b3d433cc23b72f73ffffffffffffffffffffffffffffffffffffffff16632f11d6536040518163ffffffff1660e01b8152600401602060405180830381865afa15801561058b573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906105af919061108b565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610615576040517f8e4a23d6000000000000000000000000000000000000000000000000000000008152336004820152602401610474565b70010000000000000000000000000000000180547fffffffffffffff00ffffffffffffffffffffffffffffffffffffffffffffffff169055565b735615deb798bb3e4dfa0139dfa1b3d433cc23b72f73ffffffffffffffffffffffffffffffffffffffff1663c4d5608a6040518163ffffffff1660e01b8152600401602060405180830381865afa1580156106ac573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906106d0919061108b565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610736576040517f8e4a23d6000000000000000000000000000000000000000000000000000000008152336004820152602401610474565b70010000000000000000000000000000000180547fffffffffffffffffffffffff00000000000000000000000000000000000000001673ffffffffffffffffffffffffffffffffffffffff83169081179091556107ca5770010000000000000000000000000000000180547fffffffffffffffff00000000ffffffffffffffffffffffffffffffffffffffff169055610808565b6107d762093a80426110d3565b70010000000000000000000000000000000160146101000a81548163ffffffff021916908363ffffffff1602179055505b700100000000000000000000000000000001546040517401000000000000000000000000000000000000000090910463ffffffff16815273ffffffffffffffffffffffffffffffffffffffff8216907f902b264a1c29d614597a352703ecefc3b9029ace9f396848eda14aee6151f5c59060200160405180910390a250565b735615deb798bb3e4dfa0139dfa1b3d433cc23b72f73ffffffffffffffffffffffffffffffffffffffff16632f11d6536040518163ffffffff1660e01b8152600401602060405180830381865afa1580156108e4573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610908919061108b565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff161461096e576040517f8e4a23d6000000000000000000000000000000000000000000000000000000008152336004820152602401610474565b70010000000000000000000000000000000180547fffffffffffffff00ffffffffffffffffffffffffffffffffffffffffffffffff167801000000000000000000000000000000000000000000000000179055565b5f6109cc610bfb565b905090565b735615deb798bb3e4dfa0139dfa1b3d433cc23b72f73ffffffffffffffffffffffffffffffffffffffff1663c4d5608a6040518163ffffffff1660e01b8152600401602060405180830381865afa158015610a2e573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610a52919061108b565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610ab8576040517f8e4a23d6000000000000000000000000000000000000000000000000000000008152336004820152602401610474565b7001000000000000000000000000000000015474010000000000000000000000000000000000000000900463ffffffff16421015610b22576040517f4be85dc000000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b7001000000000000000000000000000000015473ffffffffffffffffffffffffffffffffffffffff16610b81576040517f4be85dc000000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b70010000000000000000000000000000000154604080516020601f8501819004810282018101909252838152610be79273ffffffffffffffffffffffffffffffffffffffff169185908590819084018382808284375f92019190915250610c3a92505050565b5050565b61038f610bf6610bfb565b610ca1565b5f6109cc7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc5473ffffffffffffffffffffffffffffffffffffffff1690565b610c4382610cbf565b60405173ffffffffffffffffffffffffffffffffffffffff8316907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b905f90a2805115610c9957610c948282610d8d565b505050565b610be7610e0e565b365f5f375f5f365f845af43d5f5f3e808015610cbb573d5ff35b3d5ffd5b8073ffffffffffffffffffffffffffffffffffffffff163b5f03610d27576040517f4c9c8ce300000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff82166004820152602401610474565b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80547fffffffffffffffffffffffff00000000000000000000000000000000000000001673ffffffffffffffffffffffffffffffffffffffff92909216919091179055565b60605f5f8473ffffffffffffffffffffffffffffffffffffffff1684604051610db69190611114565b5f60405180830381855af49150503d805f8114610dee576040519150601f19603f3d011682016040523d82523d5f602084013e610df3565b606091505b5091509150610e03858383610e46565b925050505b92915050565b341561038f576040517fb398979f00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b606082610e5b57610e5682610ed8565b610ed1565b8151158015610e7f575073ffffffffffffffffffffffffffffffffffffffff84163b155b15610ece576040517f9996b31500000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff85166004820152602401610474565b50805b9392505050565b805115610ee757805160208201fd5b6040517fd6bda27500000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b50565b5f5f5f60408486031215610f2e575f5ffd5b833567ffffffffffffffff811115610f44575f5ffd5b8401601f81018613610f54575f5ffd5b803567ffffffffffffffff811115610f6a575f5ffd5b8660208260051b8401011115610f7e575f5ffd5b6020918201945092508401358015158114610f97575f5ffd5b809150509250925092565b73ffffffffffffffffffffffffffffffffffffffff81168114610f19575f5ffd5b5f60208284031215610fd3575f5ffd5b8135610ed181610fa2565b5f60208284031215610fee575f5ffd5b81357fffffffff0000000000000000000000000000000000000000000000000000000081168114610ed1575f5ffd5b5f5f6020838503121561102e575f5ffd5b823567ffffffffffffffff811115611044575f5ffd5b8301601f81018513611054575f5ffd5b803567ffffffffffffffff81111561106a575f5ffd5b85602082840101111561107b575f5ffd5b6020919091019590945092505050565b5f6020828403121561109b575f5ffd5b8151610ed181610fa2565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603260045260245ffd5b63ffffffff8181168382160190811115610e08577f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f82518060208501845e5f92019182525091905056fea164736f6c634300081d000a", "sourceMap": "513:3181:84:-:0;;;1139:101;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1222:6;1230:5;1155:52:9;1222:6:84;1230:5;1155:29:9;:52::i;:::-;1081:133;;1139:101:84;;513:3181;;2264:344:10;2355:37;2374:17;2355:18;:37::i;:::-;2407:36;;-1:-1:-1;;;;;2407:36:10;;;;;;;;2458:11;;:15;2454:148;;2489:53;2518:17;2537:4;2489:28;:53::i;:::-;;2264:344;;:::o;2454:148::-;2573:18;:16;:18::i;:::-;2264:344;;:::o;1671:281::-;1748:17;-1:-1:-1;;;;;1748:29:10;;1781:1;1748:34;1744:119;;1805:47;;-1:-1:-1;;;1805:47:10;;-1:-1:-1;;;;;1416:32:121;;1805:47:10;;;1398:51:121;1371:18;;1805:47:10;;;;;;;;1744:119;811:66;1872:73;;-1:-1:-1;;;;;;1872:73:10;-1:-1:-1;;;;;1872:73:10;;;;;;;;;;1671:281::o;3916:253:22:-;3999:12;4024;4038:23;4065:6;-1:-1:-1;;;;;4065:19:22;4085:4;4065:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4023:67:22;;-1:-1:-1;4023:67:22;-1:-1:-1;4107:55:22;4134:6;4023:67;;4107:26;:55::i;:::-;4100:62;3916:253;-1:-1:-1;;;;;3916:253:22:o;6113:122:10:-;6163:9;:13;6159:70;;6199:19;;-1:-1:-1;;;6199:19:10;;;;;;;;;;;6159:70;6113:122::o;4437:582:22:-;4581:12;4610:7;4605:408;;4633:19;4641:10;4633:7;:19::i;:::-;4605:408;;;4857:17;;:22;:49;;;;-1:-1:-1;;;;;;4883:18:22;;;:23;4857:49;4853:119;;;4933:24;;-1:-1:-1;;;4933:24:22;;-1:-1:-1;;;;;1416:32:121;;4933:24:22;;;1398:51:121;1371:18;;4933:24:22;1252:203:121;4853:119:22;-1:-1:-1;4992:10:22;4605:408;4437:582;;;;;:::o;5559:434::-;5690:17;;:21;5686:301;;5894:10;5888:17;5881:4;5869:10;5865:21;5858:48;5686:301;5957:19;;-1:-1:-1;;;5957:19:22;;;;;;;;;;;14:127:121;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:1101;234:6;242;295:2;283:9;274:7;270:23;266:32;263:52;;;311:1;308;301:12;263:52;337:16;;-1:-1:-1;;;;;382:31:121;;372:42;;362:70;;428:1;425;418:12;362:70;500:2;485:18;;479:25;451:5;;-1:-1:-1;;;;;;516:30:121;;513:50;;;559:1;556;549:12;513:50;582:22;;635:4;627:13;;623:27;-1:-1:-1;613:55:121;;664:1;661;654:12;613:55;691:9;;-1:-1:-1;;;;;712:30:121;;709:56;;;745:18;;:::i;:::-;794:2;788:9;886:2;848:17;;-1:-1:-1;;844:31:121;;;877:2;840:40;836:54;824:67;;-1:-1:-1;;;;;906:34:121;;942:22;;;903:62;900:88;;;968:18;;:::i;:::-;1004:2;997:22;1028;;;1069:15;;;1086:2;1065:24;1062:37;-1:-1:-1;1059:57:121;;;1112:1;1109;1102:12;1059:57;1161:6;1156:2;1152;1148:11;1143:2;1135:6;1131:15;1125:43;1214:1;1209:2;1200:6;1192;1188:19;1184:28;1177:39;1235:6;1225:16;;;;;146:1101;;;;;:::o;1460:301::-;1589:3;1627:6;1621:13;1673:6;1666:4;1658:6;1654:17;1649:3;1643:37;1735:1;1699:16;;1724:13;;;-1:-1:-1;1699:16:121;1460:301;-1:-1:-1;1460:301:121:o;:::-;513:3181:84;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "513:3181:84:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2649:11:11;:9;:11::i;:::-;513:3181:84;3052:286;;;;;;;;;;-1:-1:-1;3052:286:84;;;;;:::i;:::-;;:::i;2676:151::-;;;;;;;;;;;;;:::i;1466:567::-;;;;;;;;;;-1:-1:-1;1466:567:84;;;;;:::i;:::-;;:::i;678:45::-;;;;;;;;;;;;717:6;678:45;;;;;1380:10:121;1368:23;;;1350:42;;1338:2;1323:18;678:45:84;;;;;;;;1033:28;;;;;;;;;;-1:-1:-1;1033:28:84;;;;;;;;;;;817:51;;;;;;;;;;-1:-1:-1;817:51:84;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1905:14:121;;1898:22;1880:41;;1868:2;1853:18;817:51:84;1740:187:121;2522:148:84;;;;;;;;;;;;;:::i;929:32::-;;;;;;;;;;-1:-1:-1;929:32:84;;;;;;;;;;;2108:42:121;2096:55;;;2078:74;;2066:2;2051:18;929:32:84;1932:226:121;3344:102:84;;;;;;;;;;;;;:::i;1112:20::-;;;;;;;;;;-1:-1:-1;1112:20:84;;;;;;;;;;;2158:358;;;;;;;;;;-1:-1:-1;2158:358:84;;;;;:::i;:::-;;:::i;3452:240::-;3591:8;;;;;;;:50;;;;-1:-1:-1;3603:29:84;3624:7;;;;3603:29;;:20;:29;;;;;;;;:38;3591:50;3587:71;;;3650:8;;;;;;;;;;;;;;3587:71;3668:17;:15;:17::i;:::-;3452:240::o;3052:286::-;676:42:97;3166:27:84;;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3152:43;;:10;:43;;;3148:80;;3204:24;;;;;3217:10;3204:24;;;2078:74:121;2051:18;;3204:24:84;;;;;;;;3148:80;3243:9;3238:93;3254:20;;;3238:93;;;3318:13;3281:20;:34;3302:9;;3312:1;3302:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;3281:34;;;;;;;;;;;;;-1:-1:-1;3281:34:84;:50;;;;;;;;;;;;;-1:-1:-1;3276:3:84;3238:93;;;;3052:286;;;:::o;2676:151::-;676:42:97;2732:27:84;;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2718:43;;:10;:43;;;2714:80;;2770:24;;;;;2783:10;2770:24;;;2078:74:121;2051:18;;2770:24:84;1932:226:121;2714:80:84;2804:8;:16;;;;;;2676:151::o;1466:567::-;676:42:97;1556:29:84;;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1542:45;;:10;:45;;;1538:82;;1596:24;;;;;1609:10;1596:24;;;2078:74:121;2051:18;;1596:24:84;1932:226:121;1538:82:84;1630:17;:38;;;;;;;;;;;;;1678:282;;1844:14;:18;;;;;;1678:282;;;1910:39;717:6;1917:15;1910:39;:::i;:::-;1893:14;;:56;;;;;;;;;;;;;;;;;;1678:282;2011:14;;1974:52;;2011:14;;;;;;1350:42:121;;1974:52:84;;;;;;1338:2:121;1323:18;1974:52:84;;;;;;;1466:567;:::o;2522:148::-;676:42:97;2576:27:84;;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2562:43;;:10;:43;;;2558:80;;2614:24;;;;;2627:10;2614:24;;;2078:74:121;2051:18;;2614:24:84;1932:226:121;2558:80:84;2648:8;:15;;;;;;;;2522:148::o;3344:102::-;3396:7;3422:17;:15;:17::i;:::-;3415:24;;3344:102;:::o;2158:358::-;676:42:97;2240:29:84;;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2226:45;;:10;:45;;;2222:82;;2280:24;;;;;2293:10;2280:24;;;2078:74:121;2051:18;;2280:24:84;1932:226:121;2222:82:84;2336:14;;;;;;;2318:15;:32;2314:61;;;2359:16;;;;;;;;;;;;;;2314:61;2389:17;;:31;:17;2385:60;;2429:16;;;;;;;;;;;;;;2385:60;2485:17;;2455:54;;;;;;;;;;;;;;;;;;;;;;;;2485:17;;;2504:4;;;;;;2455:54;;2504:4;;;;2455:54;;;;;;;;;-1:-1:-1;2455:29:84;;-1:-1:-1;;;2455:54:84:i;:::-;2158:358;;:::o;2323:83:11:-;2371:28;2381:17;:15;:17::i;:::-;2371:9;:28::i;1583:132:9:-;1650:7;1676:32;811:66:10;1519:53;;;;1441:138;2264:344;2355:37;2374:17;2355:18;:37::i;:::-;2407:36;;;;;;;;;;;2458:11;;:15;2454:148;;2489:53;2518:17;2537:4;2489:28;:53::i;:::-;;2158:358:84;;:::o;2454:148:10:-;2573:18;:16;:18::i;949:895:11:-;1287:14;1284:1;1281;1268:34;1501:1;1498;1482:14;1479:1;1463:14;1456:5;1443:60;1577:16;1574:1;1571;1556:38;1615:6;1682:66;;;;1797:16;1794:1;1787:27;1682:66;1717:16;1714:1;1707:27;1671:281:10;1748:17;:29;;;1781:1;1748:34;1744:119;;1805:47;;;;;2108:42:121;2096:55;;1805:47:10;;;2078:74:121;2051:18;;1805:47:10;1932:226:121;1744:119:10;811:66;1872:73;;;;;;;;;;;;;;;1671:281::o;3916:253:22:-;3999:12;4024;4038:23;4065:6;:19;;4085:4;4065:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4023:67;;;;4107:55;4134:6;4142:7;4151:10;4107:26;:55::i;:::-;4100:62;;;;3916:253;;;;;:::o;6113:122:10:-;6163:9;:13;6159:70;;6199:19;;;;;;;;;;;;;;4437:582:22;4581:12;4610:7;4605:408;;4633:19;4641:10;4633:7;:19::i;:::-;4605:408;;;4857:17;;:22;:49;;;;-1:-1:-1;4883:18:22;;;;:23;4857:49;4853:119;;;4933:24;;;;;2108:42:121;2096:55;;4933:24:22;;;2078:74:121;2051:18;;4933:24:22;1932:226:121;4853:119:22;-1:-1:-1;4992:10:22;4605:408;4437:582;;;;;:::o;5559:434::-;5690:17;;:21;5686:301;;5894:10;5888:17;5881:4;5869:10;5865:21;5858:48;5686:301;5957:19;;;;;;;;;;;;;;5686:301;5559:434;:::o;14:776:121:-;105:6;113;121;174:2;162:9;153:7;149:23;145:32;142:52;;;190:1;187;180:12;142:52;230:9;217:23;263:18;255:6;252:30;249:50;;;295:1;292;285:12;249:50;318:22;;371:4;363:13;;359:27;-1:-1:-1;349:55:121;;400:1;397;390:12;349:55;440:2;427:16;466:18;458:6;455:30;452:50;;;498:1;495;488:12;452:50;553:7;546:4;536:6;533:1;529:14;525:2;521:23;517:34;514:47;511:67;;;574:1;571;564:12;511:67;605:4;597:13;;;;-1:-1:-1;629:6:121;-1:-1:-1;670:20:121;;657:34;727:13;;720:21;710:32;;700:60;;756:1;753;746:12;700:60;779:5;769:15;;;14:776;;;;;:::o;795:154::-;881:42;874:5;870:54;863:5;860:65;850:93;;939:1;936;929:12;954:247;1013:6;1066:2;1054:9;1045:7;1041:23;1037:32;1034:52;;;1082:1;1079;1072:12;1034:52;1121:9;1108:23;1140:31;1165:5;1140:31;:::i;1403:332::-;1461:6;1514:2;1502:9;1493:7;1489:23;1485:32;1482:52;;;1530:1;1527;1520:12;1482:52;1569:9;1556:23;1619:66;1612:5;1608:78;1601:5;1598:89;1588:117;;1701:1;1698;1691:12;2163:586;2233:6;2241;2294:2;2282:9;2273:7;2269:23;2265:32;2262:52;;;2310:1;2307;2300:12;2262:52;2350:9;2337:23;2383:18;2375:6;2372:30;2369:50;;;2415:1;2412;2405:12;2369:50;2438:22;;2491:4;2483:13;;2479:27;-1:-1:-1;2469:55:121;;2520:1;2517;2510:12;2469:55;2560:2;2547:16;2586:18;2578:6;2575:30;2572:50;;;2618:1;2615;2608:12;2572:50;2663:7;2658:2;2649:6;2645:2;2641:15;2637:24;2634:37;2631:57;;;2684:1;2681;2674:12;2631:57;2715:2;2707:11;;;;;2737:6;;-1:-1:-1;2163:586:121;-1:-1:-1;;;2163:586:121:o;2754:251::-;2824:6;2877:2;2865:9;2856:7;2852:23;2848:32;2845:52;;;2893:1;2890;2883:12;2845:52;2925:9;2919:16;2944:31;2969:5;2944:31;:::i;3010:184::-;3062:77;3059:1;3052:88;3159:4;3156:1;3149:15;3183:4;3180:1;3173:15;3199:321;3294:10;3267:18;;;3287;;;3263:43;;3318:19;;3315:199;;;3370:77;3367:1;3360:88;3471:4;3468:1;3461:15;3499:4;3496:1;3489:15;3525:301;3654:3;3692:6;3686:13;3738:6;3731:4;3723:6;3719:17;3714:3;3708:37;3800:1;3764:16;;3789:13;;;-1:-1:-1;3764:16:121;3525:301;-1:-1:-1;3525:301:121:o", "linkReferences": {}}, "methodIdentifiers": {"UPGRADE_DELAY()": "47fe8b1d", "executeUpgrade(bytes)": "b9ff0a39", "getImplementation()": "aaf10f42", "initiateUpgrade(address)": "479aa927", "isPaused()": "b187bd26", "newImplementation()": "8b677b03", "pause()": "8456cb59", "unpause()": "3f4ba83a", "upgradeValidAt()": "524f783a", "whitelistSelectors(bytes4[],bool)": "08490e80", "whitelistedSelectors(bytes4)": "629379e4"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_logic\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC1967NonPayable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidUpgrade\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Paused\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"upgradeValidAt\",\"type\":\"uint32\"}],\"name\":\"UpgradeInitiated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"inputs\":[],\"name\":\"UPGRADE_DELAY\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"executeUpgrade\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getImplementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newImplementation\",\"type\":\"address\"}],\"name\":\"initiateUpgrade\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"newImplementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"upgradeValidAt\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"},{\"internalType\":\"bool\",\"name\":\"isWhitelisted\",\"type\":\"bool\"}],\"name\":\"whitelistSelectors\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"name\":\"whitelistedSelectors\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"All storage slots are offset to avoid conflicts with the implementation\",\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"ERC1967InvalidImplementation(address)\":[{\"details\":\"The `implementation` of the proxy is invalid.\"}],\"ERC1967NonPayable()\":[{\"details\":\"An upgrade function sees `msg.value > 0` that may be lost.\"}],\"FailedCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}]},\"events\":{\"Upgraded(address)\":{\"details\":\"Emitted when the implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"initiateUpgrade(address)\":{\"params\":{\"_newImplementation\":\"The address of the new implementation.\"}},\"whitelistSelectors(bytes4[],bool)\":{\"details\":\"Allows the pause admin to whitelist selectors that can be called even if the proxy is paused, this is useful for allowing vaults to continue to exit funds but not initiate new entries, for example.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"executeUpgrade(bytes)\":{\"notice\":\"Executes an upgrade, only the upgradeAdmin can execute this to allow for a post upgrade function call.\"},\"initiateUpgrade(address)\":{\"notice\":\"Initiates an upgrade and sets the upgrade delay.\"},\"isPaused()\":{\"notice\":\"Whether the proxy is paused\"},\"newImplementation()\":{\"notice\":\"The address of the new implementation\"},\"upgradeValidAt()\":{\"notice\":\"The timestamp at which the upgrade will be valid\"},\"whitelistedSelectors(bytes4)\":{\"notice\":\"Mapping of selector to whether it is whitelisted during a paused state\"}},\"notice\":\"A proxy that allows for a timelocked upgrade and selective pausing of the implementation\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/proxy/TimelockUpgradeableProxy.sol\":\"TimelockUpgradeableProxy\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c\",\"dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz\"]},\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a\",\"dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ\"]},\"node_modules/@openzeppelin/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d\",\"dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f\",\"dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD\"]},\"node_modules/@openzeppelin/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"node_modules/@openzeppelin/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/proxy/TimelockUpgradeableProxy.sol\":{\"keccak256\":\"0x4854e20c5c6598955ce141e2eb8af08caa627edda51d721b2104138eb6cd594f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1b52d8cd72d9c3cc8d2bd0d7282c54205127ccc970e8dc0956c14339bcb87994\",\"dweb:/ipfs/QmS1wC538xpZgngfXcdk5jZz48H4CwWMvJqt75GktDxKUs\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ERC1967InvalidImplementation"}, {"inputs": [], "type": "error", "name": "ERC1967Non<PERSON>ayable"}, {"inputs": [], "type": "error", "name": "FailedCall"}, {"inputs": [], "type": "error", "name": "InvalidUpgrade"}, {"inputs": [], "type": "error", "name": "Paused"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "upgradeValidAt", "type": "uint32", "indexed": false}], "type": "event", "name": "UpgradeInitiated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "fallback"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "UPGRADE_DELAY", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "executeUpgrade"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_newImplementation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateUpgrade"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "newImplementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pause"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unpause"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "upgradeValidAt", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "whitelistSelectors"}, {"inputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "whitelistedSelectors", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"initiateUpgrade(address)": {"params": {"_newImplementation": "The address of the new implementation."}}, "whitelistSelectors(bytes4[],bool)": {"details": "Allows the pause admin to whitelist selectors that can be called even if the proxy is paused, this is useful for allowing vaults to continue to exit funds but not initiate new entries, for example."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"executeUpgrade(bytes)": {"notice": "Executes an upgrade, only the upgradeAdmin can execute this to allow for a post upgrade function call."}, "initiateUpgrade(address)": {"notice": "Initiates an upgrade and sets the upgrade delay."}, "isPaused()": {"notice": "Whether the proxy is paused"}, "newImplementation()": {"notice": "The address of the new implementation"}, "upgradeValidAt()": {"notice": "The timestamp at which the upgrade will be valid"}, "whitelistedSelectors(bytes4)": {"notice": "Mapping of selector to whether it is whitelisted during a paused state"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/proxy/TimelockUpgradeableProxy.sol": "TimelockUpgradeableProxy"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol": {"keccak256": "0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5", "urls": ["bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c", "dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618", "urls": ["bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a", "dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b", "urls": ["bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d", "dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Address.sol": {"keccak256": "0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0", "urls": ["bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f", "dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/proxy/TimelockUpgradeableProxy.sol": {"keccak256": "0x4854e20c5c6598955ce141e2eb8af08caa627edda51d721b2104138eb6cd594f", "urls": ["bzz-raw://1b52d8cd72d9c3cc8d2bd0d7282c54205127ccc970e8dc0956c14339bcb87994", "dweb:/ipfs/QmS1wC538xpZgngfXcdk5jZz48H4CwWMvJqt75GktDxKUs"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}}, "version": 1}, "id": 84}