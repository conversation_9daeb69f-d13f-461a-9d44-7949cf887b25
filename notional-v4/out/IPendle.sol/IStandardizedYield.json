{"abi": [{"type": "function", "name": "accruedRewards", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewardAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "assetInfo", "inputs": [], "outputs": [{"name": "assetType", "type": "uint8", "internalType": "enum IStandardizedYield.AssetType"}, {"name": "assetAddress", "type": "address", "internalType": "address"}, {"name": "assetDecimals", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimRewards", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewardAmounts", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "amountTokenToDeposit", "type": "uint256", "internalType": "uint256"}, {"name": "minSharesOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "amountSharesOut", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "exchangeRate", "inputs": [], "outputs": [{"name": "res", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRewardTokens", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTokensIn", "inputs": [], "outputs": [{"name": "res", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTokensOut", "inputs": [], "outputs": [{"name": "res", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "isValidTokenIn", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isValidTokenOut", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "previewDeposit", "inputs": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "amountTokenToDeposit", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "amountSharesOut", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewRedeem", "inputs": [{"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "amountSharesToRedeem", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "amountTokenOut", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "redeem", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "amountSharesToRedeem", "type": "uint256", "internalType": "uint256"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "minTokenOut", "type": "uint256", "internalType": "uint256"}, {"name": "burnFromInternalBalance", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "amountTokenOut", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardIndexesCurrent", "inputs": [], "outputs": [{"name": "indexes", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardIndexesStored", "inputs": [], "outputs": [{"name": "indexes", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "yieldToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ClaimRewards", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "rewardTokens", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "rewardAmounts", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenIn", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountDeposited", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountSyOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Redeem", "inputs": [{"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenOut", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountSyToRedeem", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountTokenOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"accruedRewards(address)": "128fced1", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "assetInfo()": "a40bee50", "balanceOf(address)": "70a08231", "claimRewards(address)": "ef5cfb8c", "decimals()": "313ce567", "deposit(address,address,uint256,uint256)": "20e8c565", "exchangeRate()": "3ba0b9a9", "getRewardTokens()": "c4f59f9b", "getTokensIn()": "213cae63", "getTokensOut()": "071bc3c9", "isValidTokenIn(address)": "fa5a4f06", "isValidTokenOut(address)": "784367d6", "name()": "06fdde03", "previewDeposit(address,uint256)": "b8f82b26", "previewRedeem(address,uint256)": "cbe52ae3", "redeem(address,uint256,address,uint256,bool)": "769f8e5d", "rewardIndexesCurrent()": "f8b2f991", "rewardIndexesStored()": "da88ecb4", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "yieldToken()": "76d5de85"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"rewardTokens\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"rewardAmounts\",\"type\":\"uint256[]\"}],\"name\":\"ClaimRewards\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountDeposited\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountSyOut\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountSyToRedeem\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountTokenOut\",\"type\":\"uint256\"}],\"name\":\"Redeem\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"accruedRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewardAmounts\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"assetInfo\",\"outputs\":[{\"internalType\":\"enum IStandardizedYield.AssetType\",\"name\":\"assetType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"assetAddress\",\"type\":\"address\"},{\"internalType\":\"uint8\",\"name\":\"assetDecimals\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"claimRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewardAmounts\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountTokenToDeposit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minSharesOut\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountSharesOut\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"exchangeRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"res\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardTokens\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTokensIn\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"res\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTokensOut\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"res\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"isValidTokenIn\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"isValidTokenOut\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountTokenToDeposit\",\"type\":\"uint256\"}],\"name\":\"previewDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountSharesOut\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountSharesToRedeem\",\"type\":\"uint256\"}],\"name\":\"previewRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountTokenOut\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountSharesToRedeem\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minTokenOut\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"burnFromInternalBalance\",\"type\":\"bool\"}],\"name\":\"redeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountTokenOut\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardIndexesCurrent\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"indexes\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardIndexesStored\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"indexes\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"yieldToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"ClaimRewards(address,address[],uint256[])\":{\"details\":\"Emitted when (`user`) claims their rewards\"},\"Deposit(address,address,address,uint256,uint256)\":{\"details\":\"Emitted when any base tokens is deposited to mint shares\"},\"Redeem(address,address,address,uint256,uint256)\":{\"details\":\"Emitted when any shares are redeemed for base tokens\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"accruedRewards(address)\":{\"params\":{\"user\":\"the user to check for\"},\"returns\":{\"rewardAmounts\":\"an array of reward amounts in the same order as `getRewardTokens`\"}},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"Sets a `value` amount of tokens as the allowance of `spender` over the caller's tokens. Returns a boolean value indicating whether the operation succeeded. IMPORTANT: Beware that changing an allowance with this method brings the risk that someone may use both the old and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards: https://github.com/ethereum/EIPs/issues/20#issuecomment-********* Emits an {Approval} event.\"},\"assetInfo()\":{\"returns\":{\"assetAddress\":\"the address of the asset\",\"assetDecimals\":\"the decimals of the asset\",\"assetType\":\"the type of the asset (0 for ERC20 tokens, 1 for AMM liquidity tokens, 2 for bridged yield bearing tokens like wstETH, rETH on Arbi whose the underlying asset doesn't exist on the chain)\"}},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"claimRewards(address)\":{\"details\":\"Emits a `ClaimRewards` event See {getRewardTokens} for list of reward tokens\",\"params\":{\"user\":\"the user receiving their rewards\"},\"returns\":{\"rewardAmounts\":\"an array of reward amounts in the same order as `getRewardTokens`\"}},\"decimals()\":{\"details\":\"Returns the decimals places of the token.\"},\"deposit(address,address,uint256,uint256)\":{\"details\":\"Emits a {Deposit} event Requirements: - (`tokenIn`) must be a valid base token.\",\"params\":{\"amountTokenToDeposit\":\"amount of base tokens to be transferred from (`msg.sender`)\",\"minSharesOut\":\"reverts if amount of shares minted is lower than this\",\"receiver\":\"shares recipient address\",\"tokenIn\":\"address of the base tokens to mint shares\"},\"returns\":{\"amountSharesOut\":\"amount of shares minted\"}},\"exchangeRate()\":{\"details\":\"SYUtils's assetToSy & syToAsset should be used instead of raw multiplication & division\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"redeem(address,uint256,address,uint256,bool)\":{\"details\":\"Emits a {Redeem} event Requirements: - (`tokenOut`) must be a valid base token.\",\"params\":{\"amountSharesToRedeem\":\"amount of shares to be burned\",\"burnFromInternalBalance\":\"if true, burns from balance of `address(this)`, otherwise burns from `msg.sender`\",\"minTokenOut\":\"reverts if amount of base token redeemed is lower than this\",\"receiver\":\"recipient address\",\"tokenOut\":\"address of the base token to be redeemed\"},\"returns\":{\"amountTokenOut\":\"amount of base tokens redeemed\"}},\"symbol()\":{\"details\":\"Returns the symbol of the token.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from the caller's account to `to`. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism. `value` is then deducted from the caller's allowance. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"accruedRewards(address)\":{\"notice\":\"get the amount of unclaimed rewards for (`user`)\"},\"assetInfo()\":{\"notice\":\"This function contains information to interpret what the asset is\"},\"claimRewards(address)\":{\"notice\":\"claims reward for (`user`)\"},\"deposit(address,address,uint256,uint256)\":{\"notice\":\"mints an amount of shares by depositing a base token.\"},\"exchangeRate()\":{\"notice\":\"exchangeRate * syBalance / 1e18 must return the asset balance of the accountvice-versa, if a user uses some amount of tokens equivalent to X asset, the amount of sy he can mint must be X * exchangeRate / 1e18\"},\"getRewardTokens()\":{\"notice\":\"returns the list of reward token addresses\"},\"getTokensIn()\":{\"notice\":\"returns all tokens that can mint this SY\"},\"getTokensOut()\":{\"notice\":\"returns all tokens that can be redeemed by this SY\"},\"redeem(address,uint256,address,uint256,bool)\":{\"notice\":\"redeems an amount of base tokens by burning some shares\"},\"yieldToken()\":{\"notice\":\"returns the address of the underlying yield token\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IPendle.sol\":\"IStandardizedYield\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"src/interfaces/IPendle.sol\":{\"keccak256\":\"0x272d09ba61f8bf889cf5030f6a06081baa0e997b5290f45d0e1d99497ebe7775\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://acb69ed34ec090f074a9888d25a180fd5a0de3d9e497b93d4500be606f4a5774\",\"dweb:/ipfs/QmafGeJ65HAmWFsFGihnSaKQFjUjd5JocssKyiVmCbwEmT\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "address[]", "name": "rewardTokens", "type": "address[]", "indexed": false}, {"internalType": "uint256[]", "name": "rewardAmounts", "type": "uint256[]", "indexed": false}], "type": "event", "name": "ClaimRewards", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "address", "name": "tokenIn", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountDeposited", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountSyOut", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "address", "name": "tokenOut", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountSyToRedeem", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountTokenOut", "type": "uint256", "indexed": false}], "type": "event", "name": "Redeem", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "accruedRewards", "outputs": [{"internalType": "uint256[]", "name": "rewardAmounts", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "assetInfo", "outputs": [{"internalType": "enum IStandardizedYield.AssetType", "name": "assetType", "type": "uint8"}, {"internalType": "address", "name": "assetAddress", "type": "address"}, {"internalType": "uint8", "name": "assetDecimals", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimRewards", "outputs": [{"internalType": "uint256[]", "name": "rewardAmounts", "type": "uint256[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "amountTokenToDeposit", "type": "uint256"}, {"internalType": "uint256", "name": "minSharesOut", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "amountSharesOut", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "exchangeRate", "outputs": [{"internalType": "uint256", "name": "res", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getRewardTokens", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getTokensIn", "outputs": [{"internalType": "address[]", "name": "res", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getTokensOut", "outputs": [{"internalType": "address[]", "name": "res", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isValidTokenIn", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isValidTokenOut", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "amountTokenToDeposit", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "amountSharesOut", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint256", "name": "amountSharesToRedeem", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewRedeem", "outputs": [{"internalType": "uint256", "name": "amountTokenOut", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amountSharesToRedeem", "type": "uint256"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint256", "name": "minTokenOut", "type": "uint256"}, {"internalType": "bool", "name": "burnFromInternalBalance", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "redeem", "outputs": [{"internalType": "uint256", "name": "amountTokenOut", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "rewardIndexesCurrent", "outputs": [{"internalType": "uint256[]", "name": "indexes", "type": "uint256[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardIndexesStored", "outputs": [{"internalType": "uint256[]", "name": "indexes", "type": "uint256[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "yieldToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"accruedRewards(address)": {"params": {"user": "the user to check for"}, "returns": {"rewardAmounts": "an array of reward amounts in the same order as `getRewardTokens`"}}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "Sets a `value` amount of tokens as the allowance of `spender` over the caller's tokens. Returns a boolean value indicating whether the operation succeeded. IMPORTANT: Beware that changing an allowance with this method brings the risk that someone may use both the old and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards: https://github.com/ethereum/EIPs/issues/20#issuecomment-********* Emits an {Approval} event."}, "assetInfo()": {"returns": {"assetAddress": "the address of the asset", "assetDecimals": "the decimals of the asset", "assetType": "the type of the asset (0 for ERC20 tokens, 1 for AMM liquidity tokens, 2 for bridged yield bearing tokens like wstETH, rETH on Arbi whose the underlying asset doesn't exist on the chain)"}}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "claimRewards(address)": {"details": "Emits a `ClaimRewards` event See {getRewardTokens} for list of reward tokens", "params": {"user": "the user receiving their rewards"}, "returns": {"rewardAmounts": "an array of reward amounts in the same order as `getRewardTokens`"}}, "decimals()": {"details": "Returns the decimals places of the token."}, "deposit(address,address,uint256,uint256)": {"details": "Emits a {Deposit} event Requirements: - (`tokenIn`) must be a valid base token.", "params": {"amountTokenToDeposit": "amount of base tokens to be transferred from (`msg.sender`)", "minSharesOut": "reverts if amount of shares minted is lower than this", "receiver": "shares recipient address", "tokenIn": "address of the base tokens to mint shares"}, "returns": {"amountSharesOut": "amount of shares minted"}}, "exchangeRate()": {"details": "SYUtils's assetToSy & syToAsset should be used instead of raw multiplication & division"}, "name()": {"details": "Returns the name of the token."}, "redeem(address,uint256,address,uint256,bool)": {"details": "Emits a {Redeem} event Requirements: - (`tokenOut`) must be a valid base token.", "params": {"amountSharesToRedeem": "amount of shares to be burned", "burnFromInternalBalance": "if true, burns from balance of `address(this)`, otherwise burns from `msg.sender`", "minTokenOut": "reverts if amount of base token redeemed is lower than this", "receiver": "recipient address", "tokenOut": "address of the base token to be redeemed"}, "returns": {"amountTokenOut": "amount of base tokens redeemed"}}, "symbol()": {"details": "Returns the symbol of the token."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "Moves a `value` amount of tokens from the caller's account to `to`. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event."}, "transferFrom(address,address,uint256)": {"details": "Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism. `value` is then deducted from the caller's allowance. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accruedRewards(address)": {"notice": "get the amount of unclaimed rewards for (`user`)"}, "assetInfo()": {"notice": "This function contains information to interpret what the asset is"}, "claimRewards(address)": {"notice": "claims reward for (`user`)"}, "deposit(address,address,uint256,uint256)": {"notice": "mints an amount of shares by depositing a base token."}, "exchangeRate()": {"notice": "exchangeRate * syBalance / 1e18 must return the asset balance of the accountvice-versa, if a user uses some amount of tokens equivalent to X asset, the amount of sy he can mint must be X * exchangeRate / 1e18"}, "getRewardTokens()": {"notice": "returns the list of reward token addresses"}, "getTokensIn()": {"notice": "returns all tokens that can mint this SY"}, "getTokensOut()": {"notice": "returns all tokens that can be redeemed by this SY"}, "redeem(address,uint256,address,uint256,bool)": {"notice": "redeems an amount of base tokens by burning some shares"}, "yieldToken()": {"notice": "returns the address of the underlying yield token"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/IPendle.sol": "IStandardizedYield"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "src/interfaces/IPendle.sol": {"keccak256": "0x272d09ba61f8bf889cf5030f6a06081baa0e997b5290f45d0e1d99497ebe7775", "urls": ["bzz-raw://acb69ed34ec090f074a9888d25a180fd5a0de3d9e497b93d4500be606f4a5774", "dweb:/ipfs/QmafGeJ65HAmWFsFGihnSaKQFjUjd5JocssKyiVmCbwEmT"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 68}