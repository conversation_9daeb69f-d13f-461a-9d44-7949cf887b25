{"abi": [{"type": "function", "name": "redeemPyToToken", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "YT", "type": "address", "internalType": "address"}, {"name": "netPyIn", "type": "uint256", "internalType": "uint256"}, {"name": "output", "type": "tuple", "internalType": "struct IPRouter.TokenOutput", "components": [{"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "minTokenOut", "type": "uint256", "internalType": "uint256"}, {"name": "tokenRedeemSy", "type": "address", "internalType": "address"}, {"name": "pendleSwap", "type": "address", "internalType": "address"}, {"name": "swapData", "type": "tuple", "internalType": "struct IPRouter.SwapData", "components": [{"name": "swapType", "type": "uint8", "internalType": "enum IPRouter.SwapType"}, {"name": "extRouter", "type": "address", "internalType": "address"}, {"name": "extCalldata", "type": "bytes", "internalType": "bytes"}, {"name": "needScale", "type": "bool", "internalType": "bool"}]}]}], "outputs": [{"name": "netTokenOut", "type": "uint256", "internalType": "uint256"}, {"name": "netSyInterm", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "swapExactPtForToken", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "exactPtIn", "type": "uint256", "internalType": "uint256"}, {"name": "output", "type": "tuple", "internalType": "struct IPRouter.TokenOutput", "components": [{"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "minTokenOut", "type": "uint256", "internalType": "uint256"}, {"name": "tokenRedeemSy", "type": "address", "internalType": "address"}, {"name": "pendleSwap", "type": "address", "internalType": "address"}, {"name": "swapData", "type": "tuple", "internalType": "struct IPRouter.SwapData", "components": [{"name": "swapType", "type": "uint8", "internalType": "enum IPRouter.SwapType"}, {"name": "extRouter", "type": "address", "internalType": "address"}, {"name": "extCalldata", "type": "bytes", "internalType": "bytes"}, {"name": "needScale", "type": "bool", "internalType": "bool"}]}]}, {"name": "limit", "type": "tuple", "internalType": "struct IPRouter.LimitOrderData", "components": [{"name": "limitRouter", "type": "address", "internalType": "address"}, {"name": "epsSkipMarket", "type": "uint256", "internalType": "uint256"}, {"name": "normalFills", "type": "tuple[]", "internalType": "struct IPRouter.FillOrderParams[]", "components": [{"name": "order", "type": "tuple", "internalType": "struct IPRouter.Order", "components": [{"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "orderType", "type": "uint8", "internalType": "enum IPRouter.OrderType"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "YT", "type": "address", "internalType": "address"}, {"name": "maker", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lnImpliedRate", "type": "uint256", "internalType": "uint256"}, {"name": "failSafeRate", "type": "uint256", "internalType": "uint256"}, {"name": "permit", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}]}, {"name": "flashFills", "type": "tuple[]", "internalType": "struct IPRouter.FillOrderParams[]", "components": [{"name": "order", "type": "tuple", "internalType": "struct IPRouter.Order", "components": [{"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "orderType", "type": "uint8", "internalType": "enum IPRouter.OrderType"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "YT", "type": "address", "internalType": "address"}, {"name": "maker", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lnImpliedRate", "type": "uint256", "internalType": "uint256"}, {"name": "failSafeRate", "type": "uint256", "internalType": "uint256"}, {"name": "permit", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}]}, {"name": "optData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "netTokenOut", "type": "uint256", "internalType": "uint256"}, {"name": "netSyFee", "type": "uint256", "internalType": "uint256"}, {"name": "netSyInterm", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "swapExactTokenForPt", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "market", "type": "address", "internalType": "address"}, {"name": "minPtOut", "type": "uint256", "internalType": "uint256"}, {"name": "guessPtOut", "type": "tuple", "internalType": "struct IPRouter.ApproxParams", "components": [{"name": "guessMin", "type": "uint256", "internalType": "uint256"}, {"name": "guessMax", "type": "uint256", "internalType": "uint256"}, {"name": "guessOffchain", "type": "uint256", "internalType": "uint256"}, {"name": "maxIteration", "type": "uint256", "internalType": "uint256"}, {"name": "eps", "type": "uint256", "internalType": "uint256"}]}, {"name": "input", "type": "tuple", "internalType": "struct IPRouter.TokenInput", "components": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "netTokenIn", "type": "uint256", "internalType": "uint256"}, {"name": "tokenMintSy", "type": "address", "internalType": "address"}, {"name": "pendleSwap", "type": "address", "internalType": "address"}, {"name": "swapData", "type": "tuple", "internalType": "struct IPRouter.SwapData", "components": [{"name": "swapType", "type": "uint8", "internalType": "enum IPRouter.SwapType"}, {"name": "extRouter", "type": "address", "internalType": "address"}, {"name": "extCalldata", "type": "bytes", "internalType": "bytes"}, {"name": "needScale", "type": "bool", "internalType": "bool"}]}]}, {"name": "limit", "type": "tuple", "internalType": "struct IPRouter.LimitOrderData", "components": [{"name": "limitRouter", "type": "address", "internalType": "address"}, {"name": "epsSkipMarket", "type": "uint256", "internalType": "uint256"}, {"name": "normalFills", "type": "tuple[]", "internalType": "struct IPRouter.FillOrderParams[]", "components": [{"name": "order", "type": "tuple", "internalType": "struct IPRouter.Order", "components": [{"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "orderType", "type": "uint8", "internalType": "enum IPRouter.OrderType"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "YT", "type": "address", "internalType": "address"}, {"name": "maker", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lnImpliedRate", "type": "uint256", "internalType": "uint256"}, {"name": "failSafeRate", "type": "uint256", "internalType": "uint256"}, {"name": "permit", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}]}, {"name": "flashFills", "type": "tuple[]", "internalType": "struct IPRouter.FillOrderParams[]", "components": [{"name": "order", "type": "tuple", "internalType": "struct IPRouter.Order", "components": [{"name": "salt", "type": "uint256", "internalType": "uint256"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "orderType", "type": "uint8", "internalType": "enum IPRouter.OrderType"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "YT", "type": "address", "internalType": "address"}, {"name": "maker", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}, {"name": "lnImpliedRate", "type": "uint256", "internalType": "uint256"}, {"name": "failSafeRate", "type": "uint256", "internalType": "uint256"}, {"name": "permit", "type": "bytes", "internalType": "bytes"}]}, {"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "makingAmount", "type": "uint256", "internalType": "uint256"}]}, {"name": "optData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "netPtOut", "type": "uint256", "internalType": "uint256"}, {"name": "netSyFee", "type": "uint256", "internalType": "uint256"}, {"name": "netSyInterm", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"redeemPyToToken(address,address,uint256,(address,uint256,address,address,(uint8,address,bytes,bool)))": "47f1de22", "swapExactPtForToken(address,address,uint256,(address,uint256,address,address,(uint8,address,bytes,bool)),(address,uint256,((uint256,uint256,uint256,uint8,address,address,address,address,uint256,uint256,uint256,bytes),bytes,uint256)[],((uint256,uint256,uint256,uint8,address,address,address,address,uint256,uint256,uint256,bytes),bytes,uint256)[],bytes))": "594a88cc", "swapExactTokenForPt(address,address,uint256,(uint256,uint256,uint256,uint256,uint256),(address,uint256,address,address,(uint8,address,bytes,bool)),(address,uint256,((uint256,uint256,uint256,uint8,address,address,address,address,uint256,uint256,uint256,bytes),bytes,uint256)[],((uint256,uint256,uint256,uint8,address,address,address,address,uint256,uint256,uint256,bytes),bytes,uint256)[],bytes))": "c81f847a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"YT\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"netPyIn\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minTokenOut\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"tokenRedeemSy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pendleSwap\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"enum IPRouter.SwapType\",\"name\":\"swapType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"extRouter\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"extCalldata\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"needScale\",\"type\":\"bool\"}],\"internalType\":\"struct IPRouter.SwapData\",\"name\":\"swapData\",\"type\":\"tuple\"}],\"internalType\":\"struct IPRouter.TokenOutput\",\"name\":\"output\",\"type\":\"tuple\"}],\"name\":\"redeemPyToToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"netTokenOut\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"netSyInterm\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"exactPtIn\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minTokenOut\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"tokenRedeemSy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pendleSwap\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"enum IPRouter.SwapType\",\"name\":\"swapType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"extRouter\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"extCalldata\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"needScale\",\"type\":\"bool\"}],\"internalType\":\"struct IPRouter.SwapData\",\"name\":\"swapData\",\"type\":\"tuple\"}],\"internalType\":\"struct IPRouter.TokenOutput\",\"name\":\"output\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"limitRouter\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"epsSkipMarket\",\"type\":\"uint256\"},{\"components\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"salt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"enum IPRouter.OrderType\",\"name\":\"orderType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"YT\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"maker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lnImpliedRate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"failSafeRate\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"permit\",\"type\":\"bytes\"}],\"internalType\":\"struct IPRouter.Order\",\"name\":\"order\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct IPRouter.FillOrderParams[]\",\"name\":\"normalFills\",\"type\":\"tuple[]\"},{\"components\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"salt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"enum IPRouter.OrderType\",\"name\":\"orderType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"YT\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"maker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lnImpliedRate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"failSafeRate\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"permit\",\"type\":\"bytes\"}],\"internalType\":\"struct IPRouter.Order\",\"name\":\"order\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct IPRouter.FillOrderParams[]\",\"name\":\"flashFills\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"optData\",\"type\":\"bytes\"}],\"internalType\":\"struct IPRouter.LimitOrderData\",\"name\":\"limit\",\"type\":\"tuple\"}],\"name\":\"swapExactPtForToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"netTokenOut\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"netSyFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"netSyInterm\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"minPtOut\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"guessMin\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"guessMax\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"guessOffchain\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxIteration\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"eps\",\"type\":\"uint256\"}],\"internalType\":\"struct IPRouter.ApproxParams\",\"name\":\"guessPtOut\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"netTokenIn\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"tokenMintSy\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pendleSwap\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"enum IPRouter.SwapType\",\"name\":\"swapType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"extRouter\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"extCalldata\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"needScale\",\"type\":\"bool\"}],\"internalType\":\"struct IPRouter.SwapData\",\"name\":\"swapData\",\"type\":\"tuple\"}],\"internalType\":\"struct IPRouter.TokenInput\",\"name\":\"input\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"limitRouter\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"epsSkipMarket\",\"type\":\"uint256\"},{\"components\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"salt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"enum IPRouter.OrderType\",\"name\":\"orderType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"YT\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"maker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lnImpliedRate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"failSafeRate\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"permit\",\"type\":\"bytes\"}],\"internalType\":\"struct IPRouter.Order\",\"name\":\"order\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct IPRouter.FillOrderParams[]\",\"name\":\"normalFills\",\"type\":\"tuple[]\"},{\"components\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"salt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"enum IPRouter.OrderType\",\"name\":\"orderType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"YT\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"maker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lnImpliedRate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"failSafeRate\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"permit\",\"type\":\"bytes\"}],\"internalType\":\"struct IPRouter.Order\",\"name\":\"order\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"makingAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct IPRouter.FillOrderParams[]\",\"name\":\"flashFills\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"optData\",\"type\":\"bytes\"}],\"internalType\":\"struct IPRouter.LimitOrderData\",\"name\":\"limit\",\"type\":\"tuple\"}],\"name\":\"swapExactTokenForPt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"netPtOut\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"netSyFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"netSyInterm\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IPendle.sol\":\"IPRouter\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"src/interfaces/IPendle.sol\":{\"keccak256\":\"0x272d09ba61f8bf889cf5030f6a06081baa0e997b5290f45d0e1d99497ebe7775\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://acb69ed34ec090f074a9888d25a180fd5a0de3d9e497b93d4500be606f4a5774\",\"dweb:/ipfs/QmafGeJ65HAmWFsFGihnSaKQFjUjd5JocssKyiVmCbwEmT\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "YT", "type": "address"}, {"internalType": "uint256", "name": "netPyIn", "type": "uint256"}, {"internalType": "struct IPRouter.TokenOutput", "name": "output", "type": "tuple", "components": [{"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint256", "name": "minTokenOut", "type": "uint256"}, {"internalType": "address", "name": "tokenRedeemSy", "type": "address"}, {"internalType": "address", "name": "pendleSwap", "type": "address"}, {"internalType": "struct IPRouter.SwapData", "name": "swapData", "type": "tuple", "components": [{"internalType": "enum IPRouter.SwapType", "name": "swapType", "type": "uint8"}, {"internalType": "address", "name": "extRouter", "type": "address"}, {"internalType": "bytes", "name": "extCalldata", "type": "bytes"}, {"internalType": "bool", "name": "needScale", "type": "bool"}]}]}], "stateMutability": "nonpayable", "type": "function", "name": "redeemPyToToken", "outputs": [{"internalType": "uint256", "name": "netTokenOut", "type": "uint256"}, {"internalType": "uint256", "name": "netSyInterm", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "exactPtIn", "type": "uint256"}, {"internalType": "struct IPRouter.TokenOutput", "name": "output", "type": "tuple", "components": [{"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint256", "name": "minTokenOut", "type": "uint256"}, {"internalType": "address", "name": "tokenRedeemSy", "type": "address"}, {"internalType": "address", "name": "pendleSwap", "type": "address"}, {"internalType": "struct IPRouter.SwapData", "name": "swapData", "type": "tuple", "components": [{"internalType": "enum IPRouter.SwapType", "name": "swapType", "type": "uint8"}, {"internalType": "address", "name": "extRouter", "type": "address"}, {"internalType": "bytes", "name": "extCalldata", "type": "bytes"}, {"internalType": "bool", "name": "needScale", "type": "bool"}]}]}, {"internalType": "struct IPRouter.LimitOrderData", "name": "limit", "type": "tuple", "components": [{"internalType": "address", "name": "limitRouter", "type": "address"}, {"internalType": "uint256", "name": "epsSkipMarket", "type": "uint256"}, {"internalType": "struct IPRouter.FillOrderParams[]", "name": "normalFills", "type": "tuple[]", "components": [{"internalType": "struct IPRouter.Order", "name": "order", "type": "tuple", "components": [{"internalType": "uint256", "name": "salt", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "enum IPRouter.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "YT", "type": "address"}, {"internalType": "address", "name": "maker", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "lnImpliedRate", "type": "uint256"}, {"internalType": "uint256", "name": "failSafeRate", "type": "uint256"}, {"internalType": "bytes", "name": "permit", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}]}, {"internalType": "struct IPRouter.FillOrderParams[]", "name": "flashFills", "type": "tuple[]", "components": [{"internalType": "struct IPRouter.Order", "name": "order", "type": "tuple", "components": [{"internalType": "uint256", "name": "salt", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "enum IPRouter.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "YT", "type": "address"}, {"internalType": "address", "name": "maker", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "lnImpliedRate", "type": "uint256"}, {"internalType": "uint256", "name": "failSafeRate", "type": "uint256"}, {"internalType": "bytes", "name": "permit", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}]}, {"internalType": "bytes", "name": "optData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "swapExactPtForToken", "outputs": [{"internalType": "uint256", "name": "netTokenOut", "type": "uint256"}, {"internalType": "uint256", "name": "netSyFee", "type": "uint256"}, {"internalType": "uint256", "name": "netSyInterm", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint256", "name": "minPtOut", "type": "uint256"}, {"internalType": "struct IPRouter.ApproxParams", "name": "guessPtOut", "type": "tuple", "components": [{"internalType": "uint256", "name": "guessMin", "type": "uint256"}, {"internalType": "uint256", "name": "guessMax", "type": "uint256"}, {"internalType": "uint256", "name": "guessOffchain", "type": "uint256"}, {"internalType": "uint256", "name": "maxIteration", "type": "uint256"}, {"internalType": "uint256", "name": "eps", "type": "uint256"}]}, {"internalType": "struct IPRouter.TokenInput", "name": "input", "type": "tuple", "components": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "netTokenIn", "type": "uint256"}, {"internalType": "address", "name": "tokenMintSy", "type": "address"}, {"internalType": "address", "name": "pendleSwap", "type": "address"}, {"internalType": "struct IPRouter.SwapData", "name": "swapData", "type": "tuple", "components": [{"internalType": "enum IPRouter.SwapType", "name": "swapType", "type": "uint8"}, {"internalType": "address", "name": "extRouter", "type": "address"}, {"internalType": "bytes", "name": "extCalldata", "type": "bytes"}, {"internalType": "bool", "name": "needScale", "type": "bool"}]}]}, {"internalType": "struct IPRouter.LimitOrderData", "name": "limit", "type": "tuple", "components": [{"internalType": "address", "name": "limitRouter", "type": "address"}, {"internalType": "uint256", "name": "epsSkipMarket", "type": "uint256"}, {"internalType": "struct IPRouter.FillOrderParams[]", "name": "normalFills", "type": "tuple[]", "components": [{"internalType": "struct IPRouter.Order", "name": "order", "type": "tuple", "components": [{"internalType": "uint256", "name": "salt", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "enum IPRouter.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "YT", "type": "address"}, {"internalType": "address", "name": "maker", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "lnImpliedRate", "type": "uint256"}, {"internalType": "uint256", "name": "failSafeRate", "type": "uint256"}, {"internalType": "bytes", "name": "permit", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}]}, {"internalType": "struct IPRouter.FillOrderParams[]", "name": "flashFills", "type": "tuple[]", "components": [{"internalType": "struct IPRouter.Order", "name": "order", "type": "tuple", "components": [{"internalType": "uint256", "name": "salt", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "enum IPRouter.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "YT", "type": "address"}, {"internalType": "address", "name": "maker", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "lnImpliedRate", "type": "uint256"}, {"internalType": "uint256", "name": "failSafeRate", "type": "uint256"}, {"internalType": "bytes", "name": "permit", "type": "bytes"}]}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "makingAmount", "type": "uint256"}]}, {"internalType": "bytes", "name": "optData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "swapExactTokenForPt", "outputs": [{"internalType": "uint256", "name": "netPtOut", "type": "uint256"}, {"internalType": "uint256", "name": "netSyFee", "type": "uint256"}, {"internalType": "uint256", "name": "netSyInterm", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/IPendle.sol": "IPRouter"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "src/interfaces/IPendle.sol": {"keccak256": "0x272d09ba61f8bf889cf5030f6a06081baa0e997b5290f45d0e1d99497ebe7775", "urls": ["bzz-raw://acb69ed34ec090f074a9888d25a180fd5a0de3d9e497b93d4500be606f4a5774", "dweb:/ipfs/QmafGeJ65HAmWFsFGihnSaKQFjUjd5JocssKyiVmCbwEmT"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 68}