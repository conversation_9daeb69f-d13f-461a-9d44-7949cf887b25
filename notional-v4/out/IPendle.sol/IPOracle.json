{"abi": [{"type": "function", "name": "getOracleState", "inputs": [{"name": "market", "type": "address", "internalType": "address"}, {"name": "duration", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "increaseCardinalityRequired", "type": "bool", "internalType": "bool"}, {"name": "cardinalityRequired", "type": "uint16", "internalType": "uint16"}, {"name": "oldestObservationSatisfied", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getPtToAssetRate", "inputs": [{"name": "market", "type": "address", "internalType": "address"}, {"name": "duration", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getPtToSyRate", "inputs": [{"name": "market", "type": "address", "internalType": "address"}, {"name": "duration", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"getOracleState(address,uint32)": "873e9600", "getPtToAssetRate(address,uint32)": "abca0eab", "getPtToSyRate(address,uint32)": "a31426d1"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"duration\",\"type\":\"uint32\"}],\"name\":\"getOracleState\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"increaseCardinalityRequired\",\"type\":\"bool\"},{\"internalType\":\"uint16\",\"name\":\"cardinalityRequired\",\"type\":\"uint16\"},{\"internalType\":\"bool\",\"name\":\"oldestObservationSatisfied\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"duration\",\"type\":\"uint32\"}],\"name\":\"getPtToAssetRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"market\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"duration\",\"type\":\"uint32\"}],\"name\":\"getPtToSyRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IPendle.sol\":\"IPOracle\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"src/interfaces/IPendle.sol\":{\"keccak256\":\"0x272d09ba61f8bf889cf5030f6a06081baa0e997b5290f45d0e1d99497ebe7775\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://acb69ed34ec090f074a9888d25a180fd5a0de3d9e497b93d4500be606f4a5774\",\"dweb:/ipfs/QmafGeJ65HAmWFsFGihnSaKQFjUjd5JocssKyiVmCbwEmT\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getOracleState", "outputs": [{"internalType": "bool", "name": "increaseCardinalityRequired", "type": "bool"}, {"internalType": "uint16", "name": "cardinalityRequired", "type": "uint16"}, {"internalType": "bool", "name": "oldestObservationSatisfied", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getPtToAssetRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "market", "type": "address"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getPtToSyRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/IPendle.sol": "IPOracle"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "src/interfaces/IPendle.sol": {"keccak256": "0x272d09ba61f8bf889cf5030f6a06081baa0e997b5290f45d0e1d99497ebe7775", "urls": ["bzz-raw://acb69ed34ec090f074a9888d25a180fd5a0de3d9e497b93d4500be606f4a5774", "dweb:/ipfs/QmafGeJ65HAmWFsFGihnSaKQFjUjd5JocssKyiVmCbwEmT"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 68}