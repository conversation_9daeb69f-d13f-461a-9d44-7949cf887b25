{"abi": [{"type": "function", "name": "acceptPauseAdmin", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptUpgradeOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "clearPosition", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "feeReceiver", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getVaultPosition", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct VaultPosition", "components": [{"name": "lendingRouter", "type": "address", "internalType": "address"}, {"name": "lastEntryTime", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "getWithdrawRequestManager", "inputs": [{"name": "yieldToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IWithdrawRequestManager"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isLendingRouter", "inputs": [{"name": "lendingRouter", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lendingRouters", "inputs": [{"name": "lendingRouter", "type": "address", "internalType": "address"}], "outputs": [{"name": "isLendingRouter", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pauseAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pendingPauseAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pendingUpgradeAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setL<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "lendingRouter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPosition", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setWithdrawRequestManager", "inputs": [{"name": "withdrawRequestManager", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFeeReceiver", "inputs": [{"name": "_newFeeReceiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferPauseAdmin", "inputs": [{"name": "_newPauseAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferUpgradeAdmin", "inputs": [{"name": "_newUpgradeAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "whitelistedVaults", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "withdrawRequestManagers", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "withdrawRequestManager", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "AccountPositionCleared", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "lendingRouter", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AccountPositionCreated", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "lendingRouter", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "FeeReceiverTransferred", "inputs": [{"name": "newFeeReceiver", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "LendingRouterSet", "inputs": [{"name": "lendingRouter", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PauseAdminTransferred", "inputs": [{"name": "newPauseAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PendingPauseAdminSet", "inputs": [{"name": "newPendingPauseAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PendingUpgradeAdminSet", "inputs": [{"name": "newPendingUpgradeAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "UpgradeAdminTransferred", "inputs": [{"name": "newUpgradeAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "WhitelistedVault", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "WithdrawRequestManagerSet", "inputs": [{"name": "yieldToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "withdrawRequestManager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "CannotEnterPosition", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "Invalid<PERSON>ault", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "434:6557:82:-:0;;;;;;;;;;;;-1:-1:-1;213:11:83;:18;;-1:-1:-1;;213:18:83;227:4;213:18;;;434:6557:82;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "434:6557:82:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2169:87;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;601:42:121;589:55;;;571:74;;559:2;544:18;2169:87:82;;;;;;;;6678:311;;;;;;:::i;:::-;;:::i;:::-;;1961:32;;;;;;;;;3629:180;;;;;;:::i;:::-;;:::i;5686:159::-;;;;;;:::i;:::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;5806:25:82;;;;;;:16;:25;;;;;:32;;;;;;;;;;;;5799:39;;;;;;;;;;;;;;;;;;;;;;;;;5686:159;;;;;1281:13:121;;1296:42;1277:62;1259:81;;1400:4;1388:17;;;1382:24;1408:10;1378:41;1356:20;;;1349:71;;;;1232:18;5686:159:82;1049:377:121;2348:76:82;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1596:14:121;;1589:22;1571:41;;1559:2;1544:18;2348:76:82;1431:187:121;1930:25:82;;;;;;;;;4251:714;;;;;;:::i;:::-;;:::i;244:169:83:-;;;;;;:::i;:::-;;:::i;3361:262:82:-;;;:::i;5550:130::-;;;;;;:::i;:::-;5644:29;;5621:4;5644:29;;;:14;:29;;;;;;;;;5550:130;3815:244;;;:::i;2477:69::-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;5851:821;;;;;;:::i;:::-;;:::i;5177:187::-;;;;;;:::i;:::-;5321:35;;;;5255:23;5321:35;;;:23;:35;;;;;;;;5177:187;2071:26;;;;;;;;;5370:174;;;;;;:::i;:::-;;:::i;1728:27::-;;;;;;;;;;;;4971:200;;;;;;:::i;:::-;;:::i;4065:180::-;;;;;;:::i;:::-;;:::i;1761:34::-;;;;;;;;;3163:192;;;;;;:::i;:::-;;:::i;6678:311::-;6823:10;6808:26;;;;:14;:26;;;;;;;;6803:64;;6843:24;;;;;6856:10;6843:24;;;571:74:121;544:18;;6843:24:82;;;;;;;;6803:64;6885:25;;;;;;;;:16;:25;;;;;;;;:32;;;;;;;;;;;;;6878:39;;;;;;6932:50;6971:10;;6885:32;:25;6932:50;;;6678:311;;:::o;3629:180::-;3094:12;;;;;;;3080:10;:26;3076:63;;3115:24;;;;;3128:10;3115:24;;;571:74:121;544:18;;3115:24:82;425:226:121;3076:63:82;3717:17:::1;:34:::0;;;::::1;;::::0;::::1;::::0;;::::1;::::0;;;3766:36:::1;::::0;::::1;::::0;-1:-1:-1;;3766:36:82::1;3629:180:::0;:::o;4251:714::-;3094:12;;;;;;;3080:10;:26;3076:63;;3115:24;;;;;3128:10;3115:24;;;571:74:121;544:18;;3115:24:82;425:226:121;3076:63:82;4354:18:::1;4399:22;4375:59;;;:61;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4721:49;:35:::0;;::::1;4768:1;4721:35:::0;;;:23:::1;:35;::::0;;;;;4354:82;;-1:-1:-1;4721:35:82::1;:49:::0;4712:99:::1;;;::::0;::::1;::::0;;3357:2:121;4712:99:82::1;::::0;::::1;3339:21:121::0;3396:2;3376:18;;;3369:30;3435:34;3415:18;;;3408:62;3506:6;3486:18;;;3479:34;3530:19;;4712:99:82::1;3155:400:121::0;4712:99:82::1;4822:35;::::0;;::::1;;::::0;;;:23:::1;:35;::::0;;;;;:60;;;::::1;::::0;;::::1;::::0;;::::1;::::0;;4897:61;::::1;::::0;4822:35;4897:61:::1;4344:621;4251:714:::0;:::o;244:169:83:-;308:11;;;;304:47;;;328:23;;;;;;;;;;;;;;304:47;361:11;:18;;;;375:4;361:18;;;389:17;401:4;;389:11;:17::i;:::-;244:169;;:::o;3361:262:82:-;3432:19;;;;3418:10;:33;3414:70;;3460:24;;;;;3473:10;3460:24;;;571:74:121;544:18;;3460:24:82;425:226:121;3414:70:82;3509:19;;;;3494:34;;3509:19;;;;;3494:34;;;;;;;;;;;;;;3538:26;;;;;;;3579:37;;3603:12;;;;;;3579:37;;;3361:262::o;3815:244::-;3880:17;;;;3866:10;:31;3862:68;;3906:24;;;;;3919:10;3906:24;;;571:74:121;544:18;;3906:24:82;425:226:121;3862:68:82;3953:17;;;3940:10;:30;;3953:17;;;3940:30;;;;;;;;;3980:24;;;;;;4019:33;;;;-1:-1:-1;;4019:33:82;3815:244::o;5851:821::-;5994:10;5979:26;;;;:14;:26;;;;;;;;5974:64;;6014:24;;;;;6027:10;6014:24;;;571:74:121;544:18;;6014:24:82;425:226:121;5974:64:82;6081:25;;;;6048:30;6081:25;;;:16;:25;;;;;;;;:32;;;;;;;;;;6128:22;;6081:32;;6128:22;6124:162;;6166:35;;;;6191:10;6166:35;;;6124:162;;;6220:22;;:36;:22;6246:10;6220:36;6216:70;;6265:21;;;;;;;;;;;;;;6216:70;6489:24;;;;;;;:17;:24;;;;;;;;6484:57;;6522:19;;;;;601:42:121;589:55;;6522:19:82;;;571:74:121;544:18;;6522:19:82;425:226:121;6484:57:82;6552:48;;;;;6584:15;6552:48;;;;;;6615:50;;6654:10;;6615:50;;;;;;;;;;;-1:-1:-1;;6615:50:82;5913:759;5851:821;;:::o;5370:174::-;3094:12;;;;;;;3080:10;:26;3076:63;;3115:24;;;;;3128:10;3115:24;;;571:74:121;544:18;;3115:24:82;425:226:121;3076:63:82;5455:29:::1;::::0;::::1;;::::0;;;:14:::1;:29;::::0;;;;;:36;;;::::1;5487:4;5455:36;::::0;;5506:31;::::1;::::0;5455:29;5506:31:::1;5370:174:::0;:::o;4971:200::-;3094:12;;;;;;;3080:10;:26;3076:63;;3115:24;;;;;3128:10;3115:24;;;571:74:121;544:18;;3115:24:82;425:226:121;3076:63:82;5071:24:::1;::::0;::::1;;::::0;;;:17:::1;:24;::::0;;;;;;;;:40;;;::::1;::::0;::::1;;::::0;;::::1;::::0;;;5126:38;;1571:41:121;;;5126:38:82::1;::::0;1544:18:121;5126:38:82::1;;;;;;;4971:200:::0;;:::o;4065:180::-;3094:12;;;;;;;3080:10;:26;3076:63;;3115:24;;;;;3128:10;3115:24;;;571:74:121;544:18;;3115:24:82;425:226:121;3076:63:82;4155:11:::1;:29:::0;;;::::1;;::::0;::::1;::::0;;::::1;::::0;;;4199:39:::1;::::0;::::1;::::0;-1:-1:-1;;4199:39:82::1;4065:180:::0;:::o;3163:192::-;3094:12;;;;;;;3080:10;:26;3076:63;;3115:24;;;;;3128:10;3115:24;;;571:74:121;544:18;;3115:24:82;425:226:121;3076:63:82;3255:19:::1;:38:::0;;;::::1;;::::0;::::1;::::0;;::::1;::::0;;;3308:40:::1;::::0;::::1;::::0;-1:-1:-1;;3308:40:82::1;3163:192:::0;:::o;2733:299::-;2804:21;;;2872:45;;;;2883:4;2872:45;:::i;:::-;2927:12;:28;;;;;;;;;;;;;;;2965:10;:24;;;;;;;;;;;;2999:11;:26;;;;;;;;;;;-1:-1:-1;;;;;2733:299:82:o;14:154:121:-;100:42;93:5;89:54;82:5;79:65;69:93;;158:1;155;148:12;69:93;14:154;:::o;173:247::-;232:6;285:2;273:9;264:7;260:23;256:32;253:52;;;301:1;298;291:12;253:52;340:9;327:23;359:31;384:5;359:31;:::i;:::-;409:5;173:247;-1:-1:-1;;;173:247:121:o;656:388::-;724:6;732;785:2;773:9;764:7;760:23;756:32;753:52;;;801:1;798;791:12;753:52;840:9;827:23;859:31;884:5;859:31;:::i;:::-;909:5;-1:-1:-1;966:2:121;951:18;;938:32;979:33;938:32;979:33;:::i;:::-;1031:7;1021:17;;;656:388;;;;;:::o;1623:586::-;1693:6;1701;1754:2;1742:9;1733:7;1729:23;1725:32;1722:52;;;1770:1;1767;1760:12;1722:52;1810:9;1797:23;1843:18;1835:6;1832:30;1829:50;;;1875:1;1872;1865:12;1829:50;1898:22;;1951:4;1943:13;;1939:27;-1:-1:-1;1929:55:121;;1980:1;1977;1970:12;1929:55;2020:2;2007:16;2046:18;2038:6;2035:30;2032:50;;;2078:1;2075;2068:12;2032:50;2123:7;2118:2;2109:6;2105:2;2101:15;2097:24;2094:37;2091:57;;;2144:1;2141;2134:12;2091:57;2175:2;2167:11;;;;;2197:6;;-1:-1:-1;1623:586:121;-1:-1:-1;;;1623:586:121:o;2478:416::-;2543:6;2551;2604:2;2592:9;2583:7;2579:23;2575:32;2572:52;;;2620:1;2617;2610:12;2572:52;2659:9;2646:23;2678:31;2703:5;2678:31;:::i;:::-;2728:5;-1:-1:-1;2785:2:121;2770:18;;2757:32;2827:15;;2820:23;2808:36;;2798:64;;2858:1;2855;2848:12;2899:251;2969:6;3022:2;3010:9;3001:7;2997:23;2993:32;2990:52;;;3038:1;3035;3028:12;2990:52;3070:9;3064:16;3089:31;3114:5;3089:31;:::i;3560:553::-;3661:6;3669;3677;3730:2;3718:9;3709:7;3705:23;3701:32;3698:52;;;3746:1;3743;3736:12;3698:52;3785:9;3772:23;3804:31;3829:5;3804:31;:::i;:::-;3854:5;-1:-1:-1;3911:2:121;3896:18;;3883:32;3924:33;3883:32;3924:33;:::i;:::-;3976:7;-1:-1:-1;4035:2:121;4020:18;;4007:32;4048:33;4007:32;4048:33;:::i;:::-;4100:7;4090:17;;;3560:553;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"acceptPauseAdmin()": "53352848", "acceptUpgradeOwnership()": "44f793a4", "clearPosition(address,address)": "0ffd849b", "feeReceiver()": "b3f00674", "getVaultPosition(address,address)": "268b4bae", "getWithdrawRequestManager(address)": "9287d7ac", "initialize(bytes)": "439fab91", "isLendingRouter(address)": "46f74dce", "lendingRouters(address)": "2b0fdbe4", "pauseAdmin()": "2f11d653", "pendingPauseAdmin()": "1404297c", "pendingUpgradeAdmin()": "de6750a2", "setLendingRouter(address)": "c0e18777", "setPosition(address,address)": "7e124598", "setWhitelistedVault(address,bool)": "c599a317", "setWithdrawRequestManager(address)": "42d90a85", "transferFeeReceiver(address)": "d23076a9", "transferPauseAdmin(address)": "24a23526", "transferUpgradeAdmin(address)": "e3fdddbf", "upgradeAdmin()": "c4d5608a", "whitelistedVaults(address)": "574b7675", "withdrawRequestManagers(address)": "0e3cf192"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"CannotEnterPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"InvalidVault\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"}],\"name\":\"AccountPositionCleared\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"}],\"name\":\"AccountPositionCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newFeeReceiver\",\"type\":\"address\"}],\"name\":\"FeeReceiverTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"}],\"name\":\"LendingRouterSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPauseAdmin\",\"type\":\"address\"}],\"name\":\"PauseAdminTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPendingPauseAdmin\",\"type\":\"address\"}],\"name\":\"PendingPauseAdminSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPendingUpgradeAdmin\",\"type\":\"address\"}],\"name\":\"PendingUpgradeAdminSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newUpgradeAdmin\",\"type\":\"address\"}],\"name\":\"UpgradeAdminTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isWhitelisted\",\"type\":\"bool\"}],\"name\":\"WhitelistedVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"yieldToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"withdrawRequestManager\",\"type\":\"address\"}],\"name\":\"WithdrawRequestManagerSet\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptPauseAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"acceptUpgradeOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"clearPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeReceiver\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"getVaultPosition\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastEntryTime\",\"type\":\"uint32\"}],\"internalType\":\"struct VaultPosition\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"yieldToken\",\"type\":\"address\"}],\"name\":\"getWithdrawRequestManager\",\"outputs\":[{\"internalType\":\"contract IWithdrawRequestManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"}],\"name\":\"isLendingRouter\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"}],\"name\":\"lendingRouters\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isLendingRouter\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pauseAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingPauseAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingUpgradeAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"lendingRouter\",\"type\":\"address\"}],\"name\":\"setLendingRouter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"setPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isWhitelisted\",\"type\":\"bool\"}],\"name\":\"setWhitelistedVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"withdrawRequestManager\",\"type\":\"address\"}],\"name\":\"setWithdrawRequestManager\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newFeeReceiver\",\"type\":\"address\"}],\"name\":\"transferFeeReceiver\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newPauseAdmin\",\"type\":\"address\"}],\"name\":\"transferPauseAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newUpgradeAdmin\",\"type\":\"address\"}],\"name\":\"transferUpgradeAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"upgradeAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"whitelistedVaults\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isWhitelisted\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"withdrawRequestManagers\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"withdrawRequestManager\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"feeReceiver()\":{\"notice\":\"Address of the account that receives the protocol fees\"},\"lendingRouters(address)\":{\"notice\":\"Mapping of lending router to boolean indicating if it is whitelisted\"},\"pauseAdmin()\":{\"notice\":\"Address of the admin that is allowed to selectively pause or unpause TimelockUpgradeableProxy contracts\"},\"upgradeAdmin()\":{\"notice\":\"Address of the admin that is allowed to: - Upgrade TimelockUpgradeableProxy contracts given a 7 day timelock - Transfer the upgrade admin role - Set the pause admin - Set the fee receiver - Add reward tokens to the RewardManager - Set the WithdrawRequestManager for a yield token - Whitelist vaults for the WithdrawRequestManager - Whitelist new lending routers\"},\"whitelistedVaults(address)\":{\"notice\":\"Mapping to whitelisted vaults\"},\"withdrawRequestManagers(address)\":{\"notice\":\"Mapping of yield token to WithdrawRequestManager\"}},\"notice\":\"Registry for the addresses for different components of the protocol.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/proxy/AddressRegistry.sol\":\"AddressRegistry\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "CannotEnterPosition"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "type": "error", "name": "Invalid<PERSON>ault"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "address", "name": "lendingRouter", "type": "address", "indexed": true}], "type": "event", "name": "AccountPositionCleared", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "address", "name": "lendingRouter", "type": "address", "indexed": true}], "type": "event", "name": "AccountPositionCreated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newFeeReceiver", "type": "address", "indexed": true}], "type": "event", "name": "FeeReceiverTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "lendingRouter", "type": "address", "indexed": true}], "type": "event", "name": "LendingRouterSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newPauseAdmin", "type": "address", "indexed": true}], "type": "event", "name": "PauseAdminTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newPendingPauseAdmin", "type": "address", "indexed": true}], "type": "event", "name": "PendingPauseAdminSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newPendingUpgradeAdmin", "type": "address", "indexed": true}], "type": "event", "name": "PendingUpgradeAdminSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newUpgradeAdmin", "type": "address", "indexed": true}], "type": "event", "name": "UpgradeAdminTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "indexed": false}], "type": "event", "name": "WhitelistedVault", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "yieldToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "withdrawRequestManager", "type": "address", "indexed": true}], "type": "event", "name": "WithdrawRequestManagerSet", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptPauseAdmin"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptUpgradeOwnership"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "clearPosition"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getVaultPosition", "outputs": [{"internalType": "struct VaultPosition", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "lendingRouter", "type": "address"}, {"internalType": "uint32", "name": "lastEntryTime", "type": "uint32"}]}]}, {"inputs": [{"internalType": "address", "name": "yieldToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getWithdrawRequestManager", "outputs": [{"internalType": "contract IWithdrawRequestManager", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "lendingRouter", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isLendingRouter", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "lendingRouter", "type": "address"}], "stateMutability": "view", "type": "function", "name": "lendingRouters", "outputs": [{"internalType": "bool", "name": "isLendingRouter", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pauseAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingPauseAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingUpgradeAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "lendingRouter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setL<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPosition"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "withdrawRequestManager", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setWithdrawRequestManager"}, {"inputs": [{"internalType": "address", "name": "_newFeeReceiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFeeReceiver"}, {"inputs": [{"internalType": "address", "name": "_newPauseAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferPauseAdmin"}, {"inputs": [{"internalType": "address", "name": "_newUpgradeAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferUpgradeAdmin"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "upgradeAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "whitelistedVaults", "outputs": [{"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "withdrawRequestManagers", "outputs": [{"internalType": "address", "name": "withdrawRequestManager", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"feeReceiver()": {"notice": "Address of the account that receives the protocol fees"}, "lendingRouters(address)": {"notice": "Mapping of lending router to boolean indicating if it is whitelisted"}, "pauseAdmin()": {"notice": "Address of the admin that is allowed to selectively pause or unpause TimelockUpgradeableProxy contracts"}, "upgradeAdmin()": {"notice": "Address of the admin that is allowed to: - Upgrade TimelockUpgradeableProxy contracts given a 7 day timelock - Transfer the upgrade admin role - Set the pause admin - Set the fee receiver - Add reward tokens to the RewardManager - Set the WithdrawRequestManager for a yield token - Whitelist vaults for the WithdrawRequestManager - Whitelist new lending routers"}, "whitelistedVaults(address)": {"notice": "Mapping to whitelisted vaults"}, "withdrawRequestManagers(address)": {"notice": "Mapping of yield token to WithdrawRequestManager"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/proxy/AddressRegistry.sol": "AddressRegistry"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}}, "version": 1}, "id": 82}