{"abi": [{"type": "function", "name": "claim_rewards", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "_value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "_value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"claim_rewards()": "e6f1daf2", "deposit(uint256)": "b6b55f25", "withdraw(uint256)": "2e1a7d4d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"claim_rewards\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Curve/ICurve.sol\":\"ICurveGauge\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Curve/ICurve.sol\":{\"keccak256\":\"0xc9c5ab52d4e6c8a541177552533a0629cec42a5eaf37edba117dc7cbb064f826\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://75ef6d199300bb6b1751462b9b5921a4f6a4d9a5cbd993efde47f0e92b32699c\",\"dweb:/ipfs/Qmcr5yQoR3vwfoupjoqKK8C55YmX2nFV8ee6XUuuHtPt65\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "claim_rewards"}, {"inputs": [{"internalType": "uint256", "name": "_value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [{"internalType": "uint256", "name": "_value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Curve/ICurve.sol": "ICurveGauge"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Curve/ICurve.sol": {"keccak256": "0xc9c5ab52d4e6c8a541177552533a0629cec42a5eaf37edba117dc7cbb064f826", "urls": ["bzz-raw://75ef6d199300bb6b1751462b9b5921a4f6a4d9a5cbd993efde47f0e92b32699c", "dweb:/ipfs/Qmcr5yQoR3vwfoupjoqKK8C55YmX2nFV8ee6XUuuHtPt65"], "license": "MIT"}}, "version": 1}, "id": 60}