{"abi": [{"type": "function", "name": "add_liquidity", "inputs": [{"name": "amounts", "type": "uint256[2]", "internalType": "uint256[2]"}, {"name": "min_mint_amount", "type": "uint256", "internalType": "uint256"}, {"name": "use_eth", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "balances", "inputs": [{"name": "i", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "coins", "inputs": [{"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "exchange", "inputs": [{"name": "i", "type": "int128", "internalType": "int128"}, {"name": "j", "type": "int128", "internalType": "int128"}, {"name": "dx", "type": "uint256", "internalType": "uint256"}, {"name": "minDy", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "get_dy", "inputs": [{"name": "i", "type": "int128", "internalType": "int128"}, {"name": "j", "type": "int128", "internalType": "int128"}, {"name": "dx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "remove_liquidity", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "_min_amounts", "type": "uint256[2]", "internalType": "uint256[2]"}, {"name": "use_eth", "type": "bool", "internalType": "bool"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "remove_liquidity_one_coin", "inputs": [{"name": "token_amount", "type": "uint256", "internalType": "uint256"}, {"name": "i", "type": "uint256", "internalType": "uint256"}, {"name": "min_amount", "type": "uint256", "internalType": "uint256"}, {"name": "use_eth", "type": "bool", "internalType": "bool"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "token", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"add_liquidity(uint256[2],uint256,bool)": "ee22be23", "balances(uint256)": "4903b0d1", "coins(uint256)": "c6610657", "exchange(int128,int128,uint256,uint256)": "3df02124", "get_dy(int128,int128,uint256)": "5e0d443f", "remove_liquidity(uint256,uint256[2],bool,address)": "1808e84a", "remove_liquidity_one_coin(uint256,uint256,uint256,bool,address)": "07329bcd", "token()": "fc0c546a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256[2]\",\"name\":\"amounts\",\"type\":\"uint256[2]\"},{\"internalType\":\"uint256\",\"name\":\"min_mint_amount\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"use_eth\",\"type\":\"bool\"}],\"name\":\"add_liquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"i\",\"type\":\"uint256\"}],\"name\":\"balances\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"idx\",\"type\":\"uint256\"}],\"name\":\"coins\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int128\",\"name\":\"i\",\"type\":\"int128\"},{\"internalType\":\"int128\",\"name\":\"j\",\"type\":\"int128\"},{\"internalType\":\"uint256\",\"name\":\"dx\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minDy\",\"type\":\"uint256\"}],\"name\":\"exchange\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int128\",\"name\":\"i\",\"type\":\"int128\"},{\"internalType\":\"int128\",\"name\":\"j\",\"type\":\"int128\"},{\"internalType\":\"uint256\",\"name\":\"dx\",\"type\":\"uint256\"}],\"name\":\"get_dy\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256[2]\",\"name\":\"_min_amounts\",\"type\":\"uint256[2]\"},{\"internalType\":\"bool\",\"name\":\"use_eth\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"remove_liquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"token_amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"i\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"min_amount\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"use_eth\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"remove_liquidity_one_coin\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Curve/ICurve.sol\":\"ICurve2TokenPoolV2\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Curve/ICurve.sol\":{\"keccak256\":\"0xc9c5ab52d4e6c8a541177552533a0629cec42a5eaf37edba117dc7cbb064f826\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://75ef6d199300bb6b1751462b9b5921a4f6a4d9a5cbd993efde47f0e92b32699c\",\"dweb:/ipfs/Qmcr5yQoR3vwfoupjoqKK8C55YmX2nFV8ee6XUuuHtPt65\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256[2]", "name": "amounts", "type": "uint256[2]"}, {"internalType": "uint256", "name": "min_mint_amount", "type": "uint256"}, {"internalType": "bool", "name": "use_eth", "type": "bool"}], "stateMutability": "payable", "type": "function", "name": "add_liquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "i", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "balances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "idx", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "coins", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "int128", "name": "i", "type": "int128"}, {"internalType": "int128", "name": "j", "type": "int128"}, {"internalType": "uint256", "name": "dx", "type": "uint256"}, {"internalType": "uint256", "name": "minDy", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "exchange", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "int128", "name": "i", "type": "int128"}, {"internalType": "int128", "name": "j", "type": "int128"}, {"internalType": "uint256", "name": "dx", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "get_dy", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256[2]", "name": "_min_amounts", "type": "uint256[2]"}, {"internalType": "bool", "name": "use_eth", "type": "bool"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "remove_liquidity"}, {"inputs": [{"internalType": "uint256", "name": "token_amount", "type": "uint256"}, {"internalType": "uint256", "name": "i", "type": "uint256"}, {"internalType": "uint256", "name": "min_amount", "type": "uint256"}, {"internalType": "bool", "name": "use_eth", "type": "bool"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "remove_liquidity_one_coin", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Curve/ICurve.sol": "ICurve2TokenPoolV2"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Curve/ICurve.sol": {"keccak256": "0xc9c5ab52d4e6c8a541177552533a0629cec42a5eaf37edba117dc7cbb064f826", "urls": ["bzz-raw://75ef6d199300bb6b1751462b9b5921a4f6a4d9a5cbd993efde47f0e92b32699c", "dweb:/ipfs/Qmcr5yQoR3vwfoupjoqKK8C55YmX2nFV8ee6XUuuHtPt65"], "license": "MIT"}}, "version": 1}, "id": 60}