{"abi": [{"type": "function", "name": "claimRewardTokens", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getRewardDebt", "inputs": [{"name": "rewardToken", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRewardSettings", "inputs": [], "outputs": [{"name": "rewardStates", "type": "tuple[]", "internalType": "struct VaultRewardState[]", "components": [{"name": "rewardToken", "type": "address", "internalType": "address"}, {"name": "lastAccumulatedTime", "type": "uint32", "internalType": "uint32"}, {"name": "endTime", "type": "uint32", "internalType": "uint32"}, {"name": "emissionRatePerYear", "type": "uint128", "internalType": "uint128"}, {"name": "accumulatedRewardPerVaultShare", "type": "uint128", "internalType": "uint128"}]}, {"name": "rewardPool", "type": "tuple", "internalType": "struct RewardPoolStorage", "components": [{"name": "rewardPool", "type": "address", "internalType": "address"}, {"name": "lastClaimTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "forceClaimAfter", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "migrateRewardPool", "inputs": [{"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "newRewardPool", "type": "tuple", "internalType": "struct RewardPoolStorage", "components": [{"name": "rewardPool", "type": "address", "internalType": "address"}, {"name": "lastClaimTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "forceClaimAfter", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAccountRewards", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "effectiveSupplyBefore", "type": "uint256", "internalType": "uint256"}, {"name": "accountSharesBefore", "type": "uint256", "internalType": "uint256"}, {"name": "accountSharesAfter", "type": "uint256", "internalType": "uint256"}, {"name": "sharesInEscrow", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "rewards", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateRewardToken", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}, {"name": "rewardToken", "type": "address", "internalType": "address"}, {"name": "emissionRatePerYear", "type": "uint128", "internalType": "uint128"}, {"name": "endTime", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "VaultRewardTransfer", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "VaultRewardUpdate", "inputs": [{"name": "rewardToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "emissionRatePerYear", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "endTime", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b506123738061001c5f395ff3fe608060405234801561000f575f5ffd5b506004361061006f575f3560e01c806389e53e6b1161004d57806389e53e6b146100b9578063ada98cc3146100cf578063cd05b4e1146100f0575f5ffd5b8063********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", "sourceMap": "355:1082:86:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b506004361061006f575f3560e01c806389e53e6b1161004d57806389e53e6b146100b9578063ada98cc3146100cf578063cd05b4e1146100f0575f5ffd5b8063********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", "sourceMap": "355:1082:86:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1878:1220:85;;;;;;:::i;:::-;;:::i;:::-;;6976:1285;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;6573:362;;;:::i;3140:261::-;;;:::i;:::-;;;;;;;;:::i;3442:175::-;;;;;;:::i;:::-;;:::i;:::-;;;5249:25:121;;;5237:2;5222:18;3442:175:85;5103:177:121;3658:2714:85;;;;;;:::i;:::-;;:::i;1878:1220::-;676:42:97;-1:-1:-1;;;;;871:29:85;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;857:45:85;:10;-1:-1:-1;;;;;857:45:85;;853:82;;911:24;;;;;924:10;911:24;;;6329:74:121;6302:18;;911:24:85;;;;;;;;853:82;1215:21:26::1;:19;:21::i;:::-;2100:29:85::2;2155:4;-1:-1:-1::0;;;;;2132:45:85::2;;:47;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2100:79:::0;-1:-1:-1;2189:69:85::2;2100:79:::0;1565:23;2231:26:::2;2189:69;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;;;::::2;::::0;;;;::::2;::::0;;::::2;::::0;::::2;::::0;;::::2;::::0;::::2;::::0;;::::2;::::0;;-1:-1:-1;;;;;2189:69:85;::::2;::::0;;::::2;::::0;;::::2;::::0;::::2;::::0;;::::2;::::0;;;;::::2;;::::0;;;;;;;;;;::::2;::::0;::::2;::::0;;::::2;::::0;;;;;;;::::2;;::::0;;;;;;;;;::::2;::::0;::::2;;;;;;;;:18;:69::i;:::-;2268:61;::::0;;::::2;::::0;::::2;::::0;;1407:16;2268:61;-1:-1:-1;;;;;2268:61:85;::::2;::::0;;;::::2;::::0;;::::2;::::0;::::2;;::::0;::::2;::::0;;;;::::2;::::0;;::::2;::::0;;;;;;;;2344:38;2340:241:::2;;2398:46;2430:13;2398:31;:46::i;:::-;2544:24:::0;;2507:63:::2;::::0;-1:-1:-1;;;;;2507:28:85;::::2;::::0;::::2;:63::i;:::-;2612:41;::::0;;;;2647:4:::2;2612:41;::::0;::::2;6329:74:121::0;2591:18:85::2;::::0;-1:-1:-1;;;;;2612:26:85;::::2;::::0;::::2;::::0;6302:18:121;;2612:41:85::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2591:62;;2663:63;2689:9;2700:10;2712:13;2663:25;:63::i;:::-;-1:-1:-1::0;;1407:16:85;2880:65;;2989:24;;3062:29:::2;::::0;::::2;::::0;2880:65:::2;3023:68:::0;;::::2;::::0;::::2;::::0;2929:15:::2;2880:65:::0;;::::2;::::0;::::2;3023:68:::0;;;;;;;;;;;;-1:-1:-1;;;;;2955:58:85;;::::2;3023:68:::0;::::2;::::0;;-1:-1:-1;1257:20:26::1;:18;:20::i;:::-;1878:1220:85::0;;:::o;6976:1285::-;7196:24;7295:14;:40;;;;;7334:1;7313:18;:22;7295:40;7337:14;7291:60;7362:31;1565:23;7362:60;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;7362:60:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7432:48;7451:21;7474:5;7432:18;:48::i;:::-;7514:5;:12;7500:27;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;7500:27:85;;7490:37;;7543:9;7538:717;7558:5;:12;7554:1;:16;7538:717;;;7595:14;:41;;;;-1:-1:-1;7613:23:85;;7595:41;7591:171;;;1805:24;7663:49;7691:5;7697:1;7691:8;;;;;;;;:::i;:::-;;;;;;;:20;;;-1:-1:-1;;;;;7663:49:85;-1:-1:-1;;;;;7663:49:85;;;;;;;;;;;;:58;7713:7;-1:-1:-1;;;;;7663:58:85;-1:-1:-1;;;;;7663:58:85;;;;;;;;;;;;7656:65;;;7739:8;;7591:171;7784:5;7790:1;7784:8;;;;;;;;:::i;:::-;;;;;;;:28;;;7780:32;;:1;:32;7776:217;;;7901:77;7943:1;7946:5;7952:1;7946:8;;;;;;;;:::i;:::-;;;;;;;7956:21;7901:41;:77::i;:::-;8020:224;8055:5;8061:1;8055:8;;;;;;;;:::i;:::-;;;;;;;:20;;;8093:7;8118:19;8155:18;8191:5;8197:1;8191:8;;;;;;;;:::i;:::-;;;;;;;:39;;;8020:224;;:17;:224::i;:::-;8007:7;8015:1;8007:10;;;;;;;;:::i;:::-;;;;;;:237;;;;;7538:717;7572:3;;7538:717;;;;7222:1039;6976:1285;;;;;;;;:::o;6573:362::-;1215:21:26;:19;:21::i;:::-;6770:29:85::1;6825:4;-1:-1:-1::0;;;;;6802:45:85::1;;:47;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6770:79:::0;-1:-1:-1;6859:69:85::1;6770:79:::0;1565:23;6901:26:::1;1437:159:::0;6859:69:::1;6624:311;1257:20:26::0;:18;:20::i;:::-;6573:362:85:o;3140:261::-;-1:-1:-1;;;3210:38:85;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;1565:23:85;3310:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3310:41:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3374:20;1407:16;;1286:145;3374:20;3361:33;;;;;;;;;;-1:-1:-1;;;;;3361:33:85;;;;;;;;;;;;;;;;;;;;;;;3140:261;;;-1:-1:-1;3140:261:85:o;3442:175::-;-1:-1:-1;;;;;3561:40:85;;;3535:7;3561:40;;;1805:24;3561:40;;;;;;;;:49;;;;;;;;;;3442:175;;;;;:::o;3658:2714::-;676:42:97;-1:-1:-1;;;;;871:29:85;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;857:45:85;:10;-1:-1:-1;;;;;857:45:85;;853:82;;911:24;;;;;924:10;911:24;;;6329:74:121;6302:18;;911:24:85;6183:226:121;853:82:85;3849:29:::1;3904:4;-1:-1:-1::0;;;;;3881:45:85::1;;:47;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1565:23:::0;3964:33;3849:79;;-1:-1:-1;4012:23:85;;::::1;4008:2145;;;4051:29;1565:23:::0;4110:5:::1;4083:33;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;;4051:65:::1;::::0;;::::1;::::0;::::1;::::0;;4083:33:::1;::::0;;::::1;::::0;;::::1;4051:65:::0;;-1:-1:-1;;;;;4051:65:85;;::::1;::::0;;;::::1;::::0;;::::1;::::0;::::1;::::0;;::::1;::::0;;;;;;;::::1;::::0;;::::1;::::0;;;;;;;;::::1;::::0;::::1;::::0;;::::1;::::0;;;;;;;::::1;;::::0;;;;;;-1:-1:-1;4276:32:85;;::::1;;4268:41;;;;;;4698:78;4740:5;4747;4754:21;4698:41;:78::i;:::-;4834:47;::::0;::::1;:25;::::0;::::1;:47:::0;;;4928:1:::1;4899:30:::0;4895:200:::1;;4965:1;4949:13;::::0;::::1;:17:::0;4895:200:::1;;;5031:7;5013:25;;:15;:25;5005:34;;;;;;5057:23;::::0;::::1;:13;::::0;::::1;:23:::0;4895:200:::1;5144:5:::0;1565:23;5135:5:::1;5108:33;;;;;;;;:::i;:::-;;::::0;;;::::1;::::0;;;;:41;;:33:::1;::::0;;::::1;;:41:::0;;;;::::1;::::0;::::1;::::0;::::1;::::0;::::1;::::0;;::::1;::::0;::::1;::::0;;;;::::1;::::0;::::1;::::0;;;;-1:-1:-1;;;;;5108:41:85;;::::1;::::0;;;;;;;::::1;::::0;;;::::1;::::0;;;::::1;::::0;;::::1;::::0;::::1;::::0;::::1;::::0;;::::1;::::0;::::1;::::0;;::::1;::::0;::::1;::::0;::::1;::::0;;;::::1;::::0;;;::::1;::::0;-1:-1:-1;4008:2145:85::1;;;5179:15;5170:5;:24:::0;5166:987:::1;;1565:23:::0;5680::::1;::::0;::::1;::::0;5676:63:::1;;5731:7;5713:25;;:15;:25;5705:34;;;;;;5766:279;::::0;;::::1;::::0;::::1;::::0;;-1:-1:-1;;;;;5766:279:85;;::::1;::::0;;::::1;5871:15;5766:279:::0;::::1;;::::0;;::::1;::::0;;;;;::::1;::::0;;;;;;::::1;::::0;;::::1;::::0;;;;;;-1:-1:-1;5766:279:85;;;;;;5754:292;;::::1;::::0;;::::1;::::0;;;;;;;;;;;::::1;::::0;;::::1;::::0;;::::1;::::0;;;;;;;::::1;::::0;::::1;::::0;;;;::::1;::::0;::::1;::::0;;;;;;;::::1;::::0;;;;;;;::::1;::::0;;;::::1;::::0;;;::::1;::::0;;;;;;;::::1;::::0;::::1;::::0;;;::::1;;::::0;::::1;::::0;5166:987:::1;6221:69;6240:21:::0;1565:23;6263:26:::1;1437:159:::0;6221:69:::1;6305:60;::::0;;-1:-1:-1;;;;;7010:55:121;;6992:74;;7114:34;7102:47;;7097:2;7082:18;;7075:75;7198:10;7186:23;;7166:18;;;7159:51;6305:60:85;;::::1;::::0;;;;6980:2:121;6305:60:85;;::::1;3839:2533;;3658:2714:::0;;;;:::o;1290:377:26:-;637:66;3375:11:28;1444:93:26;;;1496:30;;;;;;;;;;;;;;1444:93;1611:49;1655:4;637:66;1611:36;:43;;:49::i;8387:1418:85:-;8520:58;;;;;;;;1407:16;8520:58;-1:-1:-1;;;;;8520:58:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8588:48;;8629:7;8387:1418;;:::o;8588:48::-;8699:10;:26;;;8667:10;:29;;;:58;;;;:::i;:::-;8649:76;;:15;:76;8645:89;;;8727:7;8387:1418;;:::o;8645:89::-;8744:31;8792:5;:12;8778:27;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;8778:27:85;;8744:61;;8931:9;8926:212;8946:5;:12;8942:1;:16;8926:212;;;9081:5;9087:1;9081:8;;;;;;;;:::i;:::-;;;;;;;;;;;:20;9075:52;;;;;9121:4;9075:52;;;6329:74:121;-1:-1:-1;;;;;9075:37:85;;;;;;6302:18:121;;9075:52:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9055:14;9070:1;9055:17;;;;;;;;:::i;:::-;;;;;;;;;;:72;8960:3;;8926:212;;;;9148:15;:13;:15::i;:::-;1407:16;9174:65;;;;;;9223:15;9174:65;;;;;-1:-1:-1;9392:407:85;9412:5;:12;9408:1;:16;9392:407;;;9445:20;9474:5;9480:1;9474:8;;;;;;;;:::i;:::-;;;;;;;;;;;:20;9468:52;;;;;9514:4;9468:52;;;6329:74:121;-1:-1:-1;;;;;9468:37:85;;;;;;6302:18:121;;9468:52:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9445:75;;9534:254;9586:1;9605:5;9611:1;9605:8;;;;;;;;:::i;:::-;;;;;;;9718:14;9733:1;9718:17;;;;;;;;:::i;:::-;;;;;;;9703:12;:32;;;;:::i;:::-;9753:21;9534:34;:254::i;:::-;-1:-1:-1;9426:3:85;;9392:407;;;;8510:1295;;8387:1418;;:::o;645:299:86:-;785:24;;779:56;;;;;829:4;779:56;;;6329:74:121;754:22:86;;-1:-1:-1;;;;;779:41:86;;;;6302:18:121;;779:56:86;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;871:24;;853:83;;;;;;;;7883:25:121;;;931:4:86;7924:18:121;;;7917:50;754:81:86;;-1:-1:-1;;;;;;853:61:86;;;;7856:18:121;;853:83:86;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;845:92;;;;;984:157:98;-1:-1:-1;;;;;1058:28:98;;1054:41;;984:157;;:::o;1054:41::-;1104:30;-1:-1:-1;;;;;1104:18:98;;1123:7;1132:1;1104:18;:30::i;950:485:86:-;1092:14;1127:13;:24;;;-1:-1:-1;;;;;1109:47:86;;:49;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1092:66;;1168:15;1204:13;:24;;;-1:-1:-1;;;;;1186:52:86;;:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1168:72;-1:-1:-1;1250:57:86;-1:-1:-1;;;;;1250:29:86;;1168:72;1289:17;1250:29;:57::i;:::-;1322:14;;1318:111;;1360:57;;;;;;;;8424:25:121;;;8465:18;;;8458:34;;;1412:4:86;8508:18:121;;;8501:50;-1:-1:-1;;;;;1360:31:86;;;;;8397:18:121;;1360:57:86;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1352:66;;;;;1673:105:26;1721:50;1765:5;637:66;1721:36;1666:115:28;11938:452:85;12153:117;:105;12203:5;12210:21;12233:15;12153:36;:105::i;:::-;:115;:117::i;:::-;12114:156;;:36;;;:156;12315:15;12280:51;;:25;;;:51;12114:36;1565:23;12369:5;12342:33;;;;;;;;:::i;:::-;;;;;;;;;;:41;;:33;;;;;:41;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;12342:41:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;11938:452:85:o;9846:1616::-;-1:-1:-1;;;;;10172:40:85;;;10062:21;10172:40;;;1805:24;10172:40;;;;;;;;:49;;;;;;;;;;;;;333:4:97;10249:42:85;10271:20;10249:19;:42;:::i;:::-;10248:64;;;;:::i;:::-;10247:79;;;;:::i;:::-;10231:95;-1:-1:-1;333:4:97;10403:41:85;10424:20;10403:18;:41;:::i;:::-;10402:63;;;;:::i;:::-;-1:-1:-1;;;;;10336:40:85;;;;;;;1805:24;10336:40;;;;;;;;:49;;;;;;;;;:139;10490:17;;10486:970;;-1:-1:-1;;;;;10816:23:85;;;:27;10812:634;;10867:63;;;;;-1:-1:-1;;;;;9206:55:121;;;10867:63:85;;;9188:74:121;9278:18;;;9271:34;;;10867:39:85;;;;;9161:18:121;;10867:63:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10863:569;;11369:44;;;-1:-1:-1;;;;;9544:55:121;;;9526:74;;9636:55;;9631:2;9616:18;;9609:83;-1:-1:-1;9708:18:121;;;9701:34;11369:44:85;;;;;;;9514:2:121;11369:44:85;;;10863:569;;;10953:12;10968:28;:26;:28::i;:::-;10953:43;;11022:7;11018:227;;;11062:56;;;-1:-1:-1;;;;;9544:55:121;;;9526:74;;9636:55;;9631:2;9616:18;;9609:83;9708:18;;;9701:34;;;11062:56:85;;9514:2:121;9499:18;11062:56:85;;;;;;;11018:227;;;11178:44;;;-1:-1:-1;;;;;9544:55:121;;;9526:74;;9636:55;;9631:2;9616:18;;9609:83;-1:-1:-1;9708:18:121;;;9701:34;11178:44:85;;;;;;;9514:2:121;11178:44:85;;;10931:404;10863:569;10085:1377;9846:1616;;;;;;;:::o;3491:139:28:-;3608:5;3602:4;3595:19;3491:139;;:::o;448:191:86:-;501:18;1407:16:85;522:31:86;571:60;;;;;619:4;571:60;;;10336:74:121;522:31:86;10426:18:121;;;10419:50;-1:-1:-1;;;;;522:31:86;;;;-1:-1:-1;522:31:86;;571:39;;10309:18:121;;571:60:86;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;563:69;;;;;;491:148;448:191::o;11497:435:85:-;11697:31;;11721:7;11697:31;11778:95;11830:21;11793:33;333:4:97;11793:13:85;:33;:::i;:::-;11792:59;;;;:::i;11778:95::-;11738:5;:36;;:135;;;;;;;:::i;:::-;;;;;-1:-1:-1;11920:5:85;1565:23;11911:5;11884:33;;;;;;;;:::i;:::-;;;;;;;;;;:41;;:33;;;;;:41;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;11884:41:85;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;11497:435;;;;;:::o;5084:380:19:-;5199:47;;;-1:-1:-1;;;;;9206:55:121;;5199:47:19;;;9188:74:121;9278:18;;;;9271:34;;;5199:47:19;;;;;;;;;;9161:18:121;;;;5199:47:19;;;;;;;;;;;;;;5262:44;5214:13;5199:47;5262:23;:44::i;:::-;5257:201;;5349:43;;;-1:-1:-1;;;;;9206:55:121;;5349:43:19;;;9188:74:121;5389:1:19;9278:18:121;;;;9271:34;;;;5349:43:19;;;;;;;;;;9161:18:121;;;;5349:43:19;;;;;;;;;;;;;;5322:71;;5342:5;;5322:19;:71::i;:::-;5407:40;5427:5;5434:12;5407:19;:40::i;798:180:98:-;-1:-1:-1;;;;;889:28:98;;885:41;;798:180;;;:::o;885:41::-;936:35;-1:-1:-1;;;;;936:18:98;;955:7;964:6;936:18;:35::i;:::-;798:180;;;:::o;12396:1368:85:-;12575:7;12656:5;:25;;;:30;;12685:1;12656:30;12652:79;;-1:-1:-1;12695:36:85;;;;12688:43;;;;12652:79;12753:5;:13;;;12749:17;;:1;:17;12741:26;;;;;;12777:12;12804:5;:13;;;12792:25;;:9;:25;:53;;12832:5;:13;;;12792:53;;;;;12820:9;12792:53;12777:68;;12856:51;12949:4;12921:5;:25;;;:32;;;:61;;;;;12961:21;12957:1;:25;12921:61;12917:741;;;13057:33;13100:5;:25;;;13093:32;;:4;:32;;;;:::i;:::-;13057:68;-1:-1:-1;13618:28:85;13625:21;450:8:97;13618:28:85;:::i;:::-;13572:25;;;;13524:73;;:45;333:4:97;13524:25:85;:45;:::i;:::-;:73;;;;:::i;:::-;13523:124;;;;:::i;:::-;13477:170;;12984:674;12917:741;13714:43;13675:5;:36;;;:82;;;;;;:::i;:::-;13668:89;;;;12396:1368;;;;;;:::o;383:173:99:-;436:7;476:17;463:31;;;455:40;;;;;;-1:-1:-1;547:1:99;383:173::o;1147:711:98:-;1197:12;1221:24;;:::i;:::-;1285:16;1318:135;;;;1475:2;1470:219;;;;1822:1;1819;1812:12;1318:135;1411:1;1400:12;;1278:564;1264:588;1147:711;:::o;1470:219::-;1576:2;1573:1;1565:6;1550:29;-1:-1:-1;1611:13:98;;1147:711;-1:-1:-1;1147:711:98:o;9592:480:19:-;9675:4;9691:12;9713:18;9741:19;9875:4;9872:1;9865:4;9859:11;9852:4;9846;9842:15;9839:1;9832:5;9825;9820:60;9809:71;;9907:16;9893:30;;9957:1;9951:8;9936:23;;9985:7;:80;;;;-1:-1:-1;9997:15:19;;:67;;10048:11;10063:1;10048:16;9997:67;;;10044:1;10023:5;-1:-1:-1;;;;;10015:26:19;;:30;9997:67;9978:87;9592:480;-1:-1:-1;;;;;;9592:480:19:o;8370:720::-;8450:18;8478:19;8616:4;8613:1;8606:4;8600:11;8593:4;8587;8583:15;8580:1;8573:5;8566;8561:60;8673:7;8663:176;;8717:4;8711:11;8762:16;8759:1;8754:3;8739:40;8808:16;8803:3;8796:29;8663:176;-1:-1:-1;;8916:1:19;8910:8;8866:16;;-1:-1:-1;8942:15:19;;:68;;8994:11;9009:1;8994:16;;8942:68;;;-1:-1:-1;;;;;8960:26:19;;;:31;8942:68;8938:146;;;9033:40;;;;;-1:-1:-1;;;;;6347:55:121;;9033:40:19;;;6329:74:121;6302:18;;9033:40:19;6183:226:121;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:154:121:-;-1:-1:-1;;;;;93:5:121;89:54;82:5;79:65;69:93;;158:1;155;148:12;173:184;225:77;222:1;215:88;322:4;319:1;312:15;346:4;343:1;336:15;362:163;429:20;;489:10;478:22;;468:33;;458:61;;515:1;512;505:12;458:61;362:163;;;:::o;530:1094::-;634:6;642;686:9;677:7;673:23;716:3;712:2;708:12;705:32;;;733:1;730;723:12;705:32;772:9;759:23;791:31;816:5;791:31;:::i;:::-;841:5;-1:-1:-1;939:4:121;870:66;862:75;;858:86;855:106;;;957:1;954;947:12;855:106;;990:2;984:9;1032:4;1024:6;1020:17;1103:6;1091:10;1088:22;1067:18;1055:10;1052:34;1049:62;1046:242;;;1144:77;1141:1;1134:88;1245:4;1242:1;1235:15;1273:4;1270:1;1263:15;1046:242;1304:2;1297:22;1371:2;1356:18;;1343:32;1384:33;1343:32;1384:33;:::i;:::-;1426:23;;1482:37;1515:2;1500:18;;1482:37;:::i;:::-;1477:2;1469:6;1465:15;1458:62;1553:39;1586:4;1575:9;1571:20;1553:39;:::i;:::-;1548:2;1540:6;1536:15;1529:64;1612:6;1602:16;;;530:1094;;;;;:::o;1629:118::-;1715:5;1708:13;1701:21;1694:5;1691:32;1681:60;;1737:1;1734;1727:12;1752:744;1844:6;1852;1860;1868;1876;1929:3;1917:9;1908:7;1904:23;1900:33;1897:53;;;1946:1;1943;1936:12;1897:53;1985:9;1972:23;2004:31;2029:5;2004:31;:::i;:::-;2054:5;-1:-1:-1;2132:2:121;2117:18;;2104:32;;-1:-1:-1;2235:2:121;2220:18;;2207:32;;-1:-1:-1;2338:2:121;2323:18;;2310:32;;-1:-1:-1;2420:3:121;2405:19;;2392:33;2434:30;2392:33;2434:30;:::i;:::-;2483:7;2473:17;;;1752:744;;;;;;;;:::o;2501:611::-;2691:2;2703:21;;;2773:13;;2676:18;;;2795:22;;;2643:4;;2874:15;;;2848:2;2833:18;;;2643:4;2917:169;2931:6;2928:1;2925:13;2917:169;;;2992:13;;2980:26;;3035:2;3061:15;;;;3026:12;;;;2953:1;2946:9;2917:169;;;-1:-1:-1;3103:3:121;;2501:611;-1:-1:-1;;;;;2501:611:121:o;3419:1286::-;3779:3;3792:22;;;3863:13;;3764:19;;;3885:22;;;3731:4;;3977;3965:17;;;3938:3;3923:19;;;3731:4;4010:595;4024:6;4021:1;4018:13;4010:595;;;4089:6;4083:13;-1:-1:-1;;;;;4131:2:121;4125:9;4121:58;4116:3;4109:71;4242:10;4234:4;4230:2;4226:13;4220:20;4216:37;4209:4;4204:3;4200:14;4193:61;4316:10;4308:4;4304:2;4300:13;4294:20;4290:37;4283:4;4278:3;4274:14;4267:61;4390:34;4382:4;4378:2;4374:13;4368:20;4364:61;4357:4;4352:3;4348:14;4341:85;4486:34;4479:3;4475:2;4471:12;4465:19;4461:60;4455:3;4450;4446:13;4439:83;;4551:3;4546;4542:13;4535:20;;4590:4;4582:6;4578:17;4568:27;;4046:1;4043;4039:9;4034:14;;4010:595;;;4014:3;;4622;4614:11;;;;4634:65;4693:4;4682:9;4678:20;4670:6;-1:-1:-1;;;;;3210:5:121;3204:12;3200:61;3195:3;3188:74;3323:10;3315:4;3308:5;3304:16;3298:23;3294:40;3287:4;3282:3;3278:14;3271:64;3396:10;3388:4;3381:5;3377:16;3371:23;3367:40;3360:4;3355:3;3351:14;3344:64;;;3117:297;4710:388;4778:6;4786;4839:2;4827:9;4818:7;4814:23;4810:32;4807:52;;;4855:1;4852;4845:12;4807:52;4894:9;4881:23;4913:31;4938:5;4913:31;:::i;:::-;4963:5;-1:-1:-1;5020:2:121;5005:18;;4992:32;5033:33;4992:32;5033:33;:::i;:::-;5085:7;5075:17;;;4710:388;;;;;:::o;5285:637::-;5370:6;5378;5386;5394;5447:3;5435:9;5426:7;5422:23;5418:33;5415:53;;;5464:1;5461;5454:12;5415:53;5509:23;;;-1:-1:-1;5608:2:121;5593:18;;5580:32;5621:33;5580:32;5621:33;:::i;:::-;5673:7;-1:-1:-1;5732:2:121;5717:18;;5704:32;5780:34;5767:48;;5755:61;;5745:89;;5830:1;5827;5820:12;5745:89;5853:7;-1:-1:-1;5879:37:121;5912:2;5897:18;;5879:37;:::i;:::-;5869:47;;5285:637;;;;;;;:::o;5927:251::-;5997:6;6050:2;6038:9;6029:7;6025:23;6021:32;6018:52;;;6066:1;6063;6056:12;6018:52;6098:9;6092:16;6117:31;6142:5;6117:31;:::i;6414:184::-;6484:6;6537:2;6525:9;6516:7;6512:23;6508:32;6505:52;;;6553:1;6550;6543:12;6505:52;-1:-1:-1;6576:16:121;;6414:184;-1:-1:-1;6414:184:121:o;6603:::-;6655:77;6652:1;6645:88;6752:4;6749:1;6742:15;6776:4;6773:1;6766:15;7221:184;7273:77;7270:1;7263:88;7370:4;7367:1;7360:15;7394:4;7391:1;7384:15;7410:167;7505:10;7478:18;;;7498;;;7474:43;;7529:19;;7526:45;;;7551:18;;:::i;7582:128::-;7649:9;;;7670:11;;;7667:37;;;7684:18;;:::i;7978:245::-;8045:6;8098:2;8086:9;8077:7;8073:23;8069:32;8066:52;;;8114:1;8111;8104:12;8066:52;8146:9;8140:16;8165:28;8187:5;8165:28;:::i;8562:168::-;8635:9;;;8666;;8683:15;;;8677:22;;8663:37;8653:71;;8704:18;;:::i;8735:274::-;8775:1;8801;8791:189;;8836:77;8833:1;8826:88;8937:4;8934:1;8927:15;8965:4;8962:1;8955:15;8791:189;-1:-1:-1;8994:9:121;;8735:274::o;10480:240::-;10600:34;10549:42;;;10593;;;10545:91;;10648:43;;10645:69;;;10694:18;;:::i;11035:125::-;11100:9;;;11121:10;;;11118:36;;;11134:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"claimRewardTokens()": "71df13da", "getRewardDebt(address,address)": "ada98cc3", "getRewardSettings()": "89e53e6b", "migrateRewardPool(address,(address,uint32,uint32))": "********", "updateAccountRewards(address,uint256,uint256,uint256,bool)": "49ba860d", "updateRewardToken(uint256,address,uint128,uint32)": "cd05b4e1"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"VaultRewardTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"emissionRatePerYear\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"endTime\",\"type\":\"uint32\"}],\"name\":\"VaultRewardUpdate\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"claimRewardTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getRewardDebt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardSettings\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastAccumulatedTime\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"endTime\",\"type\":\"uint32\"},{\"internalType\":\"uint128\",\"name\":\"emissionRatePerYear\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"accumulatedRewardPerVaultShare\",\"type\":\"uint128\"}],\"internalType\":\"struct VaultRewardState[]\",\"name\":\"rewardStates\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"rewardPool\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastClaimTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"forceClaimAfter\",\"type\":\"uint32\"}],\"internalType\":\"struct RewardPoolStorage\",\"name\":\"rewardPool\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"rewardPool\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastClaimTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"forceClaimAfter\",\"type\":\"uint32\"}],\"internalType\":\"struct RewardPoolStorage\",\"name\":\"newRewardPool\",\"type\":\"tuple\"}],\"name\":\"migrateRewardPool\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"effectiveSupplyBefore\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accountSharesBefore\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accountSharesAfter\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"sharesInEscrow\",\"type\":\"bool\"}],\"name\":\"updateAccountRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewards\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"emissionRatePerYear\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"endTime\",\"type\":\"uint32\"}],\"name\":\"updateRewardToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"getRewardDebt(address,address)\":{\"params\":{\"account\":\"Address of the account\",\"rewardToken\":\"Address of the reward token\"},\"returns\":{\"_0\":\"The reward debt for the account\"}},\"getRewardSettings()\":{\"returns\":{\"rewardPool\":\"Reward pool storage\",\"rewardStates\":\"Array of vault reward states\"}},\"migrateRewardPool(address,(address,uint32,uint32))\":{\"params\":{\"newRewardPool\":\"The new reward pool storage configuration\",\"poolToken\":\"The pool token to migrate\"}},\"updateAccountRewards(address,uint256,uint256,uint256,bool)\":{\"params\":{\"account\":\"Address of the account\",\"accountSharesAfter\":\"Number of shares after the operation\",\"accountSharesBefore\":\"Number of shares before the operation\",\"effectiveSupplyBefore\":\"Total vault shares before the operation\",\"sharesInEscrow\":\"Whether the shares are in escrow\"}},\"updateRewardToken(uint256,address,uint128,uint32)\":{\"params\":{\"emissionRatePerYear\":\"Emission rate per year for the token\",\"endTime\":\"End time for the emission rate\",\"index\":\"Index of the reward token\",\"rewardToken\":\"Address of the reward token\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"claimRewardTokens()\":{\"notice\":\"Claims all the rewards for the entire vault and updates the accumulators. Does not update emission rewarders since those are automatically updated on every account claim.\"},\"getRewardDebt(address,address)\":{\"notice\":\"Returns the reward debt for the given reward token and account\"},\"getRewardSettings()\":{\"notice\":\"Returns the current reward claim method and reward state\"},\"migrateRewardPool(address,(address,uint32,uint32))\":{\"notice\":\"Migrates the reward pool to a new reward pool, needs to be called initially to set the reward pool storage and when the reward pool is updated.\"},\"updateAccountRewards(address,uint256,uint256,uint256,bool)\":{\"notice\":\"Updates account rewards during enter and exit vault operations, only callable via delegatecall from inside the vault\"},\"updateRewardToken(uint256,address,uint128,uint32)\":{\"notice\":\"Sets a secondary reward rate for a given token, only callable via the owner\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/rewards/ConvexRewardManager.sol\":\"ConvexRewardManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Curve/IConvex.sol\":{\"keccak256\":\"0x72201de544f9d37f7993fd296507092e1ce217a95367b55232264c7bb49b2127\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://519191f39f9f3c319a38fd898eba05a1d8a7c9fac580fb5d2c6da26cbae7dab6\",\"dweb:/ipfs/QmZdqAcDQjeWZtezKm1HaS1NefEcnY9566u7VPpZh2k7nb\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/IEIP20NonStandard.sol\":{\"keccak256\":\"0xcd73239cf684d4ee718e2c6cf9a380e298d21948cd1eaa1843e16b2ec1438e5a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4779a6b1fb6c29de8bc5f8139a2693b4ea93ad32698120f665cbd06094c36db9\",\"dweb:/ipfs/QmdVndD4AgxaS4Uivyt5GEuC4Bn51shw5EmFYnvHRzizq4\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/AbstractRewardManager.sol\":{\"keccak256\":\"0xb17a03a9005fd5ddf37b885ebf9e871e38f6b5651295350ff331e0a61022d258\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://9e98988d52eed68483f1de11bb7b1bc65bac5a7ec351aa1a093587633bf4f0b6\",\"dweb:/ipfs/QmV8fSZgvLbu6tAr9Mh6gMipULb6Gd2pW7E9hZe19X8pH1\"]},\"src/rewards/ConvexRewardManager.sol\":{\"keccak256\":\"0xb24143b2249890aefc6eb112a0f1132aad11a6ba64777945a042fa4321aec7cb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d499699d8108107aff16f94929d7139c1d73b4893519b845ea8f644fa17c188d\",\"dweb:/ipfs/QmYWCCe1QYaNi6tTEspeD2zvRpdJR9jeAWuBqSgbVbiNkN\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "address", "name": "account", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "VaultRewardTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": false}, {"internalType": "uint128", "name": "emissionRatePerYear", "type": "uint128", "indexed": false}, {"internalType": "uint32", "name": "endTime", "type": "uint32", "indexed": false}], "type": "event", "name": "VaultRewardUpdate", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "claimRewardTokens"}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getRewardDebt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getRewardSettings", "outputs": [{"internalType": "struct VaultRewardState[]", "name": "rewardStates", "type": "tuple[]", "components": [{"internalType": "address", "name": "rewardToken", "type": "address"}, {"internalType": "uint32", "name": "lastAccumulatedTime", "type": "uint32"}, {"internalType": "uint32", "name": "endTime", "type": "uint32"}, {"internalType": "uint128", "name": "emissionRatePerYear", "type": "uint128"}, {"internalType": "uint128", "name": "accumulatedRewardPerVaultShare", "type": "uint128"}]}, {"internalType": "struct RewardPoolStorage", "name": "rewardPool", "type": "tuple", "components": [{"internalType": "address", "name": "rewardPool", "type": "address"}, {"internalType": "uint32", "name": "lastClaimTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "forceClaimAfter", "type": "uint32"}]}]}, {"inputs": [{"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "struct RewardPoolStorage", "name": "newRewardPool", "type": "tuple", "components": [{"internalType": "address", "name": "rewardPool", "type": "address"}, {"internalType": "uint32", "name": "lastClaimTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "forceClaimAfter", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "migrateRewardPool"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "effectiveSupplyBefore", "type": "uint256"}, {"internalType": "uint256", "name": "accountSharesBefore", "type": "uint256"}, {"internalType": "uint256", "name": "accountSharesAfter", "type": "uint256"}, {"internalType": "bool", "name": "sharesInEscrow", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateAccountRewards", "outputs": [{"internalType": "uint256[]", "name": "rewards", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "rewardToken", "type": "address"}, {"internalType": "uint128", "name": "emissionRatePerYear", "type": "uint128"}, {"internalType": "uint32", "name": "endTime", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "updateRewardToken"}], "devdoc": {"kind": "dev", "methods": {"getRewardDebt(address,address)": {"params": {"account": "Address of the account", "rewardToken": "Address of the reward token"}, "returns": {"_0": "The reward debt for the account"}}, "getRewardSettings()": {"returns": {"rewardPool": "Reward pool storage", "rewardStates": "Array of vault reward states"}}, "migrateRewardPool(address,(address,uint32,uint32))": {"params": {"newRewardPool": "The new reward pool storage configuration", "poolToken": "The pool token to migrate"}}, "updateAccountRewards(address,uint256,uint256,uint256,bool)": {"params": {"account": "Address of the account", "accountSharesAfter": "Number of shares after the operation", "accountSharesBefore": "Number of shares before the operation", "effectiveSupplyBefore": "Total vault shares before the operation", "sharesInEscrow": "Whether the shares are in escrow"}}, "updateRewardToken(uint256,address,uint128,uint32)": {"params": {"emissionRatePerYear": "Emission rate per year for the token", "endTime": "End time for the emission rate", "index": "Index of the reward token", "rewardToken": "Address of the reward token"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"claimRewardTokens()": {"notice": "Claims all the rewards for the entire vault and updates the accumulators. Does not update emission rewarders since those are automatically updated on every account claim."}, "getRewardDebt(address,address)": {"notice": "Returns the reward debt for the given reward token and account"}, "getRewardSettings()": {"notice": "Returns the current reward claim method and reward state"}, "migrateRewardPool(address,(address,uint32,uint32))": {"notice": "Migrates the reward pool to a new reward pool, needs to be called initially to set the reward pool storage and when the reward pool is updated."}, "updateAccountRewards(address,uint256,uint256,uint256,bool)": {"notice": "Updates account rewards during enter and exit vault operations, only callable via delegatecall from inside the vault"}, "updateRewardToken(uint256,address,uint128,uint32)": {"notice": "Sets a secondary reward rate for a given token, only callable via the owner"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/rewards/ConvexRewardManager.sol": "ConvexRewardManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Curve/IConvex.sol": {"keccak256": "0x72201de544f9d37f7993fd296507092e1ce217a95367b55232264c7bb49b2127", "urls": ["bzz-raw://519191f39f9f3c319a38fd898eba05a1d8a7c9fac580fb5d2c6da26cbae7dab6", "dweb:/ipfs/QmZdqAcDQjeWZtezKm1HaS1NefEcnY9566u7VPpZh2k7nb"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/IEIP20NonStandard.sol": {"keccak256": "0xcd73239cf684d4ee718e2c6cf9a380e298d21948cd1eaa1843e16b2ec1438e5a", "urls": ["bzz-raw://4779a6b1fb6c29de8bc5f8139a2693b4ea93ad32698120f665cbd06094c36db9", "dweb:/ipfs/QmdVndD4AgxaS4Uivyt5GEuC4Bn51shw5EmFYnvHRzizq4"], "license": "GPL-3.0-only"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/AbstractRewardManager.sol": {"keccak256": "0xb17a03a9005fd5ddf37b885ebf9e871e38f6b5651295350ff331e0a61022d258", "urls": ["bzz-raw://9e98988d52eed68483f1de11bb7b1bc65bac5a7ec351aa1a093587633bf4f0b6", "dweb:/ipfs/QmV8fSZgvLbu6tAr9Mh6gMipULb6Gd2pW7E9hZe19X8pH1"], "license": "BUSL-1.1"}, "src/rewards/ConvexRewardManager.sol": {"keccak256": "0xb24143b2249890aefc6eb112a0f1132aad11a6ba64777945a042fa4321aec7cb", "urls": ["bzz-raw://d499699d8108107aff16f94929d7139c1d73b4893519b845ea8f644fa17c188d", "dweb:/ipfs/QmYWCCe1QYaNi6tTEspeD2zvRpdJR9jeAWuBqSgbVbiNkN"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}}, "version": 1}, "id": 86}