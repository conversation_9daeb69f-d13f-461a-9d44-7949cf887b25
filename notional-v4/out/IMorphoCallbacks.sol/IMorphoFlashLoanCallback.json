{"abi": [{"type": "function", "name": "onMorphoFlashLoan", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"onMorphoFlashLoan(uint256,bytes)": "31f57072"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"onMorphoFlashLoan\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"onMorphoFlashLoan(uint256,bytes)\":{\"details\":\"The callback is called only if data is not empty.\",\"params\":{\"assets\":\"The amount of assets that was flash loaned.\",\"data\":\"Arbitrary data passed to the `flashLoan` function.\"}}},\"title\":\"IMorphoFlashLoanCallback\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"onMorpho<PERSON><PERSON><PERSON>oan(uint256,bytes)\":{\"notice\":\"Callback called when a flash loan occurs.\"}},\"notice\":\"Interface that users willing to use `flashLoan`'s callback must implement.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Morpho/IMorphoCallbacks.sol\":\"IMorphoFlashLoanCallback\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "onMorphoFlashLoan"}], "devdoc": {"kind": "dev", "methods": {"onMorphoFlashLoan(uint256,bytes)": {"details": "The callback is called only if data is not empty.", "params": {"assets": "The amount of assets that was flash loaned.", "data": "Arbitrary data passed to the `flashLoan` function."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"onMorphoFlashLoan(uint256,bytes)": {"notice": "Callback called when a flash loan occurs."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Morpho/IMorphoCallbacks.sol": "IMorphoFlashLoanCallback"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 76}