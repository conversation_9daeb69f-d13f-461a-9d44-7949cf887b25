{"abi": [{"type": "function", "name": "allowTransfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "currentAccount", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burnShares", "inputs": [{"name": "sharesOwner", "type": "address", "internalType": "address"}, {"name": "sharesToBurn", "type": "uint256", "internalType": "uint256"}, {"name": "sharesHeld", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "assetsWithdrawn", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "clear<PERSON><PERSON><PERSON>A<PERSON>unt", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "collectFees", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "convertSharesToYieldToken", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToAssets", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertYieldTokenToAsset", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertYieldTokenToShares", "inputs": [{"name": "yieldTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "effectiveSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "feeRate", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "feesAccrued", "inputs": [], "outputs": [{"name": "feesAccruedInYieldToken", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "sharesHeld", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdrawNative", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintShares", "inputs": [{"name": "assetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "depositData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "sharesMinted", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "postLiquidation", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidator", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "preLiquidation", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidate", "type": "uint256", "internalType": "uint256"}, {"name": "accountSharesHeld", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "price", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "price", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemNative", "inputs": [{"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "assetsWithdrawn", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "yieldToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "VaultCreated", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "CannotEnterPosition", "inputs": []}, {"type": "error", "name": "CurrentAccountAlreadySet", "inputs": []}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InsufficientSharesHeld", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UnauthorizedLendingMarketTransfer", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"allowTransfer(address,uint256,address)": "98476c2b", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "asset()": "38d52e0f", "balanceOf(address)": "70a08231", "burnShares(address,uint256,uint256,bytes)": "0db734d4", "clearCurrentAccount()": "b35cb45d", "collectFees()": "c8796572", "convertSharesToYieldToken(uint256)": "b905a4ff", "convertToAssets(uint256)": "07a2d13a", "convertToShares(uint256)": "c6e6f592", "convertYieldTokenToAsset()": "e0b4327d", "convertYieldTokenToShares(uint256)": "********", "decimals()": "313ce567", "effectiveSupply()": "8fc47093", "feeRate()": "978bbdb9", "feesAccrued()": "94db0595", "initialize(bytes)": "439fab91", "initiateWithdraw(address,uint256,bytes)": "57831a04", "initiateWithdrawNative(bytes)": "131b822d", "mintShares(uint256,address,bytes)": "127af7f9", "name()": "06fdde03", "postLiquidation(address,address,uint256)": "98dce16d", "preLiquidation(address,address,uint256,uint256)": "cc351ac5", "price()": "a035b1fe", "price(address)": "aea91078", "redeemNative(uint256,bytes)": "eb9b1912", "symbol()": "95d89b41", "totalAssets()": "01e1d114", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "yieldToken()": "76d5de85"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"CannotEnterPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CurrentAccountAlreadySet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientSharesHeld\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"UnauthorizedLendingMarketTransfer\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"VaultCreated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"currentAccount\",\"type\":\"address\"}],\"name\":\"allowTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"asset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sharesOwner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToBurn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesHeld\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"burnShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsWithdrawn\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"clearCurrentAccount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"collectFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"convertSharesToYieldToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"convertToAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"convertToShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"convertYieldTokenToAsset\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"yieldTokens\",\"type\":\"uint256\"}],\"name\":\"convertYieldTokenToShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"effectiveSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feesAccrued\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"feesAccruedInYieldToken\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesHeld\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdrawNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assetAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"depositData\",\"type\":\"bytes\"}],\"name\":\"mintShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesMinted\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidator\",\"type\":\"uint256\"}],\"name\":\"postLiquidation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accountSharesHeld\",\"type\":\"uint256\"}],\"name\":\"preLiquidation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesToRedeem\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"redeemNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsWithdrawn\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"yieldToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowTransfer(address,uint256,address)\":{\"params\":{\"amount\":\"The amount of shares to allow the transfer of.\",\"currentAccount\":\"The address of the current account.\",\"to\":\"The address to allow the transfer to.\"}},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"burnShares(address,uint256,uint256,bytes)\":{\"params\":{\"redeemData\":\"calldata used to redeem the yield token.\",\"sharesOwner\":\"The address of the account to burn the shares for.\",\"sharesToBurn\":\"The amount of shares to burn.\"}},\"collectFees()\":{\"details\":\"Collects the fees accrued by the vault. Only callable by the owner.\"},\"convertSharesToYieldToken(uint256)\":{\"details\":\"Returns the amount of yield tokens that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met.\"},\"convertToAssets(uint256)\":{\"details\":\"Returns the amount of assets that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange.\"},\"convertToShares(uint256)\":{\"details\":\"Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the \\u201cper-user\\u201d price-per-share, and instead should reflect the \\u201caverage-user\\u2019s\\u201d price-per-share, meaning what the average user should expect to see when exchanging to and from.\"},\"convertYieldTokenToAsset()\":{\"details\":\"Returns the oracle price of a yield token in terms of the asset token.\"},\"convertYieldTokenToShares(uint256)\":{\"details\":\"Returns the amount of yield tokens that the account would receive for the amount of shares provided.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"effectiveSupply()\":{\"details\":\"Returns the effective supply which excludes any escrowed shares.\"},\"feesAccrued()\":{\"details\":\"Returns the balance of yield tokens accrued by the vault.\"},\"initiateWithdraw(address,uint256,bytes)\":{\"params\":{\"account\":\"The address of the account to initiate the withdraw for.\",\"data\":\"calldata used to initiate the withdraw.\",\"sharesHeld\":\"The number of shares the account holds.\"}},\"initiateWithdrawNative(bytes)\":{\"details\":\"We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.\",\"params\":{\"data\":\"calldata used to initiate the withdraw.\"}},\"postLiquidation(address,address,uint256)\":{\"params\":{\"liquidateAccount\":\"The address of the account to liquidate.\",\"liquidator\":\"The address of the liquidator.\",\"sharesToLiquidator\":\"The amount of shares to liquidate.\"}},\"preLiquidation(address,address,uint256,uint256)\":{\"params\":{\"accountSharesHeld\":\"The amount of shares the account holds.\",\"liquidateAccount\":\"The address of the account to liquidate.\",\"liquidator\":\"The address of the liquidator.\",\"sharesToLiquidate\":\"The amount of shares to liquidate.\"}},\"price()\":{\"details\":\"It corresponds to the price of 10**(collateral token decimals) assets of collateral token quoted in 10**(loan token decimals) assets of loan token with `36 + loan token decimals - collateral token decimals` decimals of precision.\"},\"price(address)\":{\"details\":\"Returns the price of a yield token in terms of the asset token for the given borrower taking into account withdrawals.\"},\"redeemNative(uint256,bytes)\":{\"details\":\"We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.\",\"params\":{\"redeemData\":\"calldata used to redeem the yield token.\",\"sharesToRedeem\":\"The amount of shares to redeem.\"}},\"totalAssets()\":{\"details\":\"Returns the total amount of the underlying asset that is \\u201cmanaged\\u201d by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"stateVariables\":{\"asset\":{\"details\":\"Returns the address of the underlying token used for the Vault for accounting, depositing, and withdrawing. - MUST be an ERC-20 token contract. - MUST NOT revert.\"},\"feeRate\":{\"details\":\"Returns the fee rate of the vault where 100% = 1e18.\"},\"yieldToken\":{\"details\":\"Returns the address of the yield token held by the vault. Does not equal the share token, which represents each user's share of the yield tokens held by the vault. - MUST be an ERC-20 token contract. - MUST NOT revert.\"}},\"title\":\"AbstractYieldStrategy\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"allowTransfer(address,uint256,address)\":{\"notice\":\"Allows the lending market to transfer shares on exit position or liquidation.\"},\"burnShares(address,uint256,uint256,bytes)\":{\"notice\":\"Burns shares for a given number of shares.\"},\"clearCurrentAccount()\":{\"notice\":\"Clears the current account.\"},\"initiateWithdraw(address,uint256,bytes)\":{\"notice\":\"Initiates a withdraw for a given number of shares.\"},\"initiateWithdrawNative(bytes)\":{\"notice\":\"Initiates a withdraw for the native balance of the account.\"},\"postLiquidation(address,address,uint256)\":{\"notice\":\"Post-liquidation function.\"},\"preLiquidation(address,address,uint256,uint256)\":{\"notice\":\"Pre-liquidation function.\"},\"price()\":{\"notice\":\"Returns the price of 1 asset of collateral token quoted in 1 asset of loan token, scaled by 1e36.\"},\"redeemNative(uint256,bytes)\":{\"notice\":\"Redeems shares for assets for a native token.\"}},\"notice\":\"This is the base contract for all yield strategies, it implements the core logic for minting, burning and the valuation of tokens.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/AbstractYieldStrategy.sol\":\"AbstractYieldStrategy\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "CannotEnterPosition"}, {"inputs": [], "type": "error", "name": "CurrentAccountAlreadySet"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [], "type": "error", "name": "InsufficientSharesHeld"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "type": "error", "name": "UnauthorizedLendingMarketTransfer"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}], "type": "event", "name": "VaultCreated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "currentAccount", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "allowTransfer"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "sharesOwner", "type": "address"}, {"internalType": "uint256", "name": "sharesToBurn", "type": "uint256"}, {"internalType": "uint256", "name": "sharesHeld", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "burnShares", "outputs": [{"internalType": "uint256", "name": "assetsWithdrawn", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "clear<PERSON><PERSON><PERSON>A<PERSON>unt"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "collectFees"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertSharesToYieldToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "convertYieldTokenToAsset", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "yieldTokens", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertYieldTokenToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "effectiveSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feesAccrued", "outputs": [{"internalType": "uint256", "name": "feesAccruedInYieldToken", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "sharesHeld", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdrawNative", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assetAmount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "bytes", "name": "depositData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "mintShares", "outputs": [{"internalType": "uint256", "name": "sharesMinted", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidator", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "postLiquidation"}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidate", "type": "uint256"}, {"internalType": "uint256", "name": "accountSharesHeld", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "preLiquidation"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "sharesToRedeem", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "redeemNative", "outputs": [{"internalType": "uint256", "name": "assetsWithdrawn", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "yieldToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"allowTransfer(address,uint256,address)": {"params": {"amount": "The amount of shares to allow the transfer of.", "currentAccount": "The address of the current account.", "to": "The address to allow the transfer to."}}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "burnShares(address,uint256,uint256,bytes)": {"params": {"redeemData": "calldata used to redeem the yield token.", "sharesOwner": "The address of the account to burn the shares for.", "sharesToBurn": "The amount of shares to burn."}}, "collectFees()": {"details": "Collects the fees accrued by the vault. Only callable by the owner."}, "convertSharesToYieldToken(uint256)": {"details": "Returns the amount of yield tokens that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met."}, "convertToAssets(uint256)": {"details": "Returns the amount of assets that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange."}, "convertToShares(uint256)": {"details": "Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the “per-user” price-per-share, and instead should reflect the “average-user’s” price-per-share, meaning what the average user should expect to see when exchanging to and from."}, "convertYieldTokenToAsset()": {"details": "Returns the oracle price of a yield token in terms of the asset token."}, "convertYieldTokenToShares(uint256)": {"details": "Returns the amount of yield tokens that the account would receive for the amount of shares provided."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "effectiveSupply()": {"details": "Returns the effective supply which excludes any escrowed shares."}, "feesAccrued()": {"details": "Returns the balance of yield tokens accrued by the vault."}, "initiateWithdraw(address,uint256,bytes)": {"params": {"account": "The address of the account to initiate the withdraw for.", "data": "calldata used to initiate the withdraw.", "sharesHeld": "The number of shares the account holds."}}, "initiateWithdrawNative(bytes)": {"details": "We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.", "params": {"data": "calldata used to initiate the withdraw."}}, "postLiquidation(address,address,uint256)": {"params": {"liquidateAccount": "The address of the account to liquidate.", "liquidator": "The address of the liquidator.", "sharesToLiquidator": "The amount of shares to liquidate."}}, "preLiquidation(address,address,uint256,uint256)": {"params": {"accountSharesHeld": "The amount of shares the account holds.", "liquidateAccount": "The address of the account to liquidate.", "liquidator": "The address of the liquidator.", "sharesToLiquidate": "The amount of shares to liquidate."}}, "price()": {"details": "It corresponds to the price of 10**(collateral token decimals) assets of collateral token quoted in 10**(loan token decimals) assets of loan token with `36 + loan token decimals - collateral token decimals` decimals of precision."}, "price(address)": {"details": "Returns the price of a yield token in terms of the asset token for the given borrower taking into account withdrawals."}, "redeemNative(uint256,bytes)": {"details": "We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.", "params": {"redeemData": "calldata used to redeem the yield token.", "sharesToRedeem": "The amount of shares to redeem."}}, "totalAssets()": {"details": "Returns the total amount of the underlying asset that is “managed” by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allowTransfer(address,uint256,address)": {"notice": "Allows the lending market to transfer shares on exit position or liquidation."}, "burnShares(address,uint256,uint256,bytes)": {"notice": "Burns shares for a given number of shares."}, "clearCurrentAccount()": {"notice": "Clears the current account."}, "initiateWithdraw(address,uint256,bytes)": {"notice": "Initiates a withdraw for a given number of shares."}, "initiateWithdrawNative(bytes)": {"notice": "Initiates a withdraw for the native balance of the account."}, "postLiquidation(address,address,uint256)": {"notice": "Post-liquidation function."}, "preLiquidation(address,address,uint256,uint256)": {"notice": "Pre-liquidation function."}, "price()": {"notice": "Returns the price of 1 asset of collateral token quoted in 1 asset of loan token, scaled by 1e36."}, "redeemNative(uint256,bytes)": {"notice": "Redeems shares for assets for a native token."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/AbstractYieldStrategy.sol": "AbstractYieldStrategy"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}}, "version": 1}, "id": 54}