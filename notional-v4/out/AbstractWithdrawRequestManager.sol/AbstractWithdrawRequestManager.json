{"abi": [{"type": "function", "name": "STAKING_TOKEN", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "WITHDRAW_TOKEN", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "YIELD_TOKEN", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "canFinalizeWithdrawRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "finalizeAndRedeemWithdrawRequest", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "withdrawYieldTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "sharesToBurn", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokensWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "finalized", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "finalizeRequestManual", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "tokensWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "finalized", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getWithdrawRequest", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "w", "type": "tuple", "internalType": "struct WithdrawRequest", "components": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "yieldTokenAmount", "type": "uint120", "internalType": "uint120"}, {"name": "sharesAmount", "type": "uint120", "internalType": "uint120"}]}, {"name": "s", "type": "tuple", "internalType": "struct TokenizedWithdrawRequest", "components": [{"name": "totalYieldTokenAmount", "type": "uint120", "internalType": "uint120"}, {"name": "totalWithdraw", "type": "uint120", "internalType": "uint120"}, {"name": "finalized", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getWithdrawRequestValue", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "hasRequest", "type": "bool", "internalType": "bool"}, {"name": "valueInAsset", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "yieldTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "sharesAmount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApprovedVault", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isPendingWithdrawRequest", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "rescueTokens", "inputs": [{"name": "cooldownHolder", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApp<PERSON>Vault", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "isApproved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stakeTokens", "inputs": [{"name": "depositToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "yieldTokensMinted", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenizeWithdrawRequest", "inputs": [{"name": "_from", "type": "address", "internalType": "address"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "sharesAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "didTokenize", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "ApprovedVault", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "isApproved", "type": "bool", "indexed": true, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "InitiateWithdrawRequest", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "yieldTokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "sharesAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "requestId", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WithdrawRequestTokenized", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sharesAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ExistingWithdrawRequest", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "requestId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidWithdrawRequestTokenization", "inputs": []}, {"type": "error", "name": "NoWithdrawRequest", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"STAKING_TOKEN()": "0479d644", "WITHDRAW_TOKEN()": "3ed3a054", "YIELD_TOKEN()": "544bc96d", "canFinalizeWithdrawRequest(uint256)": "c2ded8c1", "finalizeAndRedeemWithdrawRequest(address,uint256,uint256)": "ed020beb", "finalizeRequestManual(address,address)": "a7b87572", "getWithdrawRequest(address,address)": "afbf911a", "getWithdrawRequestValue(address,address,address,uint256)": "32df6ff2", "initialize(bytes)": "439fab91", "initiateWithdraw(address,uint256,uint256,bytes)": "7c86cff5", "isApprovedVault(address)": "df78a625", "isPendingWithdrawRequest(address,address)": "37504d9c", "rescueTokens(address,address,address,uint256)": "d5fc623c", "setApprovedVault(address,bool)": "d665761a", "stakeTokens(address,uint256,bytes)": "e7c35c3c", "tokenizeWithdrawRequest(address,address,uint256)": "838f705b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"name\":\"ExistingWithdrawRequest\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidWithdrawRequestTokenization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"NoWithdrawRequest\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"isApproved\",\"type\":\"bool\"}],\"name\":\"ApprovedVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"yieldTokenAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"name\":\"InitiateWithdrawRequest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"}],\"name\":\"WithdrawRequestTokenized\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"STAKING_TOKEN\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"WITHDRAW_TOKEN\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"YIELD_TOKEN\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"name\":\"canFinalizeWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"withdrawYieldTokenAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesToBurn\",\"type\":\"uint256\"}],\"name\":\"finalizeAndRedeemWithdrawRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tokensWithdrawn\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"finalized\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"finalizeRequestManual\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tokensWithdrawn\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"finalized\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getWithdrawRequest\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"uint120\",\"name\":\"yieldTokenAmount\",\"type\":\"uint120\"},{\"internalType\":\"uint120\",\"name\":\"sharesAmount\",\"type\":\"uint120\"}],\"internalType\":\"struct WithdrawRequest\",\"name\":\"w\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint120\",\"name\":\"totalYieldTokenAmount\",\"type\":\"uint120\"},{\"internalType\":\"uint120\",\"name\":\"totalWithdraw\",\"type\":\"uint120\"},{\"internalType\":\"bool\",\"name\":\"finalized\",\"type\":\"bool\"}],\"internalType\":\"struct TokenizedWithdrawRequest\",\"name\":\"s\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"getWithdrawRequestValue\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"hasRequest\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"valueInAsset\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"yieldTokenAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isApprovedVault\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isPendingWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"cooldownHolder\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"rescueTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isApproved\",\"type\":\"bool\"}],\"name\":\"setApprovedVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"depositToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"stakeTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"yieldTokensMinted\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"}],\"name\":\"tokenizeWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"didTokenize\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"canFinalizeWithdrawRequest(uint256)\":{\"params\":{\"requestId\":\"the request id of the withdraw request\"},\"returns\":{\"_0\":\"canFinalize whether the withdraw request can be finalized\"}},\"finalizeAndRedeemWithdrawRequest(address,uint256,uint256)\":{\"params\":{\"account\":\"the account to finalize and redeem the withdraw request for\",\"sharesToBurn\":\"the amount of shares to burn for the yield token\",\"withdrawYieldTokenAmount\":\"the amount of yield tokens to withdraw\"},\"returns\":{\"finalized\":\"whether the withdraw request was finalized\",\"tokensWithdrawn\":\"amount of withdraw tokens redeemed from the withdraw requests\"}},\"finalizeRequestManual(address,address)\":{\"details\":\"No access control is enforced on this function but no tokens are transferred off the request manager either.\"},\"getWithdrawRequest(address,address)\":{\"params\":{\"account\":\"the account to get the withdraw request for\",\"vault\":\"the vault to get the withdraw request for\"},\"returns\":{\"s\":\"the tokenized withdraw request\",\"w\":\"the withdraw request\"}},\"getWithdrawRequestValue(address,address,address,uint256)\":{\"params\":{\"account\":\"the account to get the withdraw request for\",\"asset\":\"the asset to get the value for\",\"shares\":\"the amount of shares to get the value for\",\"vault\":\"the vault to get the withdraw request for\"},\"returns\":{\"hasRequest\":\"whether the account has a withdraw request\",\"valueInAsset\":\"the value of the withdraw request in terms of the asset\"}},\"initiateWithdraw(address,uint256,uint256,bytes)\":{\"details\":\"Only approved vaults can initiate withdraw requests\",\"params\":{\"account\":\"the account to initiate the withdraw request for\",\"data\":\"additional data for the withdraw request\",\"sharesAmount\":\"the amount of shares to withdraw, used to mark the shares to yield token ratio at the time of the withdraw request\",\"yieldTokenAmount\":\"the amount of yield tokens to withdraw\"},\"returns\":{\"requestId\":\"the request id of the withdraw request\"}},\"isPendingWithdrawRequest(address,address)\":{\"params\":{\"account\":\"the account to check the pending withdraw request for\",\"vault\":\"the vault to check the pending withdraw request for\"},\"returns\":{\"_0\":\"isPending whether the vault has a pending withdraw request\"}},\"rescueTokens(address,address,address,uint256)\":{\"params\":{\"amount\":\"the amount of tokens to rescue\",\"cooldownHolder\":\"the cooldown holder to rescue tokens from\",\"receiver\":\"the receiver of the rescued tokens\",\"token\":\"the token to rescue\"}},\"setApprovedVault(address,bool)\":{\"params\":{\"isApproved\":\"whether the vault is approved\",\"vault\":\"the vault to set the approval for\"}},\"stakeTokens(address,uint256,bytes)\":{\"details\":\"Only approved vaults can stake tokens\",\"params\":{\"amount\":\"the amount of tokens to stake\",\"data\":\"additional data for the stake\",\"depositToken\":\"the token to stake, will be transferred from the vault\"}},\"tokenizeWithdrawRequest(address,address,uint256)\":{\"details\":\"Only approved vaults can tokenize withdraw requests\",\"params\":{\"from\":\"the account that is being liquidated\",\"sharesAmount\":\"the amount of shares to the liquidator\",\"to\":\"the liquidator\"}}},\"stateVariables\":{\"STAKING_TOKEN\":{\"return\":\"stakingToken the staking token of the withdraw request manager\",\"returns\":{\"_0\":\"stakingToken the staking token of the withdraw request manager\"}},\"WITHDRAW_TOKEN\":{\"return\":\"withdrawToken the withdraw token of the withdraw request manager\",\"returns\":{\"_0\":\"withdrawToken the withdraw token of the withdraw request manager\"}},\"YIELD_TOKEN\":{\"return\":\"yieldToken the yield token of the withdraw request manager\",\"returns\":{\"_0\":\"yieldToken the yield token of the withdraw request manager\"}},\"isApprovedVault\":{\"params\":{\"vault\":\"the vault to check the approval for\"},\"return\":\"isApproved whether the vault is approved\",\"returns\":{\"_0\":\"isApproved whether the vault is approved\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"STAKING_TOKEN()\":{\"notice\":\"Returns the token that will be used to stake\"},\"WITHDRAW_TOKEN()\":{\"notice\":\"Returns the token that will be the result of the withdraw request\"},\"YIELD_TOKEN()\":{\"notice\":\"Returns the token that will be the result of staking\"},\"canFinalizeWithdrawRequest(uint256)\":{\"notice\":\"Returns whether a withdraw request can be finalized\"},\"finalizeAndRedeemWithdrawRequest(address,uint256,uint256)\":{\"notice\":\"Attempts to redeem active withdraw requests during vault exit\"},\"finalizeRequestManual(address,address)\":{\"notice\":\"Finalizes withdraw requests outside of a vault exit. This may be required in cases if an account is negligent in exiting their vault position and letting the withdraw request sit idle could result in losses. The withdraw request is finalized and stored in a tokenized withdraw request where the account has the full claim on the withdraw.\"},\"getWithdrawRequest(address,address)\":{\"notice\":\"Returns the withdraw request and tokenized withdraw request for an account\"},\"getWithdrawRequestValue(address,address,address,uint256)\":{\"notice\":\"Returns the value of a withdraw request in terms of the asset\"},\"initiateWithdraw(address,uint256,uint256,bytes)\":{\"notice\":\"Initiates a withdraw request\"},\"isApprovedVault(address)\":{\"notice\":\"Returns whether a vault is approved to initiate withdraw requests\"},\"isPendingWithdrawRequest(address,address)\":{\"notice\":\"Returns whether a vault has a pending withdraw request\"},\"rescueTokens(address,address,address,uint256)\":{\"notice\":\"Allows the emergency exit role to rescue tokens from the withdraw request manager\"},\"setApprovedVault(address,bool)\":{\"notice\":\"Sets whether a vault is approved to initiate withdraw requests\"},\"stakeTokens(address,uint256,bytes)\":{\"notice\":\"Stakes the deposit token to the yield token and transfers it back to the vault\"},\"tokenizeWithdrawRequest(address,address,uint256)\":{\"notice\":\"If an account has an illiquid withdraw request, this method will tokenize their claim on it during liquidation.\"}},\"notice\":\"Library to handle potentially illiquid withdraw requests of staking tokens where there is some indeterminate lock up time before tokens can be redeemed. Examples would be withdraws of staked or restaked ETH, tokens like sUSDe or stkAave which have cooldown periods before they can be withdrawn. Primarily, this library tracks the withdraw request and an associated identifier for the withdraw request. It also allows for the withdraw request to be \\\"tokenized\\\" so that shares of the withdraw request can be liquidated.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/withdraws/AbstractWithdrawRequestManager.sol\":\"AbstractWithdrawRequestManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]},\"src/withdraws/AbstractWithdrawRequestManager.sol\":{\"keccak256\":\"0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb\",\"dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu\"]},\"src/withdraws/ClonedCooldownHolder.sol\":{\"keccak256\":\"0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b\",\"dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "requestId", "type": "uint256"}], "type": "error", "name": "ExistingWithdrawRequest"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidWithdrawRequestTokenization"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "NoWithdrawRequest"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "bool", "name": "isApproved", "type": "bool", "indexed": true}], "type": "event", "name": "ApprovedVault", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "yieldTokenAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": false}], "type": "event", "name": "InitiateWithdrawRequest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "WithdrawRequestTokenized", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "STAKING_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "WITHDRAW_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "YIELD_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "canFinalizeWithdrawRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "withdrawYieldTokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "sharesToBurn", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeAndRedeemWithdrawRequest", "outputs": [{"internalType": "uint256", "name": "tokensWithdrawn", "type": "uint256"}, {"internalType": "bool", "name": "finalized", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeRequestManual", "outputs": [{"internalType": "uint256", "name": "tokensWithdrawn", "type": "uint256"}, {"internalType": "bool", "name": "finalized", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getWithdrawRequest", "outputs": [{"internalType": "struct WithdrawRequest", "name": "w", "type": "tuple", "components": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "uint120", "name": "yieldTokenAmount", "type": "uint120"}, {"internalType": "uint120", "name": "sharesAmount", "type": "uint120"}]}, {"internalType": "struct TokenizedWithdrawRequest", "name": "s", "type": "tuple", "components": [{"internalType": "uint120", "name": "totalYieldTokenAmount", "type": "uint120"}, {"internalType": "uint120", "name": "totalWithdraw", "type": "uint120"}, {"internalType": "bool", "name": "finalized", "type": "bool"}]}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getWithdrawRequestValue", "outputs": [{"internalType": "bool", "name": "hasRequest", "type": "bool"}, {"internalType": "uint256", "name": "valueInAsset", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "yieldTokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedVault", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isPendingWithdrawRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "cooldownHolder", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "rescueTokens"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bool", "name": "isApproved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApp<PERSON>Vault"}, {"inputs": [{"internalType": "address", "name": "depositToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "stakeTokens", "outputs": [{"internalType": "uint256", "name": "yieldTokensMinted", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "tokenizeWithdrawRequest", "outputs": [{"internalType": "bool", "name": "didTokenize", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"canFinalizeWithdrawRequest(uint256)": {"params": {"requestId": "the request id of the withdraw request"}, "returns": {"_0": "canFinalize whether the withdraw request can be finalized"}}, "finalizeAndRedeemWithdrawRequest(address,uint256,uint256)": {"params": {"account": "the account to finalize and redeem the withdraw request for", "sharesToBurn": "the amount of shares to burn for the yield token", "withdrawYieldTokenAmount": "the amount of yield tokens to withdraw"}, "returns": {"finalized": "whether the withdraw request was finalized", "tokensWithdrawn": "amount of withdraw tokens redeemed from the withdraw requests"}}, "finalizeRequestManual(address,address)": {"details": "No access control is enforced on this function but no tokens are transferred off the request manager either."}, "getWithdrawRequest(address,address)": {"params": {"account": "the account to get the withdraw request for", "vault": "the vault to get the withdraw request for"}, "returns": {"s": "the tokenized withdraw request", "w": "the withdraw request"}}, "getWithdrawRequestValue(address,address,address,uint256)": {"params": {"account": "the account to get the withdraw request for", "asset": "the asset to get the value for", "shares": "the amount of shares to get the value for", "vault": "the vault to get the withdraw request for"}, "returns": {"hasRequest": "whether the account has a withdraw request", "valueInAsset": "the value of the withdraw request in terms of the asset"}}, "initiateWithdraw(address,uint256,uint256,bytes)": {"details": "Only approved vaults can initiate withdraw requests", "params": {"account": "the account to initiate the withdraw request for", "data": "additional data for the withdraw request", "sharesAmount": "the amount of shares to withdraw, used to mark the shares to yield token ratio at the time of the withdraw request", "yieldTokenAmount": "the amount of yield tokens to withdraw"}, "returns": {"requestId": "the request id of the withdraw request"}}, "isPendingWithdrawRequest(address,address)": {"params": {"account": "the account to check the pending withdraw request for", "vault": "the vault to check the pending withdraw request for"}, "returns": {"_0": "isPending whether the vault has a pending withdraw request"}}, "rescueTokens(address,address,address,uint256)": {"params": {"amount": "the amount of tokens to rescue", "cooldownHolder": "the cooldown holder to rescue tokens from", "receiver": "the receiver of the rescued tokens", "token": "the token to rescue"}}, "setApprovedVault(address,bool)": {"params": {"isApproved": "whether the vault is approved", "vault": "the vault to set the approval for"}}, "stakeTokens(address,uint256,bytes)": {"details": "Only approved vaults can stake tokens", "params": {"amount": "the amount of tokens to stake", "data": "additional data for the stake", "depositToken": "the token to stake, will be transferred from the vault"}}, "tokenizeWithdrawRequest(address,address,uint256)": {"details": "Only approved vaults can tokenize withdraw requests", "params": {"from": "the account that is being liquidated", "sharesAmount": "the amount of shares to the liquidator", "to": "the liquidator"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"STAKING_TOKEN()": {"notice": "Returns the token that will be used to stake"}, "WITHDRAW_TOKEN()": {"notice": "Returns the token that will be the result of the withdraw request"}, "YIELD_TOKEN()": {"notice": "Returns the token that will be the result of staking"}, "canFinalizeWithdrawRequest(uint256)": {"notice": "Returns whether a withdraw request can be finalized"}, "finalizeAndRedeemWithdrawRequest(address,uint256,uint256)": {"notice": "Attempts to redeem active withdraw requests during vault exit"}, "finalizeRequestManual(address,address)": {"notice": "Finalizes withdraw requests outside of a vault exit. This may be required in cases if an account is negligent in exiting their vault position and letting the withdraw request sit idle could result in losses. The withdraw request is finalized and stored in a tokenized withdraw request where the account has the full claim on the withdraw."}, "getWithdrawRequest(address,address)": {"notice": "Returns the withdraw request and tokenized withdraw request for an account"}, "getWithdrawRequestValue(address,address,address,uint256)": {"notice": "Returns the value of a withdraw request in terms of the asset"}, "initiateWithdraw(address,uint256,uint256,bytes)": {"notice": "Initiates a withdraw request"}, "isApprovedVault(address)": {"notice": "Returns whether a vault is approved to initiate withdraw requests"}, "isPendingWithdrawRequest(address,address)": {"notice": "Returns whether a vault has a pending withdraw request"}, "rescueTokens(address,address,address,uint256)": {"notice": "Allows the emergency exit role to rescue tokens from the withdraw request manager"}, "setApprovedVault(address,bool)": {"notice": "Sets whether a vault is approved to initiate withdraw requests"}, "stakeTokens(address,uint256,bytes)": {"notice": "Stakes the deposit token to the yield token and transfers it back to the vault"}, "tokenizeWithdrawRequest(address,address,uint256)": {"notice": "If an account has an illiquid withdraw request, this method will tokenize their claim on it during liquidation."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/withdraws/AbstractWithdrawRequestManager.sol": "AbstractWithdrawRequestManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}, "src/withdraws/AbstractWithdrawRequestManager.sol": {"keccak256": "0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd", "urls": ["bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb", "dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu"], "license": "BUSL-1.1"}, "src/withdraws/ClonedCooldownHolder.sol": {"keccak256": "0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f", "urls": ["bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b", "dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM"], "license": "BUSL-1.1"}}, "version": 1}, "id": 100}