{"abi": [{"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "remaining", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "src", "type": "address", "internalType": "address"}, {"name": "dst", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"remaining\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"src\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Version of ERC20 with no return values for `transfer` and `transferFrom`  See https://medium.com/coinmonks/missing-return-value-bug-at-least-130-tokens-affected-d67bf08521ca\",\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"params\":{\"owner\":\"The address of the account which owns the tokens to be spent\",\"spender\":\"The address of the account which may transfer tokens\"},\"returns\":{\"remaining\":\"The number of tokens allowed to be spent\"}},\"approve(address,uint256)\":{\"details\":\"This will overwrite the approval amount for `spender`  and is subject to issues noted [here](https://eips.ethereum.org/EIPS/eip-20#approve)\",\"params\":{\"amount\":\"The number of tokens that are approved\",\"spender\":\"The address of the account which may transfer tokens\"}},\"balanceOf(address)\":{\"params\":{\"owner\":\"The address from which the balance will be retrieved\"},\"returns\":{\"balance\":\"balance\"}},\"totalSupply()\":{\"returns\":{\"_0\":\"The supply of tokens\"}},\"transfer(address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address of the destination account\"}},\"transferFrom(address,address,uint256)\":{\"params\":{\"amount\":\"The number of tokens to transfer\",\"dst\":\"The address of the destination account\",\"src\":\"The address of the source account\"}}},\"title\":\"EIP20NonStandardInterface\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"allowance(address,address)\":{\"notice\":\"Get the current allowance from `owner` for `spender`\"},\"approve(address,uint256)\":{\"notice\":\"Approve `spender` to transfer up to `amount` from `src`\"},\"balanceOf(address)\":{\"notice\":\"Gets the balance of the specified address\"},\"totalSupply()\":{\"notice\":\"Get the total number of tokens in circulation\"},\"transfer(address,uint256)\":{\"notice\":\"Transfer `amount` tokens from `msg.sender` to `dst`\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"Transfer `amount` tokens from `src` to `dst`\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IEIP20NonStandard.sol\":\"IEIP20NonStandard\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/IEIP20NonStandard.sol\":{\"keccak256\":\"0xcd73239cf684d4ee718e2c6cf9a380e298d21948cd1eaa1843e16b2ec1438e5a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4779a6b1fb6c29de8bc5f8139a2693b4ea93ad32698120f665cbd06094c36db9\",\"dweb:/ipfs/QmdVndD4AgxaS4Uivyt5GEuC4Bn51shw5EmFYnvHRzizq4\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "remaining", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer"}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom"}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"params": {"owner": "The address of the account which owns the tokens to be spent", "spender": "The address of the account which may transfer tokens"}, "returns": {"remaining": "The number of tokens allowed to be spent"}}, "approve(address,uint256)": {"details": "This will overwrite the approval amount for `spender`  and is subject to issues noted [here](https://eips.ethereum.org/EIPS/eip-20#approve)", "params": {"amount": "The number of tokens that are approved", "spender": "The address of the account which may transfer tokens"}}, "balanceOf(address)": {"params": {"owner": "The address from which the balance will be retrieved"}, "returns": {"balance": "balance"}}, "totalSupply()": {"returns": {"_0": "The supply of tokens"}}, "transfer(address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address of the destination account"}}, "transferFrom(address,address,uint256)": {"params": {"amount": "The number of tokens to transfer", "dst": "The address of the destination account", "src": "The address of the source account"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allowance(address,address)": {"notice": "Get the current allowance from `owner` for `spender`"}, "approve(address,uint256)": {"notice": "Approve `spender` to transfer up to `amount` from `src`"}, "balanceOf(address)": {"notice": "Gets the balance of the specified address"}, "totalSupply()": {"notice": "Get the total number of tokens in circulation"}, "transfer(address,uint256)": {"notice": "Transfer `amount` tokens from `msg.sender` to `dst`"}, "transferFrom(address,address,uint256)": {"notice": "Transfer `amount` tokens from `src` to `dst`"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/IEIP20NonStandard.sol": "IEIP20NonStandard"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/IEIP20NonStandard.sol": {"keccak256": "0xcd73239cf684d4ee718e2c6cf9a380e298d21948cd1eaa1843e16b2ec1438e5a", "urls": ["bzz-raw://4779a6b1fb6c29de8bc5f8139a2693b4ea93ad32698120f665cbd06094c36db9", "dweb:/ipfs/QmdVndD4AgxaS4Uivyt5GEuC4Bn51shw5EmFYnvHRzizq4"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 63}