{"abi": [{"type": "function", "name": "finalizeAndRedeemWithdrawRequest", "inputs": [{"name": "sharesOwner", "type": "address", "internalType": "address"}, {"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "exitBalances", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "withdrawTokens", "type": "address[]", "internalType": "contract ERC20[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getWithdrawRequestValue", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "totalValue", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "hasPendingWithdra<PERSON>s", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialApproveTokens", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "sharesHeld", "type": "uint256", "internalType": "uint256"}, {"name": "exitBalances", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "withdrawData", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [{"name": "requestIds", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "joinPoolAndStake", "inputs": [{"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "minPoolClaim", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenizeWithdrawRequest", "inputs": [{"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidator", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "didTokenize", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "unstakeAndExitPool", "inputs": [{"name": "poolClaim", "type": "uint256", "internalType": "uint256"}, {"name": "minAmounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "isSingleSided", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "exitBalances", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"finalizeAndRedeemWithdrawRequest(address,uint256)": "f7cd58c4", "getWithdrawRequestValue(address,address,uint256)": "c31f413c", "hasPendingWithdrawals(address)": "3159bc16", "initialApproveTokens()": "9e8fac86", "initiateWithdraw(address,uint256,uint256[],bytes[])": "27455546", "joinPoolAndStake(uint256[],uint256)": "6e0bd5e7", "tokenizeWithdrawRequest(address,address,uint256)": "838f705b", "unstakeAndExitPool(uint256,uint256[],bool)": "8f2ff74a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sharesOwner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToRedeem\",\"type\":\"uint256\"}],\"name\":\"finalizeAndRedeemWithdrawRequest\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"exitBalances\",\"type\":\"uint256[]\"},{\"internalType\":\"contract ERC20[]\",\"name\":\"withdrawTokens\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"getWithdrawRequestValue\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalValue\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasPendingWithdrawals\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initialApproveTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesHeld\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"exitBalances\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes[]\",\"name\":\"withdrawData\",\"type\":\"bytes[]\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"requestIds\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"minPoolClaim\",\"type\":\"uint256\"}],\"name\":\"joinPoolAndStake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidator\",\"type\":\"uint256\"}],\"name\":\"tokenizeWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"didTokenize\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"poolClaim\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"minAmounts\",\"type\":\"uint256[]\"},{\"internalType\":\"bool\",\"name\":\"isSingleSided\",\"type\":\"bool\"}],\"name\":\"unstakeAndExitPool\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"exitBalances\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Internal library for single-sided LPs, used to reduce the bytecode size of the main contract\",\"kind\":\"dev\",\"methods\":{\"finalizeAndRedeemWithdrawRequest(address,uint256)\":{\"details\":\"Finalizes a withdraw request and redeems the shares\"},\"getWithdrawRequestValue(address,address,uint256)\":{\"details\":\"Gets the value of all pending withdrawals\"},\"hasPendingWithdrawals(address)\":{\"details\":\"Checks if the account has pending withdrawals\"},\"initialApproveTokens()\":{\"details\":\"Approves the tokens needed for the pool, only called once during initialization\"},\"initiateWithdraw(address,uint256,uint256[],bytes[])\":{\"details\":\"Initiates a withdraw request\"},\"joinPoolAndStake(uint256[],uint256)\":{\"details\":\"Joins the pool and stakes the tokens\"},\"tokenizeWithdrawRequest(address,address,uint256)\":{\"details\":\"Tokenizes a withdraw request during liquidation\"},\"unstakeAndExitPool(uint256,uint256[],bool)\":{\"details\":\"Unstakes and exits the pool\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/ISingleSidedLP.sol\":\"ILPLib\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/ISingleSidedLP.sol\":{\"keccak256\":\"0xf90948287aaefb48273728c5065f5573ad36008bf828e0f3c2332a780bd110ff\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2a2769919b026afb6c66488d0520afb2ec639101b4981a1c279b9c95139157ab\",\"dweb:/ipfs/QmcWezqKeQe9cEqdZ3WLXG45xNDmsFLoWwQ7dn9jxbJrdf\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "sharesOwner", "type": "address"}, {"internalType": "uint256", "name": "sharesToRedeem", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeAndRedeemWithdrawRequest", "outputs": [{"internalType": "uint256[]", "name": "exitBalances", "type": "uint256[]"}, {"internalType": "contract ERC20[]", "name": "withdrawTokens", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getWithdrawRequestValue", "outputs": [{"internalType": "uint256", "name": "totalValue", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasPendingWithdra<PERSON>s", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "initialApproveTokens"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "sharesHeld", "type": "uint256"}, {"internalType": "uint256[]", "name": "exitBalances", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "withdrawData", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256[]", "name": "requestIds", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256", "name": "minPoolClaim", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "joinPoolAndStake"}, {"inputs": [{"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidator", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "tokenizeWithdrawRequest", "outputs": [{"internalType": "bool", "name": "didTokenize", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "poolClaim", "type": "uint256"}, {"internalType": "uint256[]", "name": "minAmounts", "type": "uint256[]"}, {"internalType": "bool", "name": "isSingleSided", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "unstakeAndExitPool", "outputs": [{"internalType": "uint256[]", "name": "exitBalances", "type": "uint256[]"}]}], "devdoc": {"kind": "dev", "methods": {"finalizeAndRedeemWithdrawRequest(address,uint256)": {"details": "Finalizes a withdraw request and redeems the shares"}, "getWithdrawRequestValue(address,address,uint256)": {"details": "Gets the value of all pending withdrawals"}, "hasPendingWithdrawals(address)": {"details": "Checks if the account has pending withdrawals"}, "initialApproveTokens()": {"details": "Approves the tokens needed for the pool, only called once during initialization"}, "initiateWithdraw(address,uint256,uint256[],bytes[])": {"details": "Initiates a withdraw request"}, "joinPoolAndStake(uint256[],uint256)": {"details": "Joins the pool and stakes the tokens"}, "tokenizeWithdrawRequest(address,address,uint256)": {"details": "Tokenizes a withdraw request during liquidation"}, "unstakeAndExitPool(uint256,uint256[],bool)": {"details": "Unstakes and exits the pool"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/ISingleSidedLP.sol": "ILPLib"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/ISingleSidedLP.sol": {"keccak256": "0xf90948287aaefb48273728c5065f5573ad36008bf828e0f3c2332a780bd110ff", "urls": ["bzz-raw://2a2769919b026afb6c66488d0520afb2ec639101b4981a1c279b9c95139157ab", "dweb:/ipfs/QmcWezqKeQe9cEqdZ3WLXG45xNDmsFLoWwQ7dn9jxbJrdf"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}}, "version": 1}, "id": 70}