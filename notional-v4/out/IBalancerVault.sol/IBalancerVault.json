{"abi": [{"type": "function", "name": "batchSwap", "inputs": [{"name": "kind", "type": "uint8", "internalType": "enum IBalancerVault.SwapKind"}, {"name": "swaps", "type": "tuple[]", "internalType": "struct IBalancerVault.BatchSwapStep[]", "components": [{"name": "poolId", "type": "bytes32", "internalType": "bytes32"}, {"name": "assetInIndex", "type": "uint256", "internalType": "uint256"}, {"name": "assetOutIndex", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}]}, {"name": "assets", "type": "address[]", "internalType": "contract IAsset[]"}, {"name": "funds", "type": "tuple", "internalType": "struct IBalancerVault.FundManagement", "components": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "fromInternalBalance", "type": "bool", "internalType": "bool"}, {"name": "recipient", "type": "address", "internalType": "address payable"}, {"name": "toInternalBalance", "type": "bool", "internalType": "bool"}]}, {"name": "limits", "type": "int256[]", "internalType": "int256[]"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "payable"}, {"type": "function", "name": "exitPool", "inputs": [{"name": "poolId", "type": "bytes32", "internalType": "bytes32"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address payable"}, {"name": "request", "type": "tuple", "internalType": "struct IBalancerVault.ExitPoolRequest", "components": [{"name": "assets", "type": "address[]", "internalType": "contract IAsset[]"}, {"name": "minAmountsOut", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}, {"name": "toInternalBalance", "type": "bool", "internalType": "bool"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "flashLoan", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "tokens", "type": "address[]", "internalType": "address[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getPool", "inputs": [{"name": "poolId", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint8", "internalType": "enum IBalancerVault.PoolSpecialization"}], "stateMutability": "view"}, {"type": "function", "name": "getPoolTokens", "inputs": [{"name": "poolId", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "tokens", "type": "address[]", "internalType": "address[]"}, {"name": "balances", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "lastChangeBlock", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "joinPool", "inputs": [{"name": "poolId", "type": "bytes32", "internalType": "bytes32"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "request", "type": "tuple", "internalType": "struct IBalancerVault.JoinPoolRequest", "components": [{"name": "assets", "type": "address[]", "internalType": "contract IAsset[]"}, {"name": "maxAmountsIn", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}, {"name": "fromInternalBalance", "type": "bool", "internalType": "bool"}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "manageUserBalance", "inputs": [{"name": "ops", "type": "tuple[]", "internalType": "struct IBalancerVault.UserBalanceOp[]", "components": [{"name": "kind", "type": "uint8", "internalType": "enum IBalancerVault.UserBalanceOpKind"}, {"name": "asset", "type": "address", "internalType": "contract IAsset"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address payable"}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "swap", "inputs": [{"name": "singleSwap", "type": "tuple", "internalType": "struct IBalancerVault.SingleSwap", "components": [{"name": "poolId", "type": "bytes32", "internalType": "bytes32"}, {"name": "kind", "type": "uint8", "internalType": "enum IBalancerVault.SwapKind"}, {"name": "assetIn", "type": "address", "internalType": "contract IAsset"}, {"name": "assetOut", "type": "address", "internalType": "contract IAsset"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "userData", "type": "bytes", "internalType": "bytes"}]}, {"name": "funds", "type": "tuple", "internalType": "struct IBalancerVault.FundManagement", "components": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "fromInternalBalance", "type": "bool", "internalType": "bool"}, {"name": "recipient", "type": "address", "internalType": "address payable"}, {"name": "toInternalBalance", "type": "bool", "internalType": "bool"}]}, {"name": "limit", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"batchSwap(uint8,(bytes32,uint256,uint256,uint256,bytes)[],address[],(address,bool,address,bool),int256[],uint256)": "945bcec9", "exitPool(bytes32,address,address,(address[],uint256[],bytes,bool))": "8bdb3913", "flashLoan(address,address[],uint256[],bytes)": "5c38449e", "getPool(bytes32)": "f6c00927", "getPoolTokens(bytes32)": "f94d4668", "joinPool(bytes32,address,address,(address[],uint256[],bytes,bool))": "b95cac28", "manageUserBalance((uint8,address,uint256,address,address)[])": "0e8e3e84", "swap((bytes32,uint8,address,address,uint256,bytes),(address,bool,address,bool),uint256,uint256)": "52bbbe29"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"enum IBalancerVault.SwapKind\",\"name\":\"kind\",\"type\":\"uint8\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"poolId\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"assetInIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"assetOutIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"userData\",\"type\":\"bytes\"}],\"internalType\":\"struct IBalancerVault.BatchSwapStep[]\",\"name\":\"swaps\",\"type\":\"tuple[]\"},{\"internalType\":\"contract IAsset[]\",\"name\":\"assets\",\"type\":\"address[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"fromInternalBalance\",\"type\":\"bool\"},{\"internalType\":\"address payable\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"toInternalBalance\",\"type\":\"bool\"}],\"internalType\":\"struct IBalancerVault.FundManagement\",\"name\":\"funds\",\"type\":\"tuple\"},{\"internalType\":\"int256[]\",\"name\":\"limits\",\"type\":\"int256[]\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"batchSwap\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"\",\"type\":\"int256[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"poolId\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address payable\",\"name\":\"recipient\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"contract IAsset[]\",\"name\":\"assets\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"minAmountsOut\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"userData\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"toInternalBalance\",\"type\":\"bool\"}],\"internalType\":\"struct IBalancerVault.ExitPoolRequest\",\"name\":\"request\",\"type\":\"tuple\"}],\"name\":\"exitPool\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"tokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"userData\",\"type\":\"bytes\"}],\"name\":\"flashLoan\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"poolId\",\"type\":\"bytes32\"}],\"name\":\"getPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"enum IBalancerVault.PoolSpecialization\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"poolId\",\"type\":\"bytes32\"}],\"name\":\"getPoolTokens\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"tokens\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"balances\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"lastChangeBlock\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"poolId\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"contract IAsset[]\",\"name\":\"assets\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"maxAmountsIn\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"userData\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"fromInternalBalance\",\"type\":\"bool\"}],\"internalType\":\"struct IBalancerVault.JoinPoolRequest\",\"name\":\"request\",\"type\":\"tuple\"}],\"name\":\"joinPool\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"enum IBalancerVault.UserBalanceOpKind\",\"name\":\"kind\",\"type\":\"uint8\"},{\"internalType\":\"contract IAsset\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address payable\",\"name\":\"recipient\",\"type\":\"address\"}],\"internalType\":\"struct IBalancerVault.UserBalanceOp[]\",\"name\":\"ops\",\"type\":\"tuple[]\"}],\"name\":\"manageUserBalance\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"poolId\",\"type\":\"bytes32\"},{\"internalType\":\"enum IBalancerVault.SwapKind\",\"name\":\"kind\",\"type\":\"uint8\"},{\"internalType\":\"contract IAsset\",\"name\":\"assetIn\",\"type\":\"address\"},{\"internalType\":\"contract IAsset\",\"name\":\"assetOut\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"userData\",\"type\":\"bytes\"}],\"internalType\":\"struct IBalancerVault.SingleSwap\",\"name\":\"singleSwap\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"fromInternalBalance\",\"type\":\"bool\"},{\"internalType\":\"address payable\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"toInternalBalance\",\"type\":\"bool\"}],\"internalType\":\"struct IBalancerVault.FundManagement\",\"name\":\"funds\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"swap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getPool(bytes32)\":{\"details\":\"Returns a Pool's contract address and specialization setting.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Balancer/IBalancerVault.sol\":\"IBalancerVault\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Balancer/IBalancerVault.sol\":{\"keccak256\":\"0x8304259ff5571b1ac06b3e981d4b299c9229d3cce8f93bd804250071c9e27ca6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ec32b8f669b9e90ef61a8d7d35c0eb71154603018a0451f065902f069711d286\",\"dweb:/ipfs/QmXuFaDRU9FLNz1KpBkGHpU3w8PvWnvNfFQoHKjuybTVng\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "enum IBalancerVault.SwapKind", "name": "kind", "type": "uint8"}, {"internalType": "struct IBalancerVault.BatchSwapStep[]", "name": "swaps", "type": "tuple[]", "components": [{"internalType": "bytes32", "name": "poolId", "type": "bytes32"}, {"internalType": "uint256", "name": "assetInIndex", "type": "uint256"}, {"internalType": "uint256", "name": "assetOutIndex", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}]}, {"internalType": "contract IAsset[]", "name": "assets", "type": "address[]"}, {"internalType": "struct IBalancerVault.FundManagement", "name": "funds", "type": "tuple", "components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bool", "name": "fromInternalBalance", "type": "bool"}, {"internalType": "address payable", "name": "recipient", "type": "address"}, {"internalType": "bool", "name": "toInternalBalance", "type": "bool"}]}, {"internalType": "int256[]", "name": "limits", "type": "int256[]"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "batchSwap", "outputs": [{"internalType": "int256[]", "name": "", "type": "int256[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "poolId", "type": "bytes32"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address payable", "name": "recipient", "type": "address"}, {"internalType": "struct IBalancerVault.ExitPoolRequest", "name": "request", "type": "tuple", "components": [{"internalType": "contract IAsset[]", "name": "assets", "type": "address[]"}, {"internalType": "uint256[]", "name": "minAmountsOut", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}, {"internalType": "bool", "name": "toInternalBalance", "type": "bool"}]}], "stateMutability": "nonpayable", "type": "function", "name": "exitPool"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "flashLoan"}, {"inputs": [{"internalType": "bytes32", "name": "poolId", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getPool", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "enum IBalancerVault.PoolSpecialization", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "bytes32", "name": "poolId", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getPoolTokens", "outputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "uint256[]", "name": "balances", "type": "uint256[]"}, {"internalType": "uint256", "name": "lastChangeBlock", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "poolId", "type": "bytes32"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "struct IBalancerVault.JoinPoolRequest", "name": "request", "type": "tuple", "components": [{"internalType": "contract IAsset[]", "name": "assets", "type": "address[]"}, {"internalType": "uint256[]", "name": "maxAmountsIn", "type": "uint256[]"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}, {"internalType": "bool", "name": "fromInternalBalance", "type": "bool"}]}], "stateMutability": "payable", "type": "function", "name": "joinPool"}, {"inputs": [{"internalType": "struct IBalancerVault.UserBalanceOp[]", "name": "ops", "type": "tuple[]", "components": [{"internalType": "enum IBalancerVault.UserBalanceOpKind", "name": "kind", "type": "uint8"}, {"internalType": "contract IAsset", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address payable", "name": "recipient", "type": "address"}]}], "stateMutability": "payable", "type": "function", "name": "manageUserBalance"}, {"inputs": [{"internalType": "struct IBalancerVault.SingleSwap", "name": "singleSwap", "type": "tuple", "components": [{"internalType": "bytes32", "name": "poolId", "type": "bytes32"}, {"internalType": "enum IBalancerVault.SwapKind", "name": "kind", "type": "uint8"}, {"internalType": "contract IAsset", "name": "assetIn", "type": "address"}, {"internalType": "contract IAsset", "name": "assetOut", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "userData", "type": "bytes"}]}, {"internalType": "struct IBalancerVault.FundManagement", "name": "funds", "type": "tuple", "components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bool", "name": "fromInternalBalance", "type": "bool"}, {"internalType": "address payable", "name": "recipient", "type": "address"}, {"internalType": "bool", "name": "toInternalBalance", "type": "bool"}]}, {"internalType": "uint256", "name": "limit", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "swap", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"getPool(bytes32)": {"details": "Returns a Pool's contract address and specialization setting."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Balancer/IBalancerVault.sol": "IBalancer<PERSON>ault"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Balancer/IBalancerVault.sol": {"keccak256": "0x8304259ff5571b1ac06b3e981d4b299c9229d3cce8f93bd804250071c9e27ca6", "urls": ["bzz-raw://ec32b8f669b9e90ef61a8d7d35c0eb71154603018a0451f065902f069711d286", "dweb:/ipfs/QmXuFaDRU9FLNz1KpBkGHpU3w8PvWnvNfFQoHKjuybTVng"], "license": "MIT"}}, "version": 1}, "id": 58}