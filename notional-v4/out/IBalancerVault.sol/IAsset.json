{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Balancer/IBalancerVault.sol\":\"IAsset\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Balancer/IBalancerVault.sol\":{\"keccak256\":\"0x8304259ff5571b1ac06b3e981d4b299c9229d3cce8f93bd804250071c9e27ca6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ec32b8f669b9e90ef61a8d7d35c0eb71154603018a0451f065902f069711d286\",\"dweb:/ipfs/QmXuFaDRU9FLNz1KpBkGHpU3w8PvWnvNfFQoHKjuybTVng\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Balancer/IBalancerVault.sol": "IAsset"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Balancer/IBalancerVault.sol": {"keccak256": "0x8304259ff5571b1ac06b3e981d4b299c9229d3cce8f93bd804250071c9e27ca6", "urls": ["bzz-raw://ec32b8f669b9e90ef61a8d7d35c0eb71154603018a0451f065902f069711d286", "dweb:/ipfs/QmXuFaDRU9FLNz1KpBkGHpU3w8PvWnvNfFQoHKjuybTVng"], "license": "MIT"}}, "version": 1}, "id": 58}