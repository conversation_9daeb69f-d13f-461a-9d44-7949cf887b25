{"abi": [{"type": "function", "name": "deposit", "inputs": [{"name": "_pid", "type": "uint256", "internalType": "uint256"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"deposit(uint256,uint256)": "e2bbb158"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_pid\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Curve/IConvex.sol\":\"IConvexBoosterArbitrum\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Curve/IConvex.sol\":{\"keccak256\":\"0x72201de544f9d37f7993fd296507092e1ce217a95367b55232264c7bb49b2127\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://519191f39f9f3c319a38fd898eba05a1d8a7c9fac580fb5d2c6da26cbae7dab6\",\"dweb:/ipfs/QmZdqAcDQjeWZtezKm1HaS1NefEcnY9566u7VPpZh2k7nb\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "_pid", "type": "uint256"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Curve/IConvex.sol": "IConvexBoosterArbitrum"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Curve/IConvex.sol": {"keccak256": "0x72201de544f9d37f7993fd296507092e1ce217a95367b55232264c7bb49b2127", "urls": ["bzz-raw://519191f39f9f3c319a38fd898eba05a1d8a7c9fac580fb5d2c6da26cbae7dab6", "dweb:/ipfs/QmZdqAcDQjeWZtezKm1HaS1NefEcnY9566u7VPpZh2k7nb"], "license": "MIT"}}, "version": 1}, "id": 59}