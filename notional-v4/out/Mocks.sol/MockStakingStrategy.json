{"abi": [{"type": "constructor", "inputs": [{"name": "_asset", "type": "address", "internalType": "address"}, {"name": "_yieldToken", "type": "address", "internalType": "address"}, {"name": "_feeRate", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowTransfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "currentAccount", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burnShares", "inputs": [{"name": "sharesOwner", "type": "address", "internalType": "address"}, {"name": "sharesToBurn", "type": "uint256", "internalType": "uint256"}, {"name": "sharesHeld", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "assetsWithdrawn", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "clear<PERSON><PERSON><PERSON>A<PERSON>unt", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "collectFees", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "convertSharesToYieldToken", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToAssets", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertYieldTokenToAsset", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertYieldTokenToShares", "inputs": [{"name": "yieldTokens", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "effectiveSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "feeRate", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "feesAccrued", "inputs": [], "outputs": [{"name": "feesAccruedInYieldToken", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "sharesHeld", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdrawNative", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintShares", "inputs": [{"name": "assetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "depositData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "sharesMinted", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "postLiquidation", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidator", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "preLiquidation", "inputs": [{"name": "liquidator", "type": "address", "internalType": "address"}, {"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidate", "type": "uint256", "internalType": "uint256"}, {"name": "accountSharesHeld", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "price", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "price", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "redeemNative", "inputs": [{"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "assetsWithdrawn", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transientVariables", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "yieldToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "VaultCreated", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "CannotEnterPosition", "inputs": []}, {"type": "error", "name": "CurrentAccountAlreadySet", "inputs": []}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InsufficientSharesHeld", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UnauthorizedLendingMarketTransfer", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "WithdrawRequestNotFinalized", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "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********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", "sourceMap": "8220:408:108:-:0;;;8274:147;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;558:55:96;;-1:-1:-1;;;558:55:96;;-1:-1:-1;;;;;765:32:121;;558:55:96;;;747:51:121;8387:6:108;;8395:11;;8408:8;;8387:6;;8395:11;;8408:8;;676:42:97;;558::96;;720:18:121;;558:55:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1300:6:92;1308:11;1321:8;1337:11;-1:-1:-1;;;;;1331:27:92;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1582:113:16;;;;;;;;;-1:-1:-1;1582:113:16;;;;;;;;;;;;;;213:18:83;;-1:-1:-1;;213:18:83;227:4;213:18;;;;1582:113:16;1648:5;:13;1582:113;1648:5;:13;:::i;:::-;-1:-1:-1;1671:7:16;:17;1681:7;1671;:17;:::i;:::-;-1:-1:-1;;;3352:18:54::1;::::0;;;-1:-1:-1;;;;;3380:23:54;;::::1;;::::0;3413:33;::::1;;::::0;3609:42:::1;::::0;::::1;;::::0;3678:30:::1;3396:6:::0;3678:22:::1;:30::i;:::-;3661:47;;;::::0;-1:-1:-1;;;;;;;;;1493:48:92;::::1;;::::0;;;1567:100:::1;;1665:1;1567:100;;;1615:22;;-1:-1:-1::0;;;;;1615:37:92::1;;:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1::0;;;;;1551:116:92::1;;::::0;-1:-1:-1;;638:13:96::1;540:1:97;638:33:96;::::0;-1:-1:-1;630:42:96::1;::::0;-1:-1:-1;630:42:96::1;;;;;427:252:::0;;;8274:147:108;;;8220:408;;336:229:98;395:14;-1:-1:-1;;;;;433:20:98;;;;:48;;-1:-1:-1;;;;;;457:24:98;;252:42:97;457:24:98;433:48;432:93;;508:5;-1:-1:-1;;;;;502:21:98;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;432:93;;;497:2;432:93;421:104;;555:2;543:8;:14;;;;535:23;;;;;;336:229;;;:::o;14:131:121:-;-1:-1:-1;;;;;89:31:121;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:446::-;238:6;246;254;307:2;295:9;286:7;282:23;278:32;275:52;;;323:1;320;313:12;275:52;355:9;349:16;374:31;399:5;374:31;:::i;:::-;474:2;459:18;;453:25;424:5;;-1:-1:-1;487:33:121;453:25;487:33;:::i;:::-;539:7;529:17;;;586:2;575:9;571:18;565:25;555:35;;150:446;;;;;:::o;809:284::-;912:6;965:2;953:9;944:7;940:23;936:32;933:52;;;981:1;978;971:12;933:52;1013:9;1007:16;1032:31;1057:5;1032:31;:::i;:::-;1082:5;809:284;-1:-1:-1;;;809:284:121:o;1098:273::-;1166:6;1219:2;1207:9;1198:7;1194:23;1190:32;1187:52;;;1235:1;1232;1225:12;1187:52;1267:9;1261:16;1317:4;1310:5;1306:16;1299:5;1296:27;1286:55;;1337:1;1334;1327:12;1376:127;1437:10;1432:3;1428:20;1425:1;1418:31;1468:4;1465:1;1458:15;1492:4;1489:1;1482:15;1508:380;1587:1;1583:12;;;;1630;;;1651:61;;1705:4;1697:6;1693:17;1683:27;;1651:61;1758:2;1750:6;1747:14;1727:18;1724:38;1721:161;;1804:10;1799:3;1795:20;1792:1;1785:31;1839:4;1836:1;1829:15;1867:4;1864:1;1857:15;1721:161;;1508:380;;;:::o;2019:518::-;2121:2;2116:3;2113:11;2110:421;;;2157:5;2154:1;2147:16;2201:4;2198:1;2188:18;2271:2;2259:10;2255:19;2252:1;2248:27;2242:4;2238:38;2307:4;2295:10;2292:20;2289:47;;;-1:-1:-1;2330:4:121;2289:47;2385:2;2380:3;2376:12;2373:1;2369:20;2363:4;2359:31;2349:41;;2440:81;2458:2;2451:5;2448:13;2440:81;;;2517:1;2503:16;;2484:1;2473:13;2440:81;;;2444:3;;2110:421;2019:518;;;:::o;2713:1299::-;2833:10;;-1:-1:-1;;;;;2855:30:121;;2852:56;;;2888:18;;:::i;:::-;2917:97;3007:6;2967:38;2999:4;2993:11;2967:38;:::i;:::-;2961:4;2917:97;:::i;:::-;3063:4;3094:2;3083:14;;3111:1;3106:649;;;;3799:1;3816:6;3813:89;;;-1:-1:-1;3868:19:121;;;3862:26;3813:89;-1:-1:-1;;2670:1:121;2666:11;;;2662:24;2658:29;2648:40;2694:1;2690:11;;;2645:57;3915:81;;3076:930;;3106:649;1966:1;1959:14;;;2003:4;1990:18;;-1:-1:-1;;3142:20:121;;;3260:222;3274:7;3271:1;3268:14;3260:222;;;3356:19;;;3350:26;3335:42;;3463:4;3448:20;;;;3416:1;3404:14;;;;3290:12;3260:222;;;3264:3;3510:6;3501:7;3498:19;3495:201;;;3571:19;;;3565:26;-1:-1:-1;;3654:1:121;3650:14;;;3666:3;3646:24;3642:37;3638:42;3623:58;3608:74;;3495:201;-1:-1:-1;;;;3742:1:121;3726:14;;;3722:22;3709:36;;-1:-1:-1;2713:1299:121:o;4017:251::-;8220:408:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b506004361061021b575f3560e01c80638fc4709311610123578063aea91078116100b8578063c879657211610088578063dd62ed3e1161006e578063dd62ed3e146104c4578063e0b4327d146104fc578063eb9b191214610504575f5ffd5b8063c8796572146104a9578063cc351ac5146104b1575f5ffd5b8063aea9107814610468578063b35cb45d1461047b578063b905a4ff14610483578063c6e6f59214610496575f5ffd5b806398476c2b116100f357806398476c2b1461042757806398dce16d1461043a578063a035b1fe1461044d578063a9059cbb14610455575f5ffd5b80638fc47093146103e857806394db0595146103f057806395d89b41146103f8578063978bbdb914610400575f5ffd5b806323b872dd116101b3578063439fab9111610183578063616252b611610169578063616252b61461036257806370a082311461039957806376d5de85146103c1575f5ffd5b8063439fab911461033a57806357831a041461034f575f5ffd5b806323b872dd146102c6578063********146102d9578063313ce567146102ec57806338d52e0f146102fb575f5ffd5b80630db734d4116101ee5780630db734d414610285578063127af7f914610298578063131b822d146102ab57806318160ddd146102be575f5ffd5b806301e1d1141461021f57806306fdde031461023a57806307a2d13a1461024f578063095ea7b314610262575b5f5ffd5b610227610517565b6040519081526020015b60405180910390f35b610242610529565b604051610231919061333a565b61022761025d36600461334c565b6105b9565b61027561027036600461337a565b6106e3565b6040519015158152602001610231565b6102276102933660046133e2565b6106fa565b6102276102a6366004613447565b610921565b6102276102b93660046135dd565b610bca565b600354610227565b6102756102d436600461360f565b610be5565b6102276102e736600461334c565b610c0a565b60405160128152602001610231565b6103227f000000000000000000000000000000000000000000000000000000000000000081565b6040516001600160a01b039091168152602001610231565b61034d61034836600461364d565b610c4d565b005b61022761035d36600461368c565b610cc1565b604080516001600160a01b035f5c8116825260015c8116602083015260025c169181019190915260035c6060820152608001610231565b6102276103a73660046136cc565b6001600160a01b03165f9081526001602052604090205490565b6103227f000000000000000000000000000000000000000000000000000000000000000081565b610227610e97565b610227610ebc565b610242610ed2565b6102277f000000000000000000000000000000000000000000000000000000000000000081565b61034d6104353660046136e7565b610ee1565b61034d61044836600461360f565b6110a0565b610227611217565b61027561046336600461337a565b61123f565b6102276104763660046136cc565b61124c565b61034d6112d7565b61022761049136600461334c565b6113f9565b6102276104a436600461334c565b611428565b61034d6114bf565b61034d6104bf366004613726565b61154c565b6102276104d2366004613769565b6001600160a01b039182165f90815260026020908152604080832093909416825291909152205490565b610227611747565b6102276105123660046137a0565b61182c565b5f61052461025d60035490565b905090565b606060068054610538906137e4565b80601f0160208091040260200160405190810160405280929190818152602001828054610564906137e4565b80156105af5780601f10610586576101008083540402835291602001916105af565b820191905f5260205f20905b81548152906001019060200180831161059257829003601f168201915b5050505050905090565b5f805c6001600160a01b0316158015906105e157506105e16001600160a01b035f5c166118c9565b156106d4576040517f32df6ff20000000000000000000000000000000000000000000000000000000081523060048201526001600160a01b035f805c821660248401527f000000000000000000000000000000000000000000000000000000000000000082166044840152606483018590529182917f000000000000000000000000000000000000000000000000000000000000000016906332df6ff2906084016040805180830381865afa15801561069c573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906106c09190613849565b9150915081156106d1579392505050565b50505b6106dd826119a4565b92915050565b5f336106f0818585611a3b565b5060019392505050565b6040517f46f74dce0000000000000000000000000000000000000000000000000000000081523360048201525f90735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa158015610762573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906107869190613873565b15155f036107c7576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024015b60405180910390fd5b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50855f5c6001600160a01b031661082657805f805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d5061086d565b5f5c6001600160a01b039081169082161461086d576040517f391fea3c00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b610875611a4d565b6108b7868686868080601f0160208091040260200160405190810160405280939291908181526020018383808284375f920191909152508d9250611ad5915050565b91506108f26001600160a01b037f000000000000000000000000000000000000000000000000000000000000000081169060015c1684611b9b565b6108fa611c0f565b506001805c73ffffffffffffffffffffffffffffffffffffffff1916905d95945050505050565b6040517f46f74dce0000000000000000000000000000000000000000000000000000000081523360048201525f90735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa158015610989573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906109ad9190613873565b15155f036109e9576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024016107be565b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50835f5c6001600160a01b0316610a4857805f805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d50610a8f565b5f5c6001600160a01b0390811690821614610a8f576040517f391fea3c00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b610a97611a4d565b610aa0856118c9565b15610ad7576040517f1bb8b5b100000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b610b116001600160a01b037f000000000000000000000000000000000000000000000000000000000000000081169060015c163089611c39565b610b528685858080601f0160208091040260200160405190810160405280939291908181526020018383808284375f920191909152508a9250611c78915050565b915060015c6001600160a01b03166002805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50818060035d50610b9c856001600160a01b0360015c1684611d0a565b610ba4611c0f565b506001805c73ffffffffffffffffffffffffffffffffffffffff1916905d949350505050565b335f8181526001602052604081205490916106dd9184611d99565b5f33610bf2858285611e14565b610bfd858585611d0a565b60019150505b9392505050565b5f6001610c15610ebc565b610c1d611ea3565b610c2791906138b9565b610c3191906138cc565b610c39610e97565b610c4390846138df565b6106dd91906138f6565b5f5460ff1615610c89576040517ff92ee8a900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b5f80547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00166001179055610cbd8282611f44565b5050565b6040517f46f74dce0000000000000000000000000000000000000000000000000000000081523360048201525f90735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa158015610d29573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610d4d9190613873565b15155f03610d89576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024016107be565b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50845f5c6001600160a01b0316610de857805f805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d50610e2f565b5f5c6001600160a01b0390811690821614610e2f576040517f391fea3c00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b610e6f868686868080601f0160208091040260200160405190810160405280939291908181526020018383808284375f92019190915250611d9992505050565b9150506001805c73ffffffffffffffffffffffffffffffffffffffff1916905d949350505050565b5f620f4240600a54610ea860035490565b610eb291906138b9565b61052491906138cc565b5f610ec5611fd1565b60095461052491906138cc565b606060078054610538906137e4565b805f5c6001600160a01b0316610f1f57805f805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d50610f66565b5f5c6001600160a01b0390811690821614610f66576040517f391fea3c00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6040517f46f74dce000000000000000000000000000000000000000000000000000000008152336004820152735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa158015610fcc573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610ff09190613873565b15155f0361102c576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024016107be565b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50836002805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d50828060035d506001805c73ffffffffffffffffffffffffffffffffffffffff1916905d50505050565b6040517f46f74dce000000000000000000000000000000000000000000000000000000008152336004820152735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa158015611106573d5f5f3e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061112a9190613873565b15155f03611166576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024016107be565b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50826002805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d50808060035d506111cd6001600160a01b0360015c168483611d0a565b6111d883838361210e565b505f805c73ffffffffffffffffffffffffffffffffffffffff1916905d6001805c73ffffffffffffffffffffffffffffffffffffffff1916905d505050565b5f61123061025d620f4240670de0b6b3a76400006138df565b6105249064e8d4a510006138df565b5f336106f0818585611d0a565b5f6001600160a01b03815c81811691849190821673ffffffffffffffffffffffffffffffffffffffff1990911617835d505f61129661025d620f4240670de0b6b3a76400006138df565b6112a59064e8d4a510006138df565b9050815f805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d509392505050565b6040517f46f74dce000000000000000000000000000000000000000000000000000000008152336004820152735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa15801561133d573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906113619190613873565b15155f0361139d576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024016107be565b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d505f805c73ffffffffffffffffffffffffffffffffffffffff1916905d6001805c73ffffffffffffffffffffffffffffffffffffffff1916905d565b5f611402610e97565b600161140c610ebc565b611414611ea3565b61141e91906138b9565b610c3991906138cc565b5f806114557f0000000000000000000000000000000000000000000000000000000000000000600a613a11565b61145d611747565b61146791906138df565b611495601260ff7f0000000000000000000000000000000000000000000000000000000000000000166138cc565b6114a090600a613a1f565b6114aa90856138df565b6114b491906138f6565b9050610c0381610c0a565b6114c76121ef565b611546735615deb798bb3e4dfa0139dfa1b3d433cc23b72f6001600160a01b031663b3f006746040518163ffffffff1660e01b8152600401602060405180830381865afa15801561151a573d5f5f3e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061153e9190613a2a565b600954612251565b5f600955565b6040517f46f74dce000000000000000000000000000000000000000000000000000000008152336004820152735615deb798bb3e4dfa0139dfa1b3d433cc23b72f906346f74dce90602401602060405180830381865afa1580156115b2573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906115d69190613873565b15155f03611612576040517f8e4a23d60000000000000000000000000000000000000000000000000000000081523360048201526024016107be565b336001805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50825f805c73ffffffffffffffffffffffffffffffffffffffff19166001600160a01b03831617905d50611665846118c9565b1561169c576040517f1bb8b5b100000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6116a5836118c9565b80156116c657506001600160a01b0384165f90815260016020526040812054115b156116fd576040517f1bb8b5b100000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b336002805c73ffffffffffffffffffffffffffffffffffffffff19168217905d50818060035d506001805c73ffffffffffffffffffffffffffffffffffffffff1916905d50505050565b6040517f4c2d8eff0000000000000000000000000000000000000000000000000000000081526001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000811660048301527f00000000000000000000000000000000000000000000000000000000000000001660248201525f90819073594734c7e06c3d483466adbce401c6bd269746c890634c2d8eff906044016040805180830381865afa158015611801573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906118259190613a45565b5092915050565b5f611835611a4d565b335f908152600160205260408120549081900361187e576040517f3a513ebe00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b61188a84828533611ad5565b91506118c06001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000163384611b9b565b506106dd611c0f565b5f7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316158015906106dd57506040517f37504d9c0000000000000000000000000000000000000000000000000000000081523060048201526001600160a01b0383811660248301527f000000000000000000000000000000000000000000000000000000000000000016906337504d9c90604401602060405180830381865afa158015611980573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906106dd9190613873565b5f5f6119af836113f9565b90506119df601260ff7f0000000000000000000000000000000000000000000000000000000000000000166138cc565b6119ea90600a613a1f565b611a157f0000000000000000000000000000000000000000000000000000000000000000600a613a11565b611a1d611747565b611a2790846138df565b611a3191906138df565b610c0391906138f6565b611a488383836001612285565b505050565b7f9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f005c15611aa6576040517f3ee5aeb500000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b611ad360017f9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f005b90612389565b565b5f845f03611ae457505f611b93565b5f611aee836118c9565b90505f611b1a7f0000000000000000000000000000000000000000000000000000000000000000612390565b9050611b246121ef565b611b308785848861242b565b8115611b4d5786600a5f828254611b4791906138b9565b90915550505b5f611b777f0000000000000000000000000000000000000000000000000000000000000000612390565b9050611b8382826138b9565b9350611b8f8589612767565b5050505b949350505050565b6040516001600160a01b03838116602483015260448201839052611a4891859182169063a9059cbb906064015b604051602081830303815290604052915060e01b6020820180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff83818316178352505050506127b4565b611ad35f7f9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00611acd565b6040516001600160a01b038481166024830152838116604483015260648201839052611c729186918216906323b872dd90608401611bc8565b50505050565b5f835f03611c8757505f610c03565b611c8f6121ef565b5f611c98611ea3565b9050611ca5858486612839565b5f81611caf611ea3565b611cb991906138b9565b90506001611cc5610ebc565b611ccf90846138b9565b611cd991906138cc565b611ce1610e97565b611ceb90836138df565b611cf591906138f6565b9250611d0184846129cc565b50509392505050565b6001600160a01b038316611d4c576040517f96c6fd1e0000000000000000000000000000000000000000000000000000000081525f60048201526024016107be565b6001600160a01b038216611d8e576040517fec442f050000000000000000000000000000000000000000000000000000000081525f60048201526024016107be565b611a48838383612a15565b5f825f03611dd3576040517f3a513ebe00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b611ddb6121ef565b5f611de5846113f9565b9050611df385828686612b1f565b915083600a5f828254611e0691906138cc565b909155509195945050505050565b6001600160a01b038381165f908152600260209081526040808320938616835292905220545f19811015611c725781811015611e95576040517ffb8f41b20000000000000000000000000000000000000000000000000000000081526001600160a01b038416600482015260248101829052604481018390526064016107be565b611c7284848484035f612285565b6040517f70a082310000000000000000000000000000000000000000000000000000000081523060048201525f907f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316906370a0823190602401602060405180830381865afa158015611f20573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906105249190613a67565b5f80611f5283850185613a7e565b90925090506006611f638382613b18565b506007611f708282613b18565b50600880547fffffffffffffffffffffffffffffffffffffffffffffffffffffffff00000000164263ffffffff1617905560405130907f2e49a5810fda2439a23771848b4acec786b0a64d4de3d124ac57fc8739c38875905f90a250505050565b6008545f908190611fe89063ffffffff16426138b9565b90505f6301e1338061201a837f00000000000000000000000000000000000000000000000000000000000000006138df565b61202491906138f6565b9050805f03612035575f9250505090565b5f600954612041611ea3565b61204b91906138b9565b90505f670de0b6b3a76400006120628160066138df565b61206c91906138df565b8361207781806138df565b61208191906138df565b61208b91906138f6565b61209e670de0b6b3a764000060026138df565b6120a885806138df565b6120b291906138f6565b6120c485670de0b6b3a76400006138cc565b6120ce91906138cc565b6120d891906138cc565b90505f816120ee670de0b6b3a7640000856138df565b6120f891906138f6565b905061210481846138b9565b9550505050505090565b5f7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031615610c03576040517f838f705b0000000000000000000000000000000000000000000000000000000081526001600160a01b0384811660048301528581166024830152604482018490527f0000000000000000000000000000000000000000000000000000000000000000169063838f705b906064016020604051808303815f875af11580156121cb573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190611b939190613873565b6008544263ffffffff9091160361220257565b61220a611fd1565b60095f82825461221a91906138cc565b9091555050600880547fffffffffffffffffffffffffffffffffffffffffffffffffffffffff00000000164263ffffffff16179055565b610cbd6001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000168383611b9b565b6001600160a01b0384166122c7576040517fe602df050000000000000000000000000000000000000000000000000000000081525f60048201526024016107be565b6001600160a01b038316612309576040517f94280d620000000000000000000000000000000000000000000000000000000081525f60048201526024016107be565b6001600160a01b038085165f9081526002602090815260408083209387168352929052208290558015611c7257826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b9258460405161237b91815260200190565b60405180910390a350505050565b80825d5050565b5f6001600160a01b03821615612424576040517f70a082310000000000000000000000000000000000000000000000000000000081523060048201526001600160a01b038316906370a0823190602401602060405180830381865afa1580156123fb573d5f5f3e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061241f9190613a67565b6106dd565b4792915050565b8115612749576040517fafbf911a0000000000000000000000000000000000000000000000000000000081523060048201526001600160a01b0384811660248301525f917f00000000000000000000000000000000000000000000000000000000000000009091169063afbf911a9060440160c060405180830381865afa1580156124b8573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906124dc9190613c0f565b5090505f81604001516effffffffffffffffffffffffffffff168683602001516effffffffffffffffffffffffffffff1661251791906138df565b61252191906138f6565b6040517fed020beb0000000000000000000000000000000000000000000000000000000081526001600160a01b03878116600483015260248201839052604482018990529192505f9182917f00000000000000000000000000000000000000000000000000000000000000009091169063ed020beb9060640160408051808303815f875af11580156125b5573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906125d99190613cd1565b915091508061261a5783516040517f4ccb7bc200000000000000000000000000000000000000000000000000000000815260048101919091526024016107be565b7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03167f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031614612740575f858060200190518101906126889190613cfb565b90505f6040518060e001604052805f60048111156126a8576126a8613dc7565b81526020017f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031681526020017f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602001858152602001836020015181526020014281526020018360400151815250905061273b81835f015160ff16612ca1565b505050505b50505050611c72565b5f612753856113f9565b905061275f8183612f49565b505050505050565b6001600160a01b0382166127a9576040517f96c6fd1e0000000000000000000000000000000000000000000000000000000081525f60048201526024016107be565b610cbd825f83612a15565b5f5f60205f8451602086015f885af1806127d3576040513d5f823e3d81fd5b50505f513d915081156127ea5780600114156127f7565b6001600160a01b0384163b155b15611c72576040517f5274afe70000000000000000000000000000000000000000000000000000000081526001600160a01b03851660048201526024016107be565b6040517f095ea7b30000000000000000000000000000000000000000000000000000000081526001600160a01b037f000000000000000000000000000000000000000000000000000000000000000081166004830152602482018590527f0000000000000000000000000000000000000000000000000000000000000000169063095ea7b3906044016020604051808303815f875af11580156128de573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906129029190613873565b506040517fe7c35c3c0000000000000000000000000000000000000000000000000000000081526001600160a01b037f0000000000000000000000000000000000000000000000000000000000000000169063e7c35c3c9061298c907f00000000000000000000000000000000000000000000000000000000000000009087908690600401613df4565b6020604051808303815f875af11580156129a8573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190611c729190613a67565b6001600160a01b038216612a0e576040517fec442f050000000000000000000000000000000000000000000000000000000081525f60048201526024016107be565b610cbd5f83835b6001600160a01b03831615801590612a3557506001600160a01b03821615155b15612b145760025c6001600160a01b0390811690831614612a9c576040517ffd351fc80000000000000000000000000000000000000000000000000000000081526001600160a01b03808516600483015283166024820152604481018290526064016107be565b8060035c1015612af2576040517ffd351fc80000000000000000000000000000000000000000000000000000000081526001600160a01b03808516600483015283166024820152604481018290526064016107be565b6002805c73ffffffffffffffffffffffffffffffffffffffff1916905d5f60035d5b611a4883838361301c565b6040517f095ea7b30000000000000000000000000000000000000000000000000000000081526001600160a01b037f000000000000000000000000000000000000000000000000000000000000000081166004830152602482018590525f917f00000000000000000000000000000000000000000000000000000000000000009091169063095ea7b3906044016020604051808303815f875af1158015612bc8573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190612bec9190613873565b506040517f7c86cff50000000000000000000000000000000000000000000000000000000081526001600160a01b037f00000000000000000000000000000000000000000000000000000000000000001690637c86cff590612c58908890889088908890600401613e1b565b6020604051808303815f875af1158015612c74573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190612c989190613a67565b95945050505050565b5f80600484516004811115612cb857612cb8613dc7565b03612e0b5760408481015190517f9287d7ac0000000000000000000000000000000000000000000000000000000081526001600160a01b0390911660048201525f90735615deb798bb3e4dfa0139dfa1b3d433cc23b72f90639287d7ac90602401602060405180830381865afa158015612d34573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190612d589190613a2a565b9050612d8081866060015187602001516001600160a01b031661315b9092919063ffffffff16565b806001600160a01b031663e7c35c3c866020015187606001518860c001516040518463ffffffff1660e01b8152600401612dbc93929190613df4565b6020604051808303815f875af1158015612dd8573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190612dfc9190613a67565b91508460600151925050612f42565b5f73594734c7e06c3d483466adbce401c6bd269746c86001600160a01b031663aaf10f426040518163ffffffff1660e01b8152600401602060405180830381865afa158015612e5c573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190612e809190613a2a565b90505f612f2482632ba8c23c60e01b8789604051602401612ea2929190613e48565b604080517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe08184030181529190526020810180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff167fffffffff0000000000000000000000000000000000000000000000000000000090931692909217909152613182565b905080806020019051810190612f3a9190613a45565b909450925050505b9250929050565b5f5f82806020019051810190612f5f9190613cfb565b90505f6040518060e001604052805f6004811115612f7f57612f7f613dc7565b81526020017f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031681526020017f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602001868152602001836020015181526020014281526020018360400151815250905061301281835f015160ff16612ca1565b9695505050505050565b6001600160a01b038316613046578060035f82825461303b91906138cc565b909155506130cf9050565b6001600160a01b0383165f90815260016020526040902054818110156130b1576040517fe450d38c0000000000000000000000000000000000000000000000000000000081526001600160a01b038516600482015260248101829052604481018390526064016107be565b6001600160a01b0384165f9081526001602052604090209082900390555b6001600160a01b0382166130eb57600380548290039055613109565b6001600160a01b0382165f9081526001602052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef8360405161314e91815260200190565b60405180910390a3505050565b6001600160a01b03831661316e57505050565b611a486001600160a01b03841683836131ec565b60605f836001600160a01b03168360405161319d9190613ef6565b5f60405180830381855af49150503d805f81146131d5576040519150601f19603f3d011682016040523d82523d5f602084013e6131da565b606091505b509250905080611825573d5f5f3e3d5ffd5b604080516001600160a01b038416602482015260448082018490528251808303909101815260649091019091526020810180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff167f095ea7b30000000000000000000000000000000000000000000000000000000017905261326b84826132a9565b611c72576040516001600160a01b0384811660248301525f604483015261329f91869182169063095ea7b390606401611bc8565b611c7284826127b4565b5f5f5f5f60205f8651602088015f8a5af192503d91505f519050828015613012575081156132da5780600114613012565b50505050506001600160a01b03163b151590565b5f81518084528060208401602086015e5f6020828601015260207fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f83011685010191505092915050565b602081525f610c0360208301846132ee565b5f6020828403121561335c575f5ffd5b5035919050565b6001600160a01b0381168114613377575f5ffd5b50565b5f5f6040838503121561338b575f5ffd5b823561339681613363565b946020939093013593505050565b5f5f83601f8401126133b4575f5ffd5b50813567ffffffffffffffff8111156133cb575f5ffd5b602083019150836020828501011115612f42575f5ffd5b5f5f5f5f5f608086880312156133f6575f5ffd5b853561340181613363565b94506020860135935060408601359250606086013567ffffffffffffffff81111561342a575f5ffd5b613436888289016133a4565b969995985093965092949392505050565b5f5f5f5f6060858703121561345a575f5ffd5b84359350602085013561346c81613363565b9250604085013567ffffffffffffffff811115613487575f5ffd5b613493878288016133a4565b95989497509550505050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52604160045260245ffd5b6040516060810167ffffffffffffffff811182821017156134ef576134ef61349f565b60405290565b604051601f82017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe016810167ffffffffffffffff8111828210171561353c5761353c61349f565b604052919050565b5f67ffffffffffffffff82111561355d5761355d61349f565b50601f017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe01660200190565b5f82601f830112613598575f5ffd5b8135602083015f6135b06135ab84613544565b6134f5565b90508281528583830111156135c3575f5ffd5b828260208301375f92810160200192909252509392505050565b5f602082840312156135ed575f5ffd5b813567ffffffffffffffff811115613603575f5ffd5b611b9384828501613589565b5f5f5f60608486031215613621575f5ffd5b833561362c81613363565b9250602084013561363c81613363565b929592945050506040919091013590565b5f5f6020838503121561365e575f5ffd5b823567ffffffffffffffff811115613674575f5ffd5b613680858286016133a4565b90969095509350505050565b5f5f5f5f6060858703121561369f575f5ffd5b84356136aa81613363565b935060208501359250604085013567ffffffffffffffff811115613487575f5ffd5b5f602082840312156136dc575f5ffd5b8135610c0381613363565b5f5f5f606084860312156136f9575f5ffd5b833561370481613363565b925060208401359150604084013561371b81613363565b809150509250925092565b5f5f5f5f60808587031215613739575f5ffd5b843561374481613363565b9350602085013561375481613363565b93969395505050506040820135916060013590565b5f5f6040838503121561377a575f5ffd5b823561378581613363565b9150602083013561379581613363565b809150509250929050565b5f5f604083850312156137b1575f5ffd5b82359150602083013567ffffffffffffffff8111156137ce575f5ffd5b6137da85828601613589565b9150509250929050565b600181811c908216806137f857607f821691505b60208210810361382f577f4e487b71000000000000000000000000000000000000000000000000000000005f52602260045260245ffd5b50919050565b80518015158114613844575f5ffd5b919050565b5f5f6040838503121561385a575f5ffd5b61386383613835565b6020939093015192949293505050565b5f60208284031215613883575f5ffd5b610c0382613835565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b818103818111156106dd576106dd61388c565b808201808211156106dd576106dd61388c565b80820281158282048414176106dd576106dd61388c565b5f82613929577f4e487b71000000000000000000000000000000000000000000000000000000005f52601260045260245ffd5b500490565b6001815b60018411156139695780850481111561394d5761394d61388c565b600184161561395b57908102905b60019390931c928002613932565b935093915050565b5f8261397f575060016106dd565b8161398b57505f6106dd565b81600181146139a157600281146139ab576139c7565b60019150506106dd565b60ff8411156139bc576139bc61388c565b50506001821b6106dd565b5060208310610133831016604e8410600b84101617156139ea575081810a6106dd565b6139f65f19848461392e565b805f1904821115613a0957613a0961388c565b029392505050565b5f610c0360ff841683613971565b5f610c038383613971565b5f60208284031215613a3a575f5ffd5b8151610c0381613363565b5f5f60408385031215613a56575f5ffd5b505080516020909101519092909150565b5f60208284031215613a77575f5ffd5b5051919050565b5f5f60408385031215613a8f575f5ffd5b823567ffffffffffffffff811115613aa5575f5ffd5b613ab185828601613589565b925050602083013567ffffffffffffffff8111156137ce575f5ffd5b601f821115611a4857805f5260205f20601f840160051c81016020851015613af25750805b601f840160051c820191505b81811015613b11575f8155600101613afe565b5050505050565b815167ffffffffffffffff811115613b3257613b3261349f565b613b4681613b4084546137e4565b84613acd565b6020601f821160018114613b78575f8315613b615750848201515b5f19600385901b1c1916600184901b178455613b11565b5f848152602081207fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe08516915b82811015613bc55787850151825560209485019460019092019101613ba5565b5084821015613be257868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b80516effffffffffffffffffffffffffffff81168114613844575f5ffd5b5f5f82840360c0811215613c21575f5ffd5b6060811215613c2e575f5ffd5b613c366134cc565b84518152613c4660208601613bf1565b6020820152613c5760408601613bf1565b6040820152925060607fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa082011215613c8d575f5ffd5b50613c966134cc565b613ca260608501613bf1565b8152613cb060808501613bf1565b6020820152613cc160a08501613835565b6040820152809150509250929050565b5f5f60408385031215613ce2575f5ffd5b82519150613cf260208401613835565b90509250929050565b5f60208284031215613d0b575f5ffd5b815167ffffffffffffffff811115613d21575f5ffd5b820160608185031215613d32575f5ffd5b613d3a6134cc565b815160ff81168114613d4a575f5ffd5b815260208281015190820152604082015167ffffffffffffffff811115613d6f575f5ffd5b80830192505084601f830112613d83575f5ffd5b8151613d916135ab82613544565b818152866020838601011115613da5575f5ffd5b8160208501602083015e5f918101602001919091526040820152949350505050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602160045260245ffd5b6001600160a01b0384168152826020820152606060408201525f612c9860608301846132ee565b6001600160a01b0385168152836020820152826040820152608060608201525f61301260808301846132ee565b61ffff83168152604060208201525f825160058110613e8e577f4e487b71000000000000000000000000000000000000000000000000000000005f52602160045260245ffd5b604083015260208301516001600160a01b03811660608401525060408301516001600160a01b038116608084015250606083015160a0830152608083015160c083015260a083015160e083015260c083015160e0610100840152612c986101208401826132ee565b5f82518060208501845e5f92019182525091905056fea164736f6c634300081d000a", "sourceMap": "8220:408:108:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5693:116:54;;;:::i;:::-;;;160:25:121;;;148:2;133:18;5693:116:54;;;;;;;;3721:114;;;:::i;:::-;;;;;;;:::i;1777:588:92:-;;;;;;:::i;:::-;;:::i;3902:186:16:-;;;;;;:::i;:::-;;:::i;:::-;;;1701:14:121;;1694:22;1676:41;;1664:2;1649:18;3902:186:16;1536:187:121;8324:494:54;;;;;;:::i;:::-;;:::i;7507:811::-;;;;;;:::i;:::-;;:::i;11895:190::-;;;;;;:::i;:::-;;:::i;2803:97:16:-;2881:12;;2803:97;;4680:244;;;;;;:::i;:::-;;:::i;4340:248:54:-;;;;;;:::i;:::-;;:::i;2688:82:16:-;;;2761:2;6076:36:121;;6064:2;6049:18;2688:82:16;5934:184:121;1837:39:54;;;;;;;;-1:-1:-1;;;;;6419:55:121;;;6401:74;;6389:2;6374:18;1837:39:54;6255:226:121;244:169:83;;;;;;:::i;:::-;;:::i;:::-;;11424:270:54;;;;;;:::i;:::-;;:::i;8427:199:108:-;;;;-1:-1:-1;;;;;8480:7:108;8534:16;;;7800:74:121;;8534:16:108;8552:22;;;7905:2:121;7890:18;;7883:83;8576:18:108;;;7982::121;;;7975:83;;;;8596:22:108;;8089:2:121;8074:18;;8067:34;7787:3;7772:19;8427:199:108;7569:538:121;2933:116:16;;;;;;:::i;:::-;-1:-1:-1;;;;;3024:18:16;2998:7;3024:18;;;:9;:18;;;;;;;2933:116;1917:44:54;;;;;6196:132;;;:::i;6396:176::-;;;:::i;3841:118::-;;;:::i;2002:41::-;;;;;8824:362;;;;;;:::i;:::-;;:::i;10154:593::-;;;;;;:::i;:::-;;:::i;5004:132::-;;;:::i;3244:178:16:-;;;;;;:::i;:::-;;:::i;5177:475:54:-;;;;;;:::i;:::-;;:::i;7394:107::-;;;:::i;4050:249::-;;;;;;:::i;:::-;;:::i;4629:341::-;;;;;;:::i;:::-;;:::i;6613:209::-;;;:::i;9192:956::-;;;;;;:::i;:::-;;:::i;3455:140:16:-;;;;;;:::i;:::-;-1:-1:-1;;;;;3561:18:16;;;3535:7;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3455:140;5850:305:54;;;:::i;10948:435::-;;;;;;:::i;:::-;;:::i;5693:116::-;5746:7;5772:30;5788:13;2881:12:16;;;2803:97;5772:30:54;5765:37;;5693:116;:::o;3721:114::-;3790:13;3822:6;3815:13;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3721:114;:::o;1777:588:92:-;1848:7;1871:16;;-1:-1:-1;;;;;1871:16:92;:30;;;;:77;;-1:-1:-1;1905:43:92;-1:-1:-1;;;;;1931:16:92;;;1905:25;:43::i;:::-;1867:445;;;1999:124;;;;;2071:4;1999:124;;;7800:74:121;-1:-1:-1;;;;;;2078:16:92;;;;7890:18:121;;;7883:83;2096:5:92;8002:55:121;;7982:18;;;7975:83;8074:18;;;8067:34;;;-1:-1:-1;;;1999:22:92;:46;;;;7772:19:121;;1999:124:92;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1964:159;;;;2277:10;2273:28;;;2296:5;1777:588;-1:-1:-1;;;1777:588:92:o;2273:28::-;1950:362;;1867:445;2329:29;2351:6;2329:21;:29::i;:::-;2322:36;1777:588;-1:-1:-1;;1777:588:92:o;3902:186:16:-;3975:4;735:10:23;4029:31:16;735:10:23;4045:7:16;4054:5;4029:8;:31::i;:::-;-1:-1:-1;4077:4:16;;3902:186;-1:-1:-1;;;3902:186:16:o;8324:494:54:-;6900:44;;;;;6933:10;6900:44;;;6401:74:121;8561:23:54;;676:42:97;;6900:32:54;;6374:18:121;;6900:44:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53;6896:90;;6962:24;;;;;6975:10;6962:24;;;6401:74:121;6374:18;;6962:24:54;;;;;;;;6896:90;7021:10;6996:22;:35;;-1:-1:-1;;6996:35:54;;;;;-1:-1:-1;8526:11:54;7181:1:::1;7153:16;-1:-1:-1::0;;;;;7153:16:54::1;7149:186;;7218:8:::0;7199:16:::1;:27:::0;::::1;-1:-1:-1::0;;7199:27:54::1;-1:-1:-1::0;;;;;7199:27:54;::::1;;::::0;::::1;;7149:186;;;7247:16;;-1:-1:-1::0;;;;;7247:16:54;;::::1;:28:::0;;::::1;;7243:92;;7298:26;;;;;;;;;;;;;;7243:92;1215:21:26::2;:19;:21::i;:::-;8614:62:54::3;8626:12;8640:10;8652;;8614:62;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::3;::::0;;;;-1:-1:-1;8664:11:54;;-1:-1:-1;8614:11:54::3;::::0;-1:-1:-1;;8614:62:54:i:3;:::-;8596:80:::0;-1:-1:-1;8745:66:54::3;-1:-1:-1::0;;;;;8751:5:54::3;8745:25:::0;::::3;::::0;8771:22:::3;;;8596:80:::0;8745:25:::3;:66::i;:::-;1257:20:26::2;:18;:20::i;:::-;-1:-1:-1::0;7059:22:54;7052:29;;-1:-1:-1;;7052:29:54;;;8324:494;;;;;;;:::o;7507:811::-;6900:44;;;;;6933:10;6900:44;;;6401:74:121;7710:20:54;;676:42:97;;6900:32:54;;6374:18:121;;6900:44:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53;6896:90;;6962:24;;;;;6975:10;6962:24;;;6401:74:121;6374:18;;6962:24:54;6255:226:121;6896:90:54;7021:10;6996:22;:35;;-1:-1:-1;;6996:35:54;;;;;-1:-1:-1;7678:8:54;7181:1:::1;7153:16;-1:-1:-1::0;;;;;7153:16:54::1;7149:186;;7218:8:::0;7199:16:::1;:27:::0;::::1;-1:-1:-1::0;;7199:27:54::1;-1:-1:-1::0;;;;;7199:27:54;::::1;;::::0;::::1;;7149:186;;;7247:16;;-1:-1:-1::0;;;;;7247:16:54;;::::1;:28:::0;;::::1;;7243:92;;7298:26;;;;;;;;;;;;;;7243:92;1215:21:26::2;:19;:21::i;:::-;7823:35:54::3;7849:8;7823:25;:35::i;:::-;7819:69;;;7867:21;;;;;;;;;;;;;;7819:69;7898:81;-1:-1:-1::0;;;;;7904:5:54::3;7898:29:::0;::::3;::::0;7928:22:::3;;;7960:4;7967:11:::0;7898:29:::3;:81::i;:::-;8004:58;8027:11;8040;;8004:58;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::3;::::0;;;;-1:-1:-1;8053:8:54;;-1:-1:-1;8004:22:54::3;::::0;-1:-1:-1;;8004:58:54:i:3;:::-;7989:73:::0;-1:-1:-1;8094:22:54::3;;-1:-1:-1::0;;;;;8094:22:54::3;8073:18;:43:::0;::::3;-1:-1:-1::0;;8073:43:54::3;::::0;::::3;::::0;::::3;-1:-1:-1::0;8151:12:54;;8126:22:::3;:37;-1:-1:-1::0;8254:57:54::3;8264:8:::0;-1:-1:-1;;;;;8274:22:54::3;;;8298:12:::0;8254:9:::3;:57::i;:::-;1257:20:26::2;:18;:20::i;:::-;-1:-1:-1::0;7059:22:54;7052:29;;-1:-1:-1;;7052:29:54;;;7507:811;;;;;;:::o;11895:190::-;12038:10;11987:17;3024:18:16;;;:9;:18;;;;;;11987:17:54;;12028:50;;12073:4;12028:9;:50::i;4680:244:16:-;4767:4;735:10:23;4823:37:16;4839:4;735:10:23;4854:5:16;4823:15;:37::i;:::-;4870:26;4880:4;4886:2;4890:5;4870:9;:26::i;:::-;4913:4;4906:11;;;4680:244;;;;;;:::o;4340:248:54:-;4417:7;1710:1;4544:13;:11;:13::i;:::-;4521:20;:18;:20::i;:::-;:36;;;;:::i;:::-;:59;;;;:::i;:::-;4499:17;:15;:17::i;:::-;4485:31;;:11;:31;:::i;:::-;4484:97;;;;:::i;244:169:83:-;308:11;;;;304:47;;;328:23;;;;;;;;;;;;;;304:47;361:11;:18;;;;375:4;361:18;;;389:17;401:4;;389:11;:17::i;:::-;244:169;;:::o;11424:270:54:-;6900:44;;;;;6933:10;6900:44;;;6401:74:121;11610:17:54;;676:42:97;;6900:32:54;;6374:18:121;;6900:44:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53;6896:90;;6962:24;;;;;6975:10;6962:24;;;6401:74:121;6374:18;;6962:24:54;6255:226:121;6896:90:54;7021:10;6996:22;:35;;-1:-1:-1;;6996:35:54;;;;;-1:-1:-1;11583:7:54;7181:1:::1;7153:16;-1:-1:-1::0;;;;;7153:16:54::1;7149:186;;7218:8:::0;7199:16:::1;:27:::0;::::1;-1:-1:-1::0;;7199:27:54::1;-1:-1:-1::0;;;;;7199:27:54;::::1;;::::0;::::1;;7149:186;;;7247:16;;-1:-1:-1::0;;;;;7247:16:54;;::::1;:28:::0;;::::1;;7243:92;;7298:26;;;;;;;;;;;;;;7243:92;11651:36:::2;11661:7;11670:10;11682:4;;11651:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::2;::::0;;;;-1:-1:-1;11651:9:54::2;::::0;-1:-1:-1;;;11651:36:54:i:2;:::-;11639:48:::0;-1:-1:-1;;7059:22:54;7052:29;;-1:-1:-1;;7052:29:54;;;11424:270;;;;;;:::o;6196:132::-;6244:7;1652:3;6287:16;;6271:13;2881:12:16;;;2803:97;6271:13:54;:32;;;;:::i;:::-;:49;;;;:::i;6396:176::-;6449:31;6527:38;:36;:38::i;:::-;6499:25;;:66;;;;:::i;3841:118::-;3912:13;3944:8;3937:15;;;;;:::i;8824:362::-;8940:14;7181:1;7153:16;-1:-1:-1;;;;;7153:16:54;7149:186;;7218:8;7199:16;:27;;-1:-1:-1;;7199:27:54;-1:-1:-1;;;;;7199:27:54;;;;;;7149:186;;;7247:16;;-1:-1:-1;;;;;7247:16:54;;;:28;;;;7243:92;;7298:26;;;;;;;;;;;;;;7243:92;6900:44:::1;::::0;;;;6933:10:::1;6900:44;::::0;::::1;6401:74:121::0;676:42:97::1;::::0;6900:32:54::1;::::0;6374:18:121;;6900:44:54::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53:::0;6896:90:::1;;6962:24;::::0;::::1;::::0;;6975:10:::1;6962:24;::::0;::::1;6401:74:121::0;6374:18;;6962:24:54::1;6255:226:121::0;6896:90:54::1;7021:10;6996:22;:35:::0;::::1;-1:-1:-1::0;;6996:35:54::1;::::0;::::1;::::0;::::1;-1:-1:-1::0;9136:2:54;9115:18:::2;:23:::0;::::2;-1:-1:-1::0;;9115:23:54::2;-1:-1:-1::0;;;;;9115:23:54;::::2;;::::0;::::2;-1:-1:-1::0;9173:6:54;;9148:22:::2;:31;-1:-1:-1::0;7059:22:54::1;7052:29:::0;::::1;-1:-1:-1::0;;7052:29:54::1;::::0;::::1;8824:362:::0;;;;:::o;10154:593::-;6900:44;;;;;6933:10;6900:44;;;6401:74:121;676:42:97;;6900:32:54;;6374:18:121;;6900:44:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53;6896:90;;6962:24;;;;;6975:10;6962:24;;;6401:74:121;6374:18;;6962:24:54;6255:226:121;6896:90:54;7021:10;6996:22;:35;;-1:-1:-1;;6996:35:54;;;;;-1:-1:-1;10341:10:54;10320:18:::1;:31:::0;::::1;-1:-1:-1::0;;10320:31:54::1;-1:-1:-1::0;;;;;10320:31:54;::::1;;::::0;::::1;-1:-1:-1::0;10386:18:54;;10361:22:::1;:43;-1:-1:-1::0;10487:65:54::1;-1:-1:-1::0;;;;;10497:22:54::1;;;10521:10:::0;10533:18;10487:9:::1;:65::i;:::-;10563:66;10580:10;10592:16;10610:18;10563:16;:66::i;:::-;-1:-1:-1::0;10724:16:54::1;10717:23:::0;::::1;-1:-1:-1::0;;10717:23:54::1;::::0;::::1;7059:22:::0;7052:29;;-1:-1:-1;;7052:29:54;;;10154:593;;;:::o;5004:132::-;5051:7;5077:32;1761:34;1652:3;333:4:97;1761:34:54;:::i;5077:32::-;:52;;5113:15;5077:52;:::i;3244:178:16:-;3313:4;735:10:23;3367:27:16;735:10:23;3384:2:16;3388:5;3367:9;:27::i;5177:475:54:-;5237:7;-1:-1:-1;;;;;5451:16:54;;;;;;5497:8;;5478:27;;;-1:-1:-1;;5478:27:54;;;;5237:7;5478:27;-1:-1:-1;5515:9:54;5527:32;1761:34;1652:3;333:4:97;1761:34:54;:::i;5527:32::-;:52;;5563:15;5527:52;:::i;:::-;5515:64;-1:-1:-1;5609:18:54;5590:16;:37;;-1:-1:-1;;5590:37:54;-1:-1:-1;;;;;5590:37:54;;;;;-1:-1:-1;5644:1:54;5177:475;-1:-1:-1;;;5177:475:54:o;7394:107::-;6900:44;;;;;6933:10;6900:44;;;6401:74:121;676:42:97;;6900:32:54;;6374:18:121;;6900:44:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53;6896:90;;6962:24;;;;;6975:10;6962:24;;;6401:74:121;6374:18;;6962:24:54;6255:226:121;6896:90:54;7021:10;6996:22;:35;;-1:-1:-1;;6996:35:54;;;;;-1:-1:-1;7478:16:54::1;7471:23:::0;::::1;-1:-1:-1::0;;7471:23:54::1;::::0;::::1;7059:22:::0;7052:29;;-1:-1:-1;;7052:29:54;;;7394:107::o;4050:249::-;4131:7;4274:17;:15;:17::i;:::-;1710:1;4232:13;:11;:13::i;:::-;4209:20;:18;:20::i;:::-;:36;;;;:::i;:::-;:59;;;;:::i;4629:341::-;4700:7;;4886:20;4892:14;4886:2;:20;:::i;:::-;4856:26;:24;:26::i;:::-;:51;;;;:::i;:::-;4799:38;375:2:97;4799:38:54;:19;:38;;:::i;:::-;4792:46;;:2;:46;:::i;:::-;4782:57;;:6;:57;:::i;:::-;:126;;;;:::i;:::-;4760:148;;4925:38;4951:11;4925:25;:38::i;6613:209::-;6664:13;:11;:13::i;:::-;6687:85;676:42:97;-1:-1:-1;;;;;6714:28:54;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6746:25;;6687:26;:85::i;:::-;6783:32;6790:25;6783:32;6613:209::o;9192:956::-;6900:44;;;;;6933:10;6900:44;;;6401:74:121;676:42:97;;6900:32:54;;6374:18:121;;6900:44:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;6948:5;6900:53;6896:90;;6962:24;;;;;6975:10;6962:24;;;6401:74:121;6374:18;;6962:24:54;6255:226:121;6896:90:54;7021:10;6996:22;:35;;-1:-1:-1;;6996:35:54;;;;;-1:-1:-1;9410:16:54;9391::::1;:35:::0;::::1;-1:-1:-1::0;;9391:35:54::1;-1:-1:-1::0;;;;;9391:35:54;::::1;;::::0;::::1;;9571:37;9597:10;9571:25;:37::i;:::-;9567:71;;;9617:21;;;;;;;;;;;;;;9567:71;9739:43;9765:16;9739:25;:43::i;:::-;:72;;;;-1:-1:-1::0;;;;;;3024:18:16;;9810:1:54::1;3024:18:16::0;;;:9;:18;;;;;;9786:25:54::1;9739:72;9735:131;;;9834:21;;;;;;;;;;;;;;9735:131;10079:10;10058:18;:31:::0;::::1;-1:-1:-1::0;;10058:31:54::1;::::0;::::1;::::0;::::1;-1:-1:-1::0;10124:17:54;;10099:22:::1;:42;-1:-1:-1::0;7059:22:54;7052:29;;-1:-1:-1;;7052:29:54;;;9192:956;;;;:::o;5850:305::-;6070:48;;;;;-1:-1:-1;;;;;6100:10:54;14515:55:121;;6070:48:54;;;14497:74:121;6112:5:54;14607:55:121;14587:18;;;14580:83;5907:7:54;;;;4821:42:71;;6070:29:54;;14470:18:121;;6070:48:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;6046:72:54;5850:305;-1:-1:-1;;5850:305:54:o;10948:435::-;11081:23;1215:21:26;:19;:21::i;:::-;11147:10:54::1;11116:18;3024::16::0;;;:9;:18;;;;;;;11172:15:54;;;11168:52:::1;;11196:24;;;;;;;;;;;;;;11168:52;11249:63;11261:14;11277:10;11289;11301;11249:11;:63::i;:::-;11231:81:::0;-1:-1:-1;11322:54:54::1;-1:-1:-1::0;;;;;11328:5:54::1;11322:25;11348:10;11231:81:::0;11322:25:::1;:54::i;:::-;11106:277;1257:20:26::0;:18;:20::i;14726:245:54:-;14809:4;14840:22;-1:-1:-1;;;;;14832:45:54;;;;;:132;;-1:-1:-1;14893:71:54;;;;;14949:4;14893:71;;;14497:74:121;-1:-1:-1;;;;;14607:55:121;;;14587:18;;;14580:83;14893:22:54;:47;;;;14470:18:121;;14893:71:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;19966:348::-;20045:7;20064:19;20086:33;20112:6;20086:25;:33::i;:::-;20064:55;-1:-1:-1;20267:38:54;375:2:97;20267:38:54;:19;:38;;:::i;:::-;20260:46;;:2;:46;:::i;:::-;20222:20;20228:14;20222:2;:20;:::i;:::-;20192:26;:24;:26::i;:::-;20178:40;;:11;:40;:::i;:::-;:65;;;;:::i;:::-;20177:130;;;;:::i;8630:128:16:-;8714:37;8723:5;8730:7;8739:5;8746:4;8714:8;:37::i;:::-;8630:128;;;:::o;1290:377:26:-;637:66;3375:11:28;1444:93:26;;;1496:30;;;;;;;;;;;;;;1444:93;1611:49;1655:4;637:66;1611:36;:43;;:49::i;:::-;1290:377::o;17727:836:54:-;17906:23;17945:12;17961:1;17945:17;17941:31;;-1:-1:-1;17971:1:54;17964:8;;17941:31;17982:15;18000:38;18026:11;18000:25;:38::i;:::-;17982:56;;18049:27;18079:30;18103:5;18079:23;:30::i;:::-;18049:60;;18168:13;:11;:13::i;:::-;18191:64;18205:12;18219:11;18232:10;18244;18191:13;:64::i;:::-;18269:10;18265:48;;;18301:12;18281:16;;:32;;;;;;;:::i;:::-;;;;-1:-1:-1;;18265:48:54;18324:25;18352:30;18376:5;18352:23;:30::i;:::-;18324:58;-1:-1:-1;18410:39:54;18430:19;18324:58;18410:39;:::i;:::-;18392:57;;18524:32;18530:11;18543:12;18524:5;:32::i;:::-;17931:632;;;17727:836;;;;;;;:::o;1219:160:19:-;1328:43;;-1:-1:-1;;;;;15212:55:121;;;1328:43:19;;;15194:74:121;15284:18;;;15277:34;;;1301:71:19;;1321:5;;1343:14;;;;;15167:18:121;;1328:43:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1301:19;:71::i;1673:105:26:-;1721:50;1765:5;637:66;1721:36;1666:115:28;1618:188:19;1745:53;;-1:-1:-1;;;;;15542:55:121;;;1745:53:19;;;15524:74:121;15634:55;;;15614:18;;;15607:83;15706:18;;;15699:34;;;1718:81:19;;1738:5;;1760:18;;;;;15497::121;;1745:53:19;15322:417:121;1718:81:19;1618:188;;;;:::o;17013:633:54:-;17131:20;17167:6;17177:1;17167:11;17163:25;;-1:-1:-1;17187:1:54;17180:8;;17163:25;17247:13;:11;:13::i;:::-;17270:32;17305:20;:18;:20::i;:::-;17270:55;;17335:47;17352:6;17360:8;17370:11;17335:16;:47::i;:::-;17392:25;17443:24;17420:20;:18;:20::i;:::-;:47;;;;:::i;:::-;17392:75;;1710:1;17563:13;:11;:13::i;:::-;17536:40;;:24;:40;:::i;:::-;:63;;;;:::i;:::-;17514:17;:15;:17::i;:::-;17494:37;;:17;:37;:::i;:::-;17493:107;;;;:::i;:::-;17478:122;;17610:29;17616:8;17626:12;17610:5;:29::i;:::-;17153:493;;17013:633;;;;;:::o;5297:300:16:-;-1:-1:-1;;;;;5380:18:16;;5376:86;;5421:30;;;;;5448:1;5421:30;;;6401:74:121;6374:18;;5421:30:16;6255:226:121;5376:86:16;-1:-1:-1;;;;;5475:16:16;;5471:86;;5514:32;;;;;5543:1;5514:32;;;6401:74:121;6374:18;;5514:32:16;6255:226:121;5471:86:16;5566:24;5574:4;5580:2;5584:5;5566:7;:24::i;12091:654:54:-;12184:17;12217:10;12231:1;12217:15;12213:52;;12241:24;;;;;;;;;;;;;;12213:52;12370:13;:11;:13::i;:::-;12393:24;12420:37;12446:10;12420:25;:37::i;:::-;12393:64;;12479:62;12497:7;12506:16;12524:10;12536:4;12479:17;:62::i;:::-;12467:74;;12728:10;12708:16;;:30;;;;;;;:::i;:::-;;;;-1:-1:-1;12091:654:54;;;-1:-1:-1;;;;;12091:654:54:o;10319:476:16:-;-1:-1:-1;;;;;3561:18:16;;;10418:24;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;-1:-1:-1;;10484:36:16;;10480:309;;;10559:5;10540:16;:24;10536:130;;;10591:60;;;;;-1:-1:-1;;;;;15964:55:121;;10591:60:16;;;15946:74:121;16036:18;;;16029:34;;;16079:18;;;16072:34;;;15919:18;;10591:60:16;15744:368:121;10536:130:16;10707:57;10716:5;10723:7;10751:5;10732:16;:24;10758:5;10707:8;:57::i;14977:128:54:-;15056:42;;;;;15092:4;15056:42;;;6401:74:121;15030:7:54;;15062:10;-1:-1:-1;;;;;15056:27:54;;;;6374:18:121;;15056:42:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;16615:317::-;16694:19;;16740:34;;;;16751:4;16740:34;:::i;:::-;16693:81;;-1:-1:-1;16693:81:54;-1:-1:-1;16784:6:54;:14;16693:81;16784:6;:14;:::i;:::-;-1:-1:-1;16808:8:54;:18;16819:7;16808:8;:18;:::i;:::-;-1:-1:-1;16837:20:54;:46;;;;16867:15;16837:46;;;;;16898:27;;16919:4;;16898:27;;-1:-1:-1;;16898:27:54;16683:249;;16615:317;;:::o;12784:951::-;12952:20;;12854:34;;;;12934:38;;12952:20;;12934:15;:38;:::i;:::-;12900:72;-1:-1:-1;13040:9:54;450:8:97;13053:33:54;12900:72;13053:7;:33;:::i;:::-;13052:42;;;;:::i;:::-;13040:54;;13108:1;13113;13108:6;13104:20;;13123:1;13116:8;;;;12784:951;:::o;13104:20::-;13135:33;13194:25;;13171:20;:18;:20::i;:::-;:48;;;;:::i;:::-;13135:84;-1:-1:-1;13306:15:54;333:4:97;13399:21:54;333:4:97;13399:1:54;:21;:::i;:::-;:41;;;;:::i;:::-;13393:1;13385:5;13393:1;;13385:5;:::i;:::-;:9;;;;:::i;:::-;13384:57;;;;:::i;:::-;13359:21;333:4:97;13359:1:54;:21;:::i;:::-;13349:5;13353:1;;13349:5;:::i;:::-;13348:33;;;;:::i;:::-;13324:21;13344:1;333:4:97;13324:21:54;:::i;:::-;:57;;;;:::i;:::-;:117;;;;:::i;:::-;13306:135;-1:-1:-1;13542:34:54;13306:135;13579:45;333:4:97;13579:25:54;:45;:::i;:::-;:55;;;;:::i;:::-;13542:92;-1:-1:-1;13674:54:54;13542:92;13674:25;:54;:::i;:::-;13645:83;;12890:845;;;;;12784:951;:::o;6013:533:92:-;6140:16;6180:22;-1:-1:-1;;;;;6172:45:92;;6168:372;;6433:96;;;;;-1:-1:-1;;;;;15542:55:121;;;6433:96:92;;;15524:74:121;15634:55;;;15614:18;;;15607:83;15706:18;;;15699:34;;;6433:22:92;:46;;;;15497:18:121;;6433:96:92;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;13741:298:54:-;13786:20;;13810:15;13786:20;;;;:39;13782:52;;13741:298::o;13782:52::-;13938:38;:36;:38::i;:::-;13909:25;;:67;;;;;;;:::i;:::-;;;;-1:-1:-1;;13986:20:54;:46;;;;14016:15;13986:46;;;;;13741:298::o;18704:156::-;18803:50;-1:-1:-1;;;;;18809:10:54;18803:30;18834:5;18841:11;18803:30;:50::i;9605:432:16:-;-1:-1:-1;;;;;9717:19:16;;9713:89;;9759:32;;;;;9788:1;9759:32;;;6401:74:121;6374:18;;9759:32:16;6255:226:121;9713:89:16;-1:-1:-1;;;;;9815:21:16;;9811:90;;9859:31;;;;;9887:1;9859:31;;;6401:74:121;6374:18;;9859:31:16;6255:226:121;9811:90:16;-1:-1:-1;;;;;9910:18:16;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;9955:76;;;;10005:7;-1:-1:-1;;;;;9989:31:16;9998:5;-1:-1:-1;;;;;9989:31:16;;10014:5;9989:31;;;;160:25:121;;148:2;133:18;;14:177;9989:31:16;;;;;;;;9605:432;;;;:::o;3491:139:28:-;3608:5;3602:4;3595:19;3491:139;;:::o;571:221:98:-;631:7;-1:-1:-1;;;;;669:20:98;;;:116;;748:37;;;;;779:4;748:37;;;6401:74:121;-1:-1:-1;;;;;748:22:98;;;;;6374:18:121;;748:37:98;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;669:116;;;708:21;650:135;571:221;-1:-1:-1;;571:221:98:o;3215:1667:92:-;3395:10;3391:1485;;;3457:69;;;;;3507:4;3457:69;;;14497:74:121;-1:-1:-1;;;;;14607:55:121;;;14587:18;;;14580:83;3422:24:92;;3457:22;:41;;;;;;14470:18:121;;3457:69:92;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3421:105;;;3540:25;3615:1;:14;;;3568:61;;3598:14;3576:1;:18;;;3568:27;;:44;;;;:::i;:::-;:61;;;;:::i;:::-;3686:184;;;;;-1:-1:-1;;;;;15964:55:121;;;3686:184:92;;;15946:74:121;16036:18;;;16029:34;;;16079:18;;;16072:34;;;16029;;-1:-1:-1;;;;;3686:22:92;:55;;;;;;15919:18:121;;3686:184:92;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3644:226;;;;3889:9;3884:63;;3935:11;;3907:40;;;;;;;;160:25:121;;;;133:18;;3907:40:92;14:177:121;3884:63:92;4107:13;-1:-1:-1;;;;;4098:22:92;:5;-1:-1:-1;;;;;4098:22:92;;4094:602;;4140:26;4180:10;4169:38;;;;;;;;;;;;:::i;:::-;4140:67;;4225:18;4246:382;;;;;;;;4285:25;4246:382;;;;;;;;:::i;:::-;;;;;4351:13;-1:-1:-1;;;;;4246:382:92;;;;;4405:5;-1:-1:-1;;;;;4246:382:92;;;;;4441:13;4246:382;;;;4483:6;:24;;;4246:382;;;;4539:15;4246:382;;;;4590:6;:19;;;4246:382;;;4225:403;;4647:34;4661:5;4668:6;:12;;;4647:34;;:13;:34::i;:::-;;;4122:574;;4094:602;3407:1299;;;;3391:1485;;;4726:25;4754:41;4780:14;4754:25;:41::i;:::-;4726:69;;4809:56;4835:17;4854:10;4809:25;:56::i;:::-;;4712:164;3215:1667;;;;:::o;7888:206:16:-;-1:-1:-1;;;;;7958:21:16;;7954:89;;8002:30;;;;;8029:1;8002:30;;;6401:74:121;6374:18;;8002:30:16;6255:226:121;7954:89:16;8052:35;8060:7;8077:1;8081:5;8052:7;:35::i;8370:720:19:-;8450:18;8478:19;8616:4;8613:1;8606:4;8600:11;8593:4;8587;8583:15;8580:1;8573:5;8566;8561:60;8673:7;8663:176;;8717:4;8711:11;8762:16;8759:1;8754:3;8739:40;8808:16;8803:3;8796:29;8663:176;-1:-1:-1;;8916:1:19;8910:8;8866:16;;-1:-1:-1;8942:15:19;;:68;;8994:11;9009:1;8994:16;;8942:68;;;-1:-1:-1;;;;;8960:26:19;;;:31;8942:68;8938:146;;;9033:40;;;;;-1:-1:-1;;;;;6419:55:121;;9033:40:19;;;6401:74:121;6374:18;;9033:40:19;6255:226:121;2932:277:92;3060:61;;;;;-1:-1:-1;;;;;3089:22:92;15212:55:121;;3060:61:92;;;15194:74:121;15284:18;;;15277:34;;;3066:5:92;3060:20;;;;15167:18:121;;3060:61:92;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;3131:71:92;;;;;-1:-1:-1;;;;;3131:22:92;:34;;;;:71;;3174:5;;3182:6;;3190:11;;3131:71;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;7362:208:16:-;-1:-1:-1;;;;;7432:21:16;;7428:91;;7476:32;;;;;7505:1;7476:32;;;6401:74:121;6374:18;;7476:32:16;6255:226:121;7428:91:16;7528:35;7544:1;7548:7;7557:5;14045:634:54;-1:-1:-1;;;;;14135:18:54;;;;;;:38;;-1:-1:-1;;;;;;14157:16:54;;;;14135:38;14131:501;;;14348:18;;-1:-1:-1;;;;;14348:18:54;;;:24;;;;14344:87;;14381:50;;;;;-1:-1:-1;;;;;15542:55:121;;;14381:50:54;;;15524:74:121;15634:55;;15614:18;;;15607:83;15706:18;;;15699:34;;;15497:18;;14381:50:54;15322:417:121;14344:87:54;14474:5;14449:22;;:30;14445:93;;;14488:50;;;;;-1:-1:-1;;;;;15542:55:121;;;14488:50:54;;;15524:74:121;15634:55;;15614:18;;;15607:83;15706:18;;;15699:34;;;15497:18;;14488:50:54;15322:417:121;14445:93:54;14560:18;14553:25;;-1:-1:-1;;14553:25:54;;;14592:29;14599:22;14592:29;14131:501;14642:30;14656:4;14662:2;14666:5;14642:13;:30::i;2371:471:92:-;2582:76;;;;;-1:-1:-1;;;;;2616:22:92;15212:55:121;;2582:76:92;;;15194:74:121;15284:18;;;15277:34;;;-1:-1:-1;;2588:10:92;2582:25;;;;;;15167:18:121;;2582:76:92;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;2680:155:92;;;;;-1:-1:-1;;;;;2680:22:92;:39;;;;:155;;2743:7;;2770:16;;2802:10;;2820:4;;2680:155;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2668:167;2371:471;-1:-1:-1;;;;;2371:471:92:o;15219:903:54:-;15316:18;;15391:21;15372:15;;:40;;;;;;;;:::i;:::-;;15368:748;;15501:14;;;;;15458:58;;;;;-1:-1:-1;;;;;6419:55:121;;;15458:58:54;;;6401:74:121;15428:27:54;;676:42:97;;15458::54;;6374:18:121;;15458:58:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15428:88;;15530:63;15574:3;15580:5;:12;;;15536:5;:15;;;-1:-1:-1;;;;;15530:35:54;;;:63;;;;;:::i;:::-;15622:3;-1:-1:-1;;;;;15622:15:54;;15638:5;:15;;;15655:5;:12;;;15669:5;:18;;;15622:66;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15607:81;;15710:5;:12;;;15702:35;;;;;15368:748;15768:22;4821:42:71;-1:-1:-1;;;;;15793:58:54;;:60;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15768:85;;15867:19;15889:135;15920:14;15959:36;;;15997:5;16004;15936:74;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;15889:13;:135::i;:::-;15867:157;;16078:6;16067:38;;;;;;;;;;;;:::i;:::-;16038:67;;-1:-1:-1;16038:67:54;-1:-1:-1;;;15368:748:54;15219:903;;;;;:::o;5099:804:92:-;5236:23;5271:26;5311:10;5300:38;;;;;;;;;;;;:::i;:::-;5271:67;;5348:18;5369:321;;;;;;;;5400:25;5369:321;;;;;;;;:::i;:::-;;;;;5458:10;-1:-1:-1;;;;;5369:321:92;;;;;5501:5;-1:-1:-1;;;;;5369:321:92;;;;;5529:19;5369:321;;;;5569:6;:24;;;5369:321;;;;5617:15;5369:321;;;;5660:6;:19;;;5369:321;;;5348:342;;5862:34;5876:5;5883:6;:12;;;5862:34;;:13;:34::i;:::-;5835:61;5099:804;-1:-1:-1;;;;;;5099:804:92:o;5912:1107:16:-;-1:-1:-1;;;;;6001:18:16;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:16;;-1:-1:-1;5997:540:16;;-1:-1:-1;;;;;6211:15:16;;6189:19;6211:15;;;:9;:15;;;;;;6244:19;;;6240:115;;;6290:50;;;;;-1:-1:-1;;;;;15964:55:121;;6290:50:16;;;15946:74:121;16036:18;;;16029:34;;;16079:18;;;16072:34;;;15919:18;;6290:50:16;15744:368:121;6240:115:16;-1:-1:-1;;;;;6475:15:16;;;;;;:9;:15;;;;;6493:19;;;;6475:37;;5997:540;-1:-1:-1;;;;;6551:16:16;;6547:425;;6714:12;:21;;;;;;;6547:425;;;-1:-1:-1;;;;;6925:13:16;;;;;;:9;:13;;;;;:22;;;;;;6547:425;7002:2;-1:-1:-1;;;;;6987:25:16;6996:4;-1:-1:-1;;;;;6987:25:16;;7006:5;6987:25;;;;160::121;;148:2;133:18;;14:177;6987:25:16;;;;;;;;5912:1107;;;:::o;798:180:98:-;-1:-1:-1;;;;;889:28:98;;885:41;;798:180;;;:::o;885:41::-;936:35;-1:-1:-1;;;;;936:18:98;;955:7;964:6;936:18;:35::i;16128:448:54:-;16204:19;16235:12;16277:6;-1:-1:-1;;;;;16277:19:54;16297:4;16277:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;16257:45:54;-1:-1:-1;16257:45:54;-1:-1:-1;16257:45:54;16312:258;;16438:16;16435:1;;16417:38;16529:16;16435:1;16519:27;5084:380:19;5199:47;;;-1:-1:-1;;;;;15212:55:121;;5199:47:19;;;15194:74:121;15284:18;;;;15277:34;;;5199:47:19;;;;;;;;;;15167:18:121;;;;5199:47:19;;;;;;;;;;;;;;5262:44;5214:13;5199:47;5262:23;:44::i;:::-;5257:201;;5349:43;;-1:-1:-1;;;;;15212:55:121;;;5349:43:19;;;15194:74:121;5389:1:19;15284:18:121;;;15277:34;5322:71:19;;5342:5;;5364:13;;;;;15167:18:121;;5349:43:19;15020:297:121;5322:71:19;5407:40;5427:5;5434:12;5407:19;:40::i;9592:480::-;9675:4;9691:12;9713:18;9741:19;9875:4;9872:1;9865:4;9859:11;9852:4;9846;9842:15;9839:1;9832:5;9825;9820:60;9809:71;;9907:16;9893:30;;9957:1;9951:8;9936:23;;9985:7;:80;;;;-1:-1:-1;9997:15:19;;:67;;10048:11;10063:1;10048:16;9997:67;;;-1:-1:-1;;;;;;;;;;10015:26:19;;:30;;;9592:480::o;196:348:121:-;238:3;276:5;270:12;303:6;298:3;291:19;359:6;352:4;345:5;341:16;334:4;329:3;325:14;319:47;411:1;404:4;395:6;390:3;386:16;382:27;375:38;533:4;463:66;458:2;450:6;446:15;442:88;437:3;433:98;429:109;422:116;;;196:348;;;;:::o;549:220::-;698:2;687:9;680:21;661:4;718:45;759:2;748:9;744:18;736:6;718:45;:::i;774:226::-;833:6;886:2;874:9;865:7;861:23;857:32;854:52;;;902:1;899;892:12;854:52;-1:-1:-1;947:23:121;;774:226;-1:-1:-1;774:226:121:o;1005:154::-;-1:-1:-1;;;;;1084:5:121;1080:54;1073:5;1070:65;1060:93;;1149:1;1146;1139:12;1060:93;1005:154;:::o;1164:367::-;1232:6;1240;1293:2;1281:9;1272:7;1268:23;1264:32;1261:52;;;1309:1;1306;1299:12;1261:52;1348:9;1335:23;1367:31;1392:5;1367:31;:::i;:::-;1417:5;1495:2;1480:18;;;;1467:32;;-1:-1:-1;;;1164:367:121:o;1728:347::-;1779:8;1789:6;1843:3;1836:4;1828:6;1824:17;1820:27;1810:55;;1861:1;1858;1851:12;1810:55;-1:-1:-1;1884:20:121;;1927:18;1916:30;;1913:50;;;1959:1;1956;1949:12;1913:50;1996:4;1988:6;1984:17;1972:29;;2048:3;2041:4;2032:6;2024;2020:19;2016:30;2013:39;2010:59;;;2065:1;2062;2055:12;2080:785;2177:6;2185;2193;2201;2209;2262:3;2250:9;2241:7;2237:23;2233:33;2230:53;;;2279:1;2276;2269:12;2230:53;2318:9;2305:23;2337:31;2362:5;2337:31;:::i;:::-;2387:5;-1:-1:-1;2465:2:121;2450:18;;2437:32;;-1:-1:-1;2568:2:121;2553:18;;2540:32;;-1:-1:-1;2649:2:121;2634:18;;2621:32;2676:18;2665:30;;2662:50;;;2708:1;2705;2698:12;2662:50;2747:58;2797:7;2788:6;2777:9;2773:22;2747:58;:::i;:::-;2080:785;;;;-1:-1:-1;2080:785:121;;-1:-1:-1;2824:8:121;;2721:84;2080:785;-1:-1:-1;;;2080:785:121:o;2870:664::-;2958:6;2966;2974;2982;3035:2;3023:9;3014:7;3010:23;3006:32;3003:52;;;3051:1;3048;3041:12;3003:52;3096:23;;;-1:-1:-1;3195:2:121;3180:18;;3167:32;3208:33;3167:32;3208:33;:::i;:::-;3260:7;-1:-1:-1;3318:2:121;3303:18;;3290:32;3345:18;3334:30;;3331:50;;;3377:1;3374;3367:12;3331:50;3416:58;3466:7;3457:6;3446:9;3442:22;3416:58;:::i;:::-;2870:664;;;;-1:-1:-1;3493:8:121;-1:-1:-1;;;;2870:664:121:o;3539:184::-;3591:77;3588:1;3581:88;3688:4;3685:1;3678:15;3712:4;3709:1;3702:15;3728:253;3800:2;3794:9;3842:4;3830:17;;3877:18;3862:34;;3898:22;;;3859:62;3856:88;;;3924:18;;:::i;:::-;3960:2;3953:22;3728:253;:::o;3986:334::-;4057:2;4051:9;4113:2;4103:13;;4118:66;4099:86;4087:99;;4216:18;4201:34;;4237:22;;;4198:62;4195:88;;;4263:18;;:::i;:::-;4299:2;4292:22;3986:334;;-1:-1:-1;3986:334:121:o;4325:245::-;4373:4;4406:18;4398:6;4395:30;4392:56;;;4428:18;;:::i;:::-;-1:-1:-1;4485:2:121;4473:15;4490:66;4469:88;4559:4;4465:99;;4325:245::o;4575:516::-;4617:5;4670:3;4663:4;4655:6;4651:17;4647:27;4637:55;;4688:1;4685;4678:12;4637:55;4728:6;4715:20;4767:4;4759:6;4755:17;4796:1;4817:52;4833:35;4861:6;4833:35;:::i;:::-;4817:52;:::i;:::-;4806:63;;4894:6;4885:7;4878:23;4934:3;4925:6;4920:3;4916:16;4913:25;4910:45;;;4951:1;4948;4941:12;4910:45;5002:6;4997:3;4990:4;4981:7;4977:18;4964:45;5058:1;5029:20;;;5051:4;5025:31;5018:42;;;;-1:-1:-1;5033:7:121;4575:516;-1:-1:-1;;;4575:516:121:o;5096:320::-;5164:6;5217:2;5205:9;5196:7;5192:23;5188:32;5185:52;;;5233:1;5230;5223:12;5185:52;5273:9;5260:23;5306:18;5298:6;5295:30;5292:50;;;5338:1;5335;5328:12;5292:50;5361:49;5402:7;5393:6;5382:9;5378:22;5361:49;:::i;5421:508::-;5498:6;5506;5514;5567:2;5555:9;5546:7;5542:23;5538:32;5535:52;;;5583:1;5580;5573:12;5535:52;5622:9;5609:23;5641:31;5666:5;5641:31;:::i;:::-;5691:5;-1:-1:-1;5748:2:121;5733:18;;5720:32;5761:33;5720:32;5761:33;:::i;:::-;5421:508;;5813:7;;-1:-1:-1;;;5893:2:121;5878:18;;;;5865:32;;5421:508::o;6486:409::-;6556:6;6564;6617:2;6605:9;6596:7;6592:23;6588:32;6585:52;;;6633:1;6630;6623:12;6585:52;6673:9;6660:23;6706:18;6698:6;6695:30;6692:50;;;6738:1;6735;6728:12;6692:50;6777:58;6827:7;6818:6;6807:9;6803:22;6777:58;:::i;:::-;6854:8;;6751:84;;-1:-1:-1;6486:409:121;-1:-1:-1;;;;6486:409:121:o;6900:664::-;6988:6;6996;7004;7012;7065:2;7053:9;7044:7;7040:23;7036:32;7033:52;;;7081:1;7078;7071:12;7033:52;7120:9;7107:23;7139:31;7164:5;7139:31;:::i;:::-;7189:5;-1:-1:-1;7267:2:121;7252:18;;7239:32;;-1:-1:-1;7348:2:121;7333:18;;7320:32;7375:18;7364:30;;7361:50;;;7407:1;7404;7397:12;8112:247;8171:6;8224:2;8212:9;8203:7;8199:23;8195:32;8192:52;;;8240:1;8237;8230:12;8192:52;8279:9;8266:23;8298:31;8323:5;8298:31;:::i;8364:508::-;8441:6;8449;8457;8510:2;8498:9;8489:7;8485:23;8481:32;8478:52;;;8526:1;8523;8516:12;8478:52;8565:9;8552:23;8584:31;8609:5;8584:31;:::i;:::-;8634:5;-1:-1:-1;8712:2:121;8697:18;;8684:32;;-1:-1:-1;8794:2:121;8779:18;;8766:32;8807:33;8766:32;8807:33;:::i;:::-;8859:7;8849:17;;;8364:508;;;;;:::o;8877:629::-;8963:6;8971;8979;8987;9040:3;9028:9;9019:7;9015:23;9011:33;9008:53;;;9057:1;9054;9047:12;9008:53;9096:9;9083:23;9115:31;9140:5;9115:31;:::i;:::-;9165:5;-1:-1:-1;9222:2:121;9207:18;;9194:32;9235:33;9194:32;9235:33;:::i;:::-;8877:629;;9287:7;;-1:-1:-1;;;;9367:2:121;9352:18;;9339:32;;9470:2;9455:18;9442:32;;8877:629::o;9511:388::-;9579:6;9587;9640:2;9628:9;9619:7;9615:23;9611:32;9608:52;;;9656:1;9653;9646:12;9608:52;9695:9;9682:23;9714:31;9739:5;9714:31;:::i;:::-;9764:5;-1:-1:-1;9821:2:121;9806:18;;9793:32;9834:33;9793:32;9834:33;:::i;:::-;9886:7;9876:17;;;9511:388;;;;;:::o;9904:434::-;9981:6;9989;10042:2;10030:9;10021:7;10017:23;10013:32;10010:52;;;10058:1;10055;10048:12;10010:52;10103:23;;;-1:-1:-1;10201:2:121;10186:18;;10173:32;10228:18;10217:30;;10214:50;;;10260:1;10257;10250:12;10214:50;10283:49;10324:7;10315:6;10304:9;10300:22;10283:49;:::i;:::-;10273:59;;;9904:434;;;;;:::o;10343:437::-;10422:1;10418:12;;;;10465;;;10486:61;;10540:4;10532:6;10528:17;10518:27;;10486:61;10593:2;10585:6;10582:14;10562:18;10559:38;10556:218;;10630:77;10627:1;10620:88;10731:4;10728:1;10721:15;10759:4;10756:1;10749:15;10556:218;;10343:437;;;:::o;10785:164::-;10861:13;;10910;;10903:21;10893:32;;10883:60;;10939:1;10936;10929:12;10883:60;10785:164;;;:::o;10954:309::-;11030:6;11038;11091:2;11079:9;11070:7;11066:23;11062:32;11059:52;;;11107:1;11104;11097:12;11059:52;11130:37;11157:9;11130:37;:::i;:::-;11229:2;11214:18;;;;11208:25;11120:47;;11208:25;;-1:-1:-1;;;10954:309:121:o;11268:202::-;11335:6;11388:2;11376:9;11367:7;11363:23;11359:32;11356:52;;;11404:1;11401;11394:12;11356:52;11427:37;11454:9;11427:37;:::i;11475:184::-;11527:77;11524:1;11517:88;11624:4;11621:1;11614:15;11648:4;11645:1;11638:15;11664:128;11731:9;;;11752:11;;;11749:37;;;11766:18;;:::i;11797:125::-;11862:9;;;11883:10;;;11880:36;;;11896:18;;:::i;11927:168::-;12000:9;;;12031;;12048:15;;;12042:22;;12028:37;12018:71;;12069:18;;:::i;12100:274::-;12140:1;12166;12156:189;;12201:77;12198:1;12191:88;12302:4;12299:1;12292:15;12330:4;12327:1;12320:15;12156:189;-1:-1:-1;12359:9:121;;12100:274::o;12379:375::-;12467:1;12485:5;12499:249;12520:1;12510:8;12507:15;12499:249;;;12570:4;12565:3;12561:14;12555:4;12552:24;12549:50;;;12579:18;;:::i;:::-;12629:1;12619:8;12615:16;12612:49;;;12643:16;;;;12612:49;12726:1;12722:16;;;;;12682:15;;12499:249;;;12379:375;;;;;;:::o;12759:1022::-;12808:5;12838:8;12828:80;;-1:-1:-1;12879:1:121;12893:5;;12828:80;12927:4;12917:76;;-1:-1:-1;12964:1:121;12978:5;;12917:76;13009:4;13027:1;13022:59;;;;13095:1;13090:174;;;;13002:262;;13022:59;13052:1;13043:10;;13066:5;;;13090:174;13127:3;13117:8;13114:17;13111:43;;;13134:18;;:::i;:::-;-1:-1:-1;;13190:1:121;13176:16;;13249:5;;13002:262;;13348:2;13338:8;13335:16;13329:3;13323:4;13320:13;13316:36;13310:2;13300:8;13297:16;13292:2;13286:4;13283:12;13279:35;13276:77;13273:203;;;-1:-1:-1;13385:19:121;;;13461:5;;13273:203;13508:102;-1:-1:-1;;13533:8:121;13527:4;13508:102;:::i;:::-;13706:6;-1:-1:-1;;13634:79:121;13625:7;13622:92;13619:118;;;13717:18;;:::i;:::-;13755:20;;12759:1022;-1:-1:-1;;;12759:1022:121:o;13786:140::-;13844:5;13873:47;13914:4;13904:8;13900:19;13894:4;13873:47;:::i;13931:131::-;13991:5;14020:36;14047:8;14041:4;14020:36;:::i;14067:251::-;14137:6;14190:2;14178:9;14169:7;14165:23;14161:32;14158:52;;;14206:1;14203;14196:12;14158:52;14238:9;14232:16;14257:31;14282:5;14257:31;:::i;14674:341::-;14751:6;14759;14812:2;14800:9;14791:7;14787:23;14783:32;14780:52;;;14828:1;14825;14818:12;14780:52;-1:-1:-1;;14873:16:121;;14979:2;14964:18;;;14958:25;14873:16;;14958:25;;-1:-1:-1;14674:341:121:o;16117:230::-;16187:6;16240:2;16228:9;16219:7;16215:23;16211:32;16208:52;;;16256:1;16253;16246:12;16208:52;-1:-1:-1;16301:16:121;;16117:230;-1:-1:-1;16117:230:121:o;16352:536::-;16440:6;16448;16501:2;16489:9;16480:7;16476:23;16472:32;16469:52;;;16517:1;16514;16507:12;16469:52;16557:9;16544:23;16590:18;16582:6;16579:30;16576:50;;;16622:1;16619;16612:12;16576:50;16645:49;16686:7;16677:6;16666:9;16662:22;16645:49;:::i;:::-;16635:59;;;16747:2;16736:9;16732:18;16719:32;16776:18;16766:8;16763:32;16760:52;;;16808:1;16805;16798:12;17019:518;17121:2;17116:3;17113:11;17110:421;;;17157:5;17154:1;17147:16;17201:4;17198:1;17188:18;17271:2;17259:10;17255:19;17252:1;17248:27;17242:4;17238:38;17307:4;17295:10;17292:20;17289:47;;;-1:-1:-1;17330:4:121;17289:47;17385:2;17380:3;17376:12;17373:1;17369:20;17363:4;17359:31;17349:41;;17440:81;17458:2;17451:5;17448:13;17440:81;;;17517:1;17503:16;;17484:1;17473:13;17440:81;;;17444:3;;17019:518;;;:::o;17773:1418::-;17899:3;17893:10;17926:18;17918:6;17915:30;17912:56;;;17948:18;;:::i;:::-;17977:97;18067:6;18027:38;18059:4;18053:11;18027:38;:::i;:::-;18021:4;17977:97;:::i;:::-;18123:4;18154:2;18143:14;;18171:1;18166:768;;;;18978:1;18995:6;18992:89;;;-1:-1:-1;19047:19:121;;;19041:26;18992:89;-1:-1:-1;;17670:1:121;17666:11;;;17662:84;17658:89;17648:100;17754:1;17750:11;;;17645:117;19094:81;;18136:1049;;18166:768;16966:1;16959:14;;;17003:4;16990:18;;18214:66;18202:79;;;18379:222;18393:7;18390:1;18387:14;18379:222;;;18475:19;;;18469:26;18454:42;;18582:4;18567:20;;;;18535:1;18523:14;;;;18409:12;18379:222;;;18383:3;18629:6;18620:7;18617:19;18614:261;;;18690:19;;;18684:26;-1:-1:-1;;18773:1:121;18769:14;;;18785:3;18765:24;18761:97;18757:102;18742:118;18727:134;;18614:261;-1:-1:-1;;;;18921:1:121;18905:14;;;18901:22;18888:36;;-1:-1:-1;17773:1418:121:o;19196:190::-;19275:13;;19328:32;19317:44;;19307:55;;19297:83;;19376:1;19373;19366:12;19391:1062;19547:6;19555;19599:9;19590:7;19586:23;19629:3;19625:2;19621:12;19618:32;;;19646:1;19643;19636:12;19618:32;19670:4;19666:2;19662:13;19659:33;;;19688:1;19685;19678:12;19659:33;19714:22;;:::i;:::-;19781:16;;19806:22;;19860:49;19905:2;19890:18;;19860:49;:::i;:::-;19855:2;19848:5;19844:14;19837:73;19942:49;19987:2;19976:9;19972:18;19942:49;:::i;:::-;19937:2;19926:14;;19919:73;19930:5;-1:-1:-1;20109:4:121;20040:66;20032:75;;20028:86;20025:106;;;20127:1;20124;20117:12;20025:106;;20155:22;;:::i;:::-;20202:51;20247:4;20236:9;20232:20;20202:51;:::i;:::-;20193:7;20186:68;20288:50;20333:3;20322:9;20318:19;20288:50;:::i;:::-;20283:2;20274:7;20270:16;20263:76;20373:47;20415:3;20404:9;20400:19;20373:47;:::i;:::-;20368:2;20359:7;20355:16;20348:73;20440:7;20430:17;;;19391:1062;;;;;:::o;20458:309::-;20534:6;20542;20595:2;20583:9;20574:7;20570:23;20566:32;20563:52;;;20611:1;20608;20601:12;20563:52;20656:16;;;-1:-1:-1;20715:46:121;20757:2;20742:18;;20715:46;:::i;:::-;20705:56;;20458:309;;;;;:::o;20772:1220::-;20873:6;20926:2;20914:9;20905:7;20901:23;20897:32;20894:52;;;20942:1;20939;20932:12;20894:52;20975:9;20969:16;21008:18;21000:6;20997:30;20994:50;;;21040:1;21037;21030:12;20994:50;21063:22;;21119:4;21101:16;;;21097:27;21094:47;;;21137:1;21134;21127:12;21094:47;21163:22;;:::i;:::-;21215:2;21209:9;21262:4;21253:7;21249:18;21240:7;21237:31;21227:59;;21282:1;21279;21272:12;21227:59;21295:22;;21376:2;21368:11;;;21362:18;21396:14;;;21389:31;21459:2;21451:11;;21445:18;21488;21475:32;;21472:52;;;21520:1;21517;21510:12;21472:52;21551:8;21547:2;21543:17;21533:27;;;21598:7;21591:4;21587:2;21583:13;21579:27;21569:55;;21620:1;21617;21610:12;21569:55;21653:2;21647:9;21678:52;21694:35;21722:6;21694:35;:::i;21678:52::-;21753:6;21746:5;21739:21;21801:7;21796:2;21787:6;21783:2;21779:15;21775:24;21772:37;21769:57;;;21822:1;21819;21812:12;21769:57;21870:6;21865:2;21861;21857:11;21852:2;21845:5;21841:14;21835:42;21922:1;21897:18;;;21917:2;21893:27;21886:38;;;;21951:2;21940:14;;21933:29;21944:5;20772:1220;-1:-1:-1;;;;20772:1220:121:o;21997:184::-;22049:77;22046:1;22039:88;22146:4;22143:1;22136:15;22170:4;22167:1;22160:15;22186:409;-1:-1:-1;;;;;22393:6:121;22389:55;22378:9;22371:74;22481:6;22476:2;22465:9;22461:18;22454:34;22524:2;22519;22508:9;22504:18;22497:30;22352:4;22544:45;22585:2;22574:9;22570:18;22562:6;22544:45;:::i;22600:482::-;-1:-1:-1;;;;;22835:6:121;22831:55;22820:9;22813:74;22923:6;22918:2;22907:9;22903:18;22896:34;22966:6;22961:2;22950:9;22946:18;22939:34;23009:3;23004:2;22993:9;22989:18;22982:31;22794:4;23030:46;23071:3;23060:9;23056:19;23048:6;23030:46;:::i;23376:1124::-;23591:6;23583;23579:19;23568:9;23561:38;23635:2;23630;23619:9;23615:18;23608:30;23542:4;23663:6;23657:13;23696:1;23692:2;23689:9;23679:197;;23732:77;23729:1;23722:88;23833:4;23830:1;23823:15;23861:4;23858:1;23851:15;23679:197;23907:2;23892:18;;23885:30;23962:2;23950:15;;23944:22;-1:-1:-1;;;;;6189:54:121;;24023:2;24008:18;;6177:67;-1:-1:-1;24076:2:121;24064:15;;24058:22;-1:-1:-1;;;;;6189:54:121;;24139:3;24124:19;;6177:67;24089:55;24199:2;24191:6;24187:15;24181:22;24175:3;24164:9;24160:19;24153:51;24259:3;24251:6;24247:16;24241:23;24235:3;24224:9;24220:19;24213:52;24321:3;24313:6;24309:16;24303:23;24296:4;24285:9;24281:20;24274:53;24376:3;24368:6;24364:16;24358:23;24418:4;24412:3;24401:9;24397:19;24390:33;24440:54;24489:3;24478:9;24474:19;24458:14;24440:54;:::i;24853:301::-;24982:3;25020:6;25014:13;25066:6;25059:4;25051:6;25047:17;25042:3;25036:37;25128:1;25092:16;;25117:13;;;-1:-1:-1;25092:16:121;24853:301;-1:-1:-1;24853:301:121:o", "linkReferences": {}, "immutableReferences": {"43073": [{"start": 768, "length": 32}, {"start": 1574, "length": 32}, {"start": 2246, "length": 32}, {"start": 2788, "length": 32}, {"start": 6047, "length": 32}, {"start": 6297, "length": 32}, {"start": 6902, "length": 32}, {"start": 6995, "length": 32}, {"start": 9798, "length": 32}, {"start": 9950, "length": 32}, {"start": 10392, "length": 32}, {"start": 10592, "length": 32}, {"start": 12213, "length": 32}], "43077": [{"start": 966, "length": 32}, {"start": 6007, "length": 32}, {"start": 7891, "length": 32}, {"start": 8798, "length": 32}, {"start": 11136, "length": 32}, {"start": 12166, "length": 32}], "43081": [{"start": 1029, "length": 32}, {"start": 8182, "length": 32}], "43084": [{"start": 1624, "length": 32}, {"start": 6348, "length": 32}, {"start": 6459, "length": 32}, {"start": 8465, "length": 32}, {"start": 8581, "length": 32}, {"start": 9329, "length": 32}, {"start": 9582, "length": 32}, {"start": 10345, "length": 32}, {"start": 10547, "length": 32}, {"start": 11087, "length": 32}, {"start": 11293, "length": 32}], "43086": [{"start": 5232, "length": 32}, {"start": 6586, "length": 32}], "43088": [{"start": 5167, "length": 32}, {"start": 6639, "length": 32}], "55750": [{"start": 9756, "length": 32}, {"start": 9903, "length": 32}]}}, "methodIdentifiers": {"allowTransfer(address,uint256,address)": "98476c2b", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "asset()": "38d52e0f", "balanceOf(address)": "70a08231", "burnShares(address,uint256,uint256,bytes)": "0db734d4", "clearCurrentAccount()": "b35cb45d", "collectFees()": "c8796572", "convertSharesToYieldToken(uint256)": "b905a4ff", "convertToAssets(uint256)": "07a2d13a", "convertToShares(uint256)": "c6e6f592", "convertYieldTokenToAsset()": "e0b4327d", "convertYieldTokenToShares(uint256)": "********", "decimals()": "313ce567", "effectiveSupply()": "8fc47093", "feeRate()": "978bbdb9", "feesAccrued()": "94db0595", "initialize(bytes)": "439fab91", "initiateWithdraw(address,uint256,bytes)": "57831a04", "initiateWithdrawNative(bytes)": "131b822d", "mintShares(uint256,address,bytes)": "127af7f9", "name()": "06fdde03", "postLiquidation(address,address,uint256)": "98dce16d", "preLiquidation(address,address,uint256,uint256)": "cc351ac5", "price()": "a035b1fe", "price(address)": "aea91078", "redeemNative(uint256,bytes)": "eb9b1912", "symbol()": "95d89b41", "totalAssets()": "01e1d114", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "transientVariables()": "616252b6", "yieldToken()": "76d5de85"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_yieldToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_feeRate\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"CannotEnterPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CurrentAccountAlreadySet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientSharesHeld\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"}],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"UnauthorizedLendingMarketTransfer\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"name\":\"WithdrawRequestNotFinalized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"VaultCreated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"currentAccount\",\"type\":\"address\"}],\"name\":\"allowTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"asset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sharesOwner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToBurn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesHeld\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"burnShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsWithdrawn\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"clearCurrentAccount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"collectFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"convertSharesToYieldToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"convertToAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"convertToShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"convertYieldTokenToAsset\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"yieldTokens\",\"type\":\"uint256\"}],\"name\":\"convertYieldTokenToShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"effectiveSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feesAccrued\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"feesAccruedInYieldToken\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesHeld\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdrawNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assetAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"depositData\",\"type\":\"bytes\"}],\"name\":\"mintShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesMinted\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidator\",\"type\":\"uint256\"}],\"name\":\"postLiquidation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accountSharesHeld\",\"type\":\"uint256\"}],\"name\":\"preLiquidation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"}],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesToRedeem\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"redeemNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsWithdrawn\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"transientVariables\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"yieldToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowTransfer(address,uint256,address)\":{\"params\":{\"amount\":\"The amount of shares to allow the transfer of.\",\"currentAccount\":\"The address of the current account.\",\"to\":\"The address to allow the transfer to.\"}},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"burnShares(address,uint256,uint256,bytes)\":{\"params\":{\"redeemData\":\"calldata used to redeem the yield token.\",\"sharesOwner\":\"The address of the account to burn the shares for.\",\"sharesToBurn\":\"The amount of shares to burn.\"}},\"collectFees()\":{\"details\":\"Collects the fees accrued by the vault. Only callable by the owner.\"},\"convertSharesToYieldToken(uint256)\":{\"details\":\"Returns the amount of yield tokens that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met.\"},\"convertToShares(uint256)\":{\"details\":\"Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the \\u201cper-user\\u201d price-per-share, and instead should reflect the \\u201caverage-user\\u2019s\\u201d price-per-share, meaning what the average user should expect to see when exchanging to and from.\"},\"convertYieldTokenToAsset()\":{\"details\":\"Returns the oracle price of a yield token in terms of the asset token.\"},\"convertYieldTokenToShares(uint256)\":{\"details\":\"Returns the amount of yield tokens that the account would receive for the amount of shares provided.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"effectiveSupply()\":{\"details\":\"Returns the effective supply which excludes any escrowed shares.\"},\"feesAccrued()\":{\"details\":\"Returns the balance of yield tokens accrued by the vault.\"},\"initiateWithdraw(address,uint256,bytes)\":{\"params\":{\"account\":\"The address of the account to initiate the withdraw for.\",\"data\":\"calldata used to initiate the withdraw.\",\"sharesHeld\":\"The number of shares the account holds.\"}},\"initiateWithdrawNative(bytes)\":{\"details\":\"We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.\",\"params\":{\"data\":\"calldata used to initiate the withdraw.\"}},\"postLiquidation(address,address,uint256)\":{\"params\":{\"liquidateAccount\":\"The address of the account to liquidate.\",\"liquidator\":\"The address of the liquidator.\",\"sharesToLiquidator\":\"The amount of shares to liquidate.\"}},\"preLiquidation(address,address,uint256,uint256)\":{\"params\":{\"accountSharesHeld\":\"The amount of shares the account holds.\",\"liquidateAccount\":\"The address of the account to liquidate.\",\"liquidator\":\"The address of the liquidator.\",\"sharesToLiquidate\":\"The amount of shares to liquidate.\"}},\"price()\":{\"details\":\"It corresponds to the price of 10**(collateral token decimals) assets of collateral token quoted in 10**(loan token decimals) assets of loan token with `36 + loan token decimals - collateral token decimals` decimals of precision.\"},\"price(address)\":{\"details\":\"Returns the price of a yield token in terms of the asset token for the given borrower taking into account withdrawals.\"},\"redeemNative(uint256,bytes)\":{\"details\":\"We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.\",\"params\":{\"redeemData\":\"calldata used to redeem the yield token.\",\"sharesToRedeem\":\"The amount of shares to redeem.\"}},\"totalAssets()\":{\"details\":\"Returns the total amount of the underlying asset that is \\u201cmanaged\\u201d by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"allowTransfer(address,uint256,address)\":{\"notice\":\"Allows the lending market to transfer shares on exit position or liquidation.\"},\"burnShares(address,uint256,uint256,bytes)\":{\"notice\":\"Burns shares for a given number of shares.\"},\"clearCurrentAccount()\":{\"notice\":\"Clears the current account.\"},\"convertToAssets(uint256)\":{\"notice\":\"Returns the total value in terms of the borrowed token of the account's position\"},\"initiateWithdraw(address,uint256,bytes)\":{\"notice\":\"Initiates a withdraw for a given number of shares.\"},\"initiateWithdrawNative(bytes)\":{\"notice\":\"Initiates a withdraw for the native balance of the account.\"},\"postLiquidation(address,address,uint256)\":{\"notice\":\"Post-liquidation function.\"},\"preLiquidation(address,address,uint256,uint256)\":{\"notice\":\"Pre-liquidation function.\"},\"price()\":{\"notice\":\"Returns the price of 1 asset of collateral token quoted in 1 asset of loan token, scaled by 1e36.\"},\"redeemNative(uint256,bytes)\":{\"notice\":\"Redeems shares for assets for a native token.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"tests/Mocks.sol\":\"MockStakingStrategy\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e\",\"dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol\":{\"keccak256\":\"0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244\",\"dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"node_modules/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/IEtherFi.sol\":{\"keccak256\":\"0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635\",\"dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/oracles/AbstractCustomOracle.sol\":{\"keccak256\":\"0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524\",\"license\":\"BSUL-1.1\",\"urls\":[\"bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd\",\"dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/staking/AbstractStakingStrategy.sol\":{\"keccak256\":\"0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f\",\"dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg\"]},\"src/staking/StakingStrategy.sol\":{\"keccak256\":\"0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2\",\"dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]},\"src/withdraws/AbstractWithdrawRequestManager.sol\":{\"keccak256\":\"0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb\",\"dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu\"]},\"src/withdraws/ClonedCooldownHolder.sol\":{\"keccak256\":\"0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b\",\"dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM\"]},\"src/withdraws/EtherFi.sol\":{\"keccak256\":\"0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2\",\"dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L\"]},\"tests/Mocks.sol\":{\"keccak256\":\"0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876\",\"dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_yieldToken", "type": "address"}, {"internalType": "uint256", "name": "_feeRate", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "CannotEnterPosition"}, {"inputs": [], "type": "error", "name": "CurrentAccountAlreadySet"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [], "type": "error", "name": "InsufficientSharesHeld"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "type": "error", "name": "Unauthorized"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "type": "error", "name": "UnauthorizedLendingMarketTransfer"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "type": "error", "name": "WithdrawRequestNotFinalized"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}], "type": "event", "name": "VaultCreated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "currentAccount", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "allowTransfer"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "sharesOwner", "type": "address"}, {"internalType": "uint256", "name": "sharesToBurn", "type": "uint256"}, {"internalType": "uint256", "name": "sharesHeld", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "burnShares", "outputs": [{"internalType": "uint256", "name": "assetsWithdrawn", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "clear<PERSON><PERSON><PERSON>A<PERSON>unt"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "collectFees"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertSharesToYieldToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "convertYieldTokenToAsset", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "yieldTokens", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertYieldTokenToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "effectiveSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feesAccrued", "outputs": [{"internalType": "uint256", "name": "feesAccruedInYieldToken", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "sharesHeld", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdrawNative", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assetAmount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "bytes", "name": "depositData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "mintShares", "outputs": [{"internalType": "uint256", "name": "sharesMinted", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidator", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "postLiquidation"}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidate", "type": "uint256"}, {"internalType": "uint256", "name": "accountSharesHeld", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "preLiquidation"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "sharesToRedeem", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "redeemNative", "outputs": [{"internalType": "uint256", "name": "assetsWithdrawn", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "transientVariables", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "yieldToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"allowTransfer(address,uint256,address)": {"params": {"amount": "The amount of shares to allow the transfer of.", "currentAccount": "The address of the current account.", "to": "The address to allow the transfer to."}}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "burnShares(address,uint256,uint256,bytes)": {"params": {"redeemData": "calldata used to redeem the yield token.", "sharesOwner": "The address of the account to burn the shares for.", "sharesToBurn": "The amount of shares to burn."}}, "collectFees()": {"details": "Collects the fees accrued by the vault. Only callable by the owner."}, "convertSharesToYieldToken(uint256)": {"details": "Returns the amount of yield tokens that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met."}, "convertToShares(uint256)": {"details": "Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the “per-user” price-per-share, and instead should reflect the “average-user’s” price-per-share, meaning what the average user should expect to see when exchanging to and from."}, "convertYieldTokenToAsset()": {"details": "Returns the oracle price of a yield token in terms of the asset token."}, "convertYieldTokenToShares(uint256)": {"details": "Returns the amount of yield tokens that the account would receive for the amount of shares provided."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "effectiveSupply()": {"details": "Returns the effective supply which excludes any escrowed shares."}, "feesAccrued()": {"details": "Returns the balance of yield tokens accrued by the vault."}, "initiateWithdraw(address,uint256,bytes)": {"params": {"account": "The address of the account to initiate the withdraw for.", "data": "calldata used to initiate the withdraw.", "sharesHeld": "The number of shares the account holds."}}, "initiateWithdrawNative(bytes)": {"details": "We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.", "params": {"data": "calldata used to initiate the withdraw."}}, "postLiquidation(address,address,uint256)": {"params": {"liquidateAccount": "The address of the account to liquidate.", "liquidator": "The address of the liquidator.", "sharesToLiquidator": "The amount of shares to liquidate."}}, "preLiquidation(address,address,uint256,uint256)": {"params": {"accountSharesHeld": "The amount of shares the account holds.", "liquidateAccount": "The address of the account to liquidate.", "liquidator": "The address of the liquidator.", "sharesToLiquidate": "The amount of shares to liquidate."}}, "price()": {"details": "It corresponds to the price of 10**(collateral token decimals) assets of collateral token quoted in 10**(loan token decimals) assets of loan token with `36 + loan token decimals - collateral token decimals` decimals of precision."}, "price(address)": {"details": "Returns the price of a yield token in terms of the asset token for the given borrower taking into account withdrawals."}, "redeemNative(uint256,bytes)": {"details": "We do not set the current account here because valuation is not done in this method. A native balance does not require a collateral check.", "params": {"redeemData": "calldata used to redeem the yield token.", "sharesToRedeem": "The amount of shares to redeem."}}, "totalAssets()": {"details": "Returns the total amount of the underlying asset that is “managed” by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allowTransfer(address,uint256,address)": {"notice": "Allows the lending market to transfer shares on exit position or liquidation."}, "burnShares(address,uint256,uint256,bytes)": {"notice": "Burns shares for a given number of shares."}, "clearCurrentAccount()": {"notice": "Clears the current account."}, "convertToAssets(uint256)": {"notice": "Returns the total value in terms of the borrowed token of the account's position"}, "initiateWithdraw(address,uint256,bytes)": {"notice": "Initiates a withdraw for a given number of shares."}, "initiateWithdrawNative(bytes)": {"notice": "Initiates a withdraw for the native balance of the account."}, "postLiquidation(address,address,uint256)": {"notice": "Post-liquidation function."}, "preLiquidation(address,address,uint256,uint256)": {"notice": "Pre-liquidation function."}, "price()": {"notice": "Returns the price of 1 asset of collateral token quoted in 1 asset of loan token, scaled by 1e36."}, "redeemNative(uint256,bytes)": {"notice": "Redeems shares for assets for a native token."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"tests/Mocks.sol": "MockStakingStrategy"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055", "urls": ["bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e", "dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol": {"keccak256": "0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8", "urls": ["bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244", "dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "node_modules/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/IEtherFi.sol": {"keccak256": "0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc", "urls": ["bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635", "dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/oracles/AbstractCustomOracle.sol": {"keccak256": "0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524", "urls": ["bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd", "dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW"], "license": "BSUL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/staking/AbstractStakingStrategy.sol": {"keccak256": "0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31", "urls": ["bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f", "dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg"], "license": "BUSL-1.1"}, "src/staking/StakingStrategy.sol": {"keccak256": "0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9", "urls": ["bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2", "dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}, "src/withdraws/AbstractWithdrawRequestManager.sol": {"keccak256": "0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd", "urls": ["bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb", "dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu"], "license": "BUSL-1.1"}, "src/withdraws/ClonedCooldownHolder.sol": {"keccak256": "0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f", "urls": ["bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b", "dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM"], "license": "BUSL-1.1"}, "src/withdraws/EtherFi.sol": {"keccak256": "0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262", "urls": ["bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2", "dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L"], "license": "BUSL-1.1"}, "tests/Mocks.sol": {"keccak256": "0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a", "urls": ["bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876", "dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc"], "license": "UNLICENSED"}}, "version": 1}, "id": 108}