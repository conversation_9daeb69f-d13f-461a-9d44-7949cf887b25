{"abi": [{"type": "constructor", "inputs": [{"name": "_depositToken", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "depositToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "view"}, {"type": "function", "name": "getReward", "inputs": [{"name": "holder", "type": "address", "internalType": "address"}, {"name": "claim", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pid", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "rewardAmount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "rewardToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "view"}, {"type": "function", "name": "setRewardAmount", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawAndUnwrap", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "claim", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "success", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "3550:1448:108:-:0;;;3704:181;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1582:113:16;;;;;;;;;;;;;-1:-1:-1;;;1582:113:16;;;;;;;;;;;;;;;;-1:-1:-1;;;1582:113:16;;;1656:5;1648;:13;;;;;;:::i;:::-;-1:-1:-1;1671:7:16;:17;1681:7;1671;:17;:::i;:::-;-1:-1:-1;;;;;;;;3780:35:108;::::1;;::::0;3839:39:::1;::::0;::::1;::::0;::::1;:::i;:::-;3253:2:121::0;3235:21;;;3292:2;3272:18;;;3265:30;-1:-1:-1;;;3326:2:121;3311:18;;3304:45;3387:3;3380:4;3365:20;;3358:33;;;3428:1;3407:19;;;3400:30;-1:-1:-1;;;3461:3:121;3446:19;;3439:34;3505:3;3490:19;3839:39:108::1;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;;3825:53:108::1;;::::0;-1:-1:-1;3550:1448:108;;;;;;;;;;:::o;14:290:121:-;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:121;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:121:o;309:127::-;370:10;365:3;361:20;358:1;351:31;401:4;398:1;391:15;425:4;422:1;415:15;441:380;520:1;516:12;;;;563;;;584:61;;638:4;630:6;626:17;616:27;;584:61;691:2;683:6;680:14;660:18;657:38;654:161;;737:10;732:3;728:20;725:1;718:31;772:4;769:1;762:15;800:4;797:1;790:15;654:161;;441:380;;;:::o;952:518::-;1054:2;1049:3;1046:11;1043:421;;;1090:5;1087:1;1080:16;1134:4;1131:1;1121:18;1204:2;1192:10;1188:19;1185:1;1181:27;1175:4;1171:38;1240:4;1228:10;1225:20;1222:47;;;-1:-1:-1;1263:4:121;1222:47;1318:2;1313:3;1309:12;1306:1;1302:20;1296:4;1292:31;1282:41;;1373:81;1391:2;1384:5;1381:13;1373:81;;;1450:1;1436:16;;1417:1;1406:13;1373:81;;;1377:3;;1043:421;952:518;;;:::o;1646:1299::-;1766:10;;-1:-1:-1;;;;;1788:30:121;;1785:56;;;1821:18;;:::i;:::-;1850:97;1940:6;1900:38;1932:4;1926:11;1900:38;:::i;:::-;1894:4;1850:97;:::i;:::-;1996:4;2027:2;2016:14;;2044:1;2039:649;;;;2732:1;2749:6;2746:89;;;-1:-1:-1;2801:19:121;;;2795:26;2746:89;-1:-1:-1;;1603:1:121;1599:11;;;1595:24;1591:29;1581:40;1627:1;1623:11;;;1578:57;2848:81;;2009:930;;2039:649;899:1;892:14;;;936:4;923:18;;-1:-1:-1;;2075:20:121;;;2193:222;2207:7;2204:1;2201:14;2193:222;;;2289:19;;;2283:26;2268:42;;2396:4;2381:20;;;;2349:1;2337:14;;;;2223:12;2193:222;;;2197:3;2443:6;2434:7;2431:19;2428:201;;;2504:19;;;2498:26;-1:-1:-1;;2587:1:121;2583:14;;;2599:3;2579:24;2575:37;2571:42;2556:58;2541:74;;2428:201;-1:-1:-1;;;;2675:1:121;2659:14;;;2655:22;2642:36;;-1:-1:-1;1646:1299:121:o;2950:565::-;3550:1448:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "3550:1448:108:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89:16;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3902:186;;;;;;:::i;:::-;;:::i;:::-;;;1167:14:121;;1160:22;1142:41;;1130:2;1115:18;3902:186:16;1002:187:121;2803:97:16;2881:12;;2803:97;;;1340:25:121;;;1328:2;1313:18;2803:97:16;1194:177:121;4680:244:16;;;;;;:::i;:::-;;:::i;2688:82::-;;;2761:2;1897:36:121;;1885:2;1870:18;2688:82:16;1755:184:121;4282:254:108;;;;;;:::i;:::-;;:::i;4907:89::-;4984:4;4907:89;;;2729:42:121;2717:55;;;2699:74;;2687:2;2672:18;4907:89:108;2553:226:121;3985:291:108;;;;;;:::i;:::-;;:::i;2933:116:16:-;;;;;;:::i;:::-;3024:18;;2998:7;3024:18;;;;;;;;;;;;2933:116;1962:93;;;:::i;3891:88:108:-;;;;;;:::i;:::-;3951:12;:21;3891:88;;;3244:178:16;;;;;;:::i;:::-;;:::i;4542:281:108:-;;;;;;:::i;:::-;;:::i;3622:35::-;;;;;3455:140:16;;;;;;:::i;:::-;3561:18;;;;3535:7;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3455:140;4829:72:108;4867:7;4829:72;;3589:27;;;;;;3663:34;;;;;1760:89:16;1805:13;1837:5;1830:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89;:::o;3902:186::-;3975:4;735:10:23;4029:31:16;735:10:23;4045:7:16;4054:5;4029:8;:31::i;:::-;4077:4;4070:11;;;3902:186;;;;;:::o;4680:244::-;4767:4;735:10:23;4823:37:16;4839:4;735:10:23;4854:5:16;4823:15;:37::i;:::-;4870:26;4880:4;4886:2;4890:5;4870:9;:26::i;:::-;-1:-1:-1;4913:4:16;;4680:244;-1:-1:-1;;;;4680:244:16:o;4282:254:108:-;4397:60;;;;;4423:10;4397:60;;;5046:74:121;4443:4:108;5136:18:121;;;5129:83;5228:18;;;5221:34;;;4373:12:108;;4397;:25;;;;;5019:18:121;;4397:60:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;4467:38:108;4473:10;4501:3;4485:13;:6;4494:4;4485:13;:::i;:::-;:19;;;;:::i;:::-;4467:5;:38::i;:::-;-1:-1:-1;4525:4:108;;4282:254;-1:-1:-1;;;4282:254:108:o;3985:291::-;4050:4;4070:12;;4086:1;4070:17;4066:34;;-1:-1:-1;4096:4:108;4089:11;;4066:34;4114:5;4110:53;;;4150:12;;4121:42;;;;;:20;6349:55:121;;;4121:42:108;;;6331:74:121;6421:18;;;6414:34;;;;4121:11:108;:20;;;;;;6304:18:121;;4121:42:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4110:53;-1:-1:-1;;4247:1:108;4232:12;:16;-1:-1:-1;4265:4:108;;3985:291::o;1962:93:16:-;2009:13;2041:7;2034:14;;;;;:::i;3244:178::-;3313:4;735:10:23;3367:27:16;735:10:23;3384:2:16;3388:5;3367:9;:27::i;4542:281:108:-;4615:12;4643:5;4639:54;;;4650:43;;;;;4679:4;4650:43;;;6331:74:121;6421:18;;;6414:34;;;4650:11:108;:20;;;;;6304:18:121;;4650:43:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4639:54;4703:25;4709:10;4721:6;4703:5;:25::i;:::-;4738:21;:12;:21;;4760:10;4787:4;4772:12;:6;4781:3;4772:12;:::i;:::-;:19;;;;:::i;:::-;4738:54;;;;;;;;;;6361:42:121;6349:55;;;4738:54:108;;;6331:74:121;6421:18;;;6414:34;6304:18;;4738:54:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;8630:128:16:-;8714:37;8723:5;8730:7;8739:5;8746:4;8714:8;:37::i;:::-;8630:128;;;:::o;10319:476::-;3561:18;;;;10418:24;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;10503:17;10484:36;;10480:309;;;10559:5;10540:16;:24;10536:130;;;10591:60;;;;;6691:42:121;6679:55;;10591:60:16;;;6661:74:121;6751:18;;;6744:34;;;6794:18;;;6787:34;;;6634:18;;10591:60:16;;;;;;;;10536:130;10707:57;10716:5;10723:7;10751:5;10732:16;:24;10758:5;10707:8;:57::i;:::-;10408:387;10319:476;;;:::o;5297:300::-;5380:18;;;5376:86;;5421:30;;;;;5448:1;5421:30;;;2699:74:121;2672:18;;5421:30:16;2553:226:121;5376:86:16;5475:16;;;5471:86;;5514:32;;;;;5543:1;5514:32;;;2699:74:121;2672:18;;5514:32:16;2553:226:121;5471:86:16;5566:24;5574:4;5580:2;5584:5;5566:7;:24::i;7362:208::-;7432:21;;;7428:91;;7476:32;;;;;7505:1;7476:32;;;2699:74:121;2672:18;;7476:32:16;2553:226:121;7428:91:16;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;:::-;7362:208;;:::o;7888:206::-;7958:21;;;7954:89;;8002:30;;;;;8029:1;8002:30;;;2699:74:121;2672:18;;8002:30:16;2553:226:121;7954:89:16;8052:35;8060:7;8077:1;8081:5;8052:7;:35::i;9605:432::-;9717:19;;;9713:89;;9759:32;;;;;9788:1;9759:32;;;2699:74:121;2672:18;;9759:32:16;2553:226:121;9713:89:16;9815:21;;;9811:90;;9859:31;;;;;9887:1;9859:31;;;2699:74:121;2672:18;;9859:31:16;2553:226:121;9811:90:16;9910:18;;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;9955:76;;;;10005:7;9989:31;;9998:5;9989:31;;;10014:5;9989:31;;;;1340:25:121;;1328:2;1313:18;;1194:177;9989:31:16;;;;;;;;9605:432;;;;:::o;5912:1107::-;6001:18;;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:16;;-1:-1:-1;5997:540:16;;6211:15;;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6290:50;;;;;6691:42:121;6679:55;;6290:50:16;;;6661:74:121;6751:18;;;6744:34;;;6794:18;;;6787:34;;;6634:18;;6290:50:16;6459:368:121;6240:115:16;6475:15;;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;6551:16;;;6547:425;;6714:12;:21;;;;;;;6547:425;;;6925:13;;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;6987:25;;6996:4;6987:25;;;7006:5;6987:25;;;;1340::121;;1328:2;1313:18;;1194:177;6987:25:16;;;;;;;;5912:1107;;;:::o;14:477:121:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;297:6;292:2;284:6;280:15;275:2;264:9;260:18;254:50;353:1;348:2;339:6;328:9;324:22;320:31;313:42;482:2;412:66;407:2;399:6;395:15;391:88;380:9;376:104;372:113;364:121;;;14:477;;;;:::o;496:196::-;564:20;;624:42;613:54;;603:65;;593:93;;682:1;679;672:12;593:93;496:196;;;:::o;697:300::-;765:6;773;826:2;814:9;805:7;801:23;797:32;794:52;;;842:1;839;832:12;794:52;865:29;884:9;865:29;:::i;:::-;855:39;963:2;948:18;;;;935:32;;-1:-1:-1;;;697:300:121:o;1376:374::-;1453:6;1461;1469;1522:2;1510:9;1501:7;1497:23;1493:32;1490:52;;;1538:1;1535;1528:12;1490:52;1561:29;1580:9;1561:29;:::i;:::-;1551:39;;1609:38;1643:2;1632:9;1628:18;1609:38;:::i;:::-;1376:374;;1599:48;;-1:-1:-1;;;1716:2:121;1701:18;;;;1688:32;;1376:374::o;1944:118::-;2030:5;2023:13;2016:21;2009:5;2006:32;1996:60;;2052:1;2049;2042:12;1996:60;1944:118;:::o;2067:481::-;2141:6;2149;2157;2210:2;2198:9;2189:7;2185:23;2181:32;2178:52;;;2226:1;2223;2216:12;2178:52;2271:23;;;-1:-1:-1;2391:2:121;2376:18;;2363:32;;-1:-1:-1;2473:2:121;2458:18;;2445:32;2486:30;2445:32;2486:30;:::i;:::-;2535:7;2525:17;;;2067:481;;;;;:::o;2784:315::-;2849:6;2857;2910:2;2898:9;2889:7;2885:23;2881:32;2878:52;;;2926:1;2923;2916:12;2878:52;2949:29;2968:9;2949:29;:::i;:::-;2939:39;;3028:2;3017:9;3013:18;3000:32;3041:28;3063:5;3041:28;:::i;:::-;3088:5;3078:15;;;2784:315;;;;;:::o;3104:186::-;3163:6;3216:2;3204:9;3195:7;3191:23;3187:32;3184:52;;;3232:1;3229;3222:12;3184:52;3255:29;3274:9;3255:29;:::i;:::-;3245:39;3104:186;-1:-1:-1;;;3104:186:121:o;3295:226::-;3354:6;3407:2;3395:9;3386:7;3382:23;3378:32;3375:52;;;3423:1;3420;3413:12;3375:52;-1:-1:-1;3468:23:121;;3295:226;-1:-1:-1;3295:226:121:o;3526:361::-;3591:6;3599;3652:2;3640:9;3631:7;3627:23;3623:32;3620:52;;;3668:1;3665;3658:12;3620:52;3713:23;;;-1:-1:-1;3812:2:121;3797:18;;3784:32;3825:30;3784:32;3825:30;:::i;4137:260::-;4205:6;4213;4266:2;4254:9;4245:7;4241:23;4237:32;4234:52;;;4282:1;4279;4272:12;4234:52;4305:29;4324:9;4305:29;:::i;:::-;4295:39;;4353:38;4387:2;4376:9;4372:18;4353:38;:::i;:::-;4343:48;;4137:260;;;;;:::o;4402:437::-;4481:1;4477:12;;;;4524;;;4545:61;;4599:4;4591:6;4587:17;4577:27;;4545:61;4652:2;4644:6;4641:14;4621:18;4618:38;4615:218;;4689:77;4686:1;4679:88;4790:4;4787:1;4780:15;4818:4;4815:1;4808:15;4615:218;;4402:437;;;:::o;5266:245::-;5333:6;5386:2;5374:9;5365:7;5361:23;5357:32;5354:52;;;5402:1;5399;5392:12;5354:52;5434:9;5428:16;5453:28;5475:5;5453:28;:::i;5516:184::-;5568:77;5565:1;5558:88;5665:4;5662:1;5655:15;5689:4;5686:1;5679:15;5705:168;5778:9;;;5809;;5826:15;;;5820:22;;5806:37;5796:71;;5847:18;;:::i;5878:274::-;5918:1;5944;5934:189;;5979:77;5976:1;5969:88;6080:4;6077:1;6070:15;6108:4;6105:1;6098:15;5934:189;-1:-1:-1;6137:9:121;;5878:274::o;6832:125::-;6897:9;;;6918:10;;;6915:36;;;6931:18;;:::i", "linkReferences": {}, "immutableReferences": {"60340": [{"start": 626, "length": 32}, {"start": 1048, "length": 32}, {"start": 1703, "length": 32}], "60343": [{"start": 749, "length": 32}, {"start": 1331, "length": 32}, {"start": 1541, "length": 32}]}}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "deposit(uint256,uint256,bool)": "43a0d066", "depositToken()": "c89039c5", "getReward(address,bool)": "7050ccd9", "name()": "06fdde03", "operator()": "570ca735", "pid()": "f1068454", "rewardAmount()": "f7b2a7be", "rewardToken()": "f7c618c1", "setRewardAmount(uint256)": "a8a65a78", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "withdrawAndUnwrap(uint256,bool)": "c32e7202"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_depositToken\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositToken\",\"outputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"claim\",\"type\":\"bool\"}],\"name\":\"getReward\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pid\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardToken\",\"outputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"setRewardAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"claim\",\"type\":\"bool\"}],\"name\":\"withdrawAndUnwrap\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"tests/Mocks.sol\":\"MockRewardPool\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e\",\"dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol\":{\"keccak256\":\"0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244\",\"dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"node_modules/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/IEtherFi.sol\":{\"keccak256\":\"0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635\",\"dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/oracles/AbstractCustomOracle.sol\":{\"keccak256\":\"0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524\",\"license\":\"BSUL-1.1\",\"urls\":[\"bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd\",\"dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/staking/AbstractStakingStrategy.sol\":{\"keccak256\":\"0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f\",\"dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg\"]},\"src/staking/StakingStrategy.sol\":{\"keccak256\":\"0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2\",\"dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]},\"src/withdraws/AbstractWithdrawRequestManager.sol\":{\"keccak256\":\"0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb\",\"dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu\"]},\"src/withdraws/ClonedCooldownHolder.sol\":{\"keccak256\":\"0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b\",\"dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM\"]},\"src/withdraws/EtherFi.sol\":{\"keccak256\":\"0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2\",\"dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L\"]},\"tests/Mocks.sol\":{\"keccak256\":\"0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876\",\"dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_depositToken", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "depositToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "bool", "name": "claim", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "getReward", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "pid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardAmount"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bool", "name": "claim", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "withdrawAndUnwrap", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "name()": {"details": "Returns the name of the token."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"tests/Mocks.sol": "MockRewardPool"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055", "urls": ["bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e", "dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol": {"keccak256": "0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8", "urls": ["bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244", "dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "node_modules/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/IEtherFi.sol": {"keccak256": "0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc", "urls": ["bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635", "dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/oracles/AbstractCustomOracle.sol": {"keccak256": "0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524", "urls": ["bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd", "dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW"], "license": "BSUL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/staking/AbstractStakingStrategy.sol": {"keccak256": "0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31", "urls": ["bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f", "dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg"], "license": "BUSL-1.1"}, "src/staking/StakingStrategy.sol": {"keccak256": "0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9", "urls": ["bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2", "dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}, "src/withdraws/AbstractWithdrawRequestManager.sol": {"keccak256": "0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd", "urls": ["bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb", "dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu"], "license": "BUSL-1.1"}, "src/withdraws/ClonedCooldownHolder.sol": {"keccak256": "0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f", "urls": ["bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b", "dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM"], "license": "BUSL-1.1"}, "src/withdraws/EtherFi.sol": {"keccak256": "0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262", "urls": ["bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2", "dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L"], "license": "BUSL-1.1"}, "tests/Mocks.sol": {"keccak256": "0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a", "urls": ["bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876", "dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc"], "license": "UNLICENSED"}}, "version": 1}, "id": 108}