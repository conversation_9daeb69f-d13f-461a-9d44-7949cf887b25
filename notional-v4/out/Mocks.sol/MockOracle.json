{"abi": [{"type": "constructor", "inputs": [{"name": "_price", "type": "int256", "internalType": "int256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "SEQUENCER_UPTIME_GRACE_PERIOD", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "description", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getAnswer", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "pure"}, {"type": "function", "name": "getRoundData", "inputs": [{"name": "", "type": "uint80", "internalType": "uint80"}], "outputs": [{"name": "", "type": "uint80", "internalType": "uint80"}, {"name": "", "type": "int256", "internalType": "int256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint80", "internalType": "uint80"}], "stateMutability": "pure"}, {"type": "function", "name": "getTimestamp", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "latestAnswer", "inputs": [], "outputs": [{"name": "answer", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "latestRound", "inputs": [], "outputs": [{"name": "roundId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "latestRoundData", "inputs": [], "outputs": [{"name": "roundId", "type": "uint80", "internalType": "uint80"}, {"name": "answer", "type": "int256", "internalType": "int256"}, {"name": "startedAt", "type": "uint256", "internalType": "uint256"}, {"name": "updatedAt", "type": "uint256", "internalType": "uint256"}, {"name": "answeredInRound", "type": "uint80", "internalType": "uint80"}], "stateMutability": "view"}, {"type": "function", "name": "latestTimestamp", "inputs": [], "outputs": [{"name": "updatedAt", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "price", "inputs": [], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "sequencerUptimeO<PERSON>le", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract AggregatorV2V3Interface"}], "stateMutability": "view"}, {"type": "function", "name": "setPrice", "inputs": [{"name": "_price", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "version", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "1265:484:108:-:0;;;1342:93;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;781:218:78;;;;;;;;;;;;-1:-1:-1;;;781:218:78;;;;-1:-1:-1;;885:26:78;781:218;-1:-1:-1;885:26:78;:::i;:::-;-1:-1:-1;;;;;;921:71:78;;;-1:-1:-1;1418:5:108::1;:14:::0;1265:484;;14:183:121;83:6;136:2;124:9;115:7;111:23;107:32;104:52;;;152:1;149;142:12;104:52;-1:-1:-1;175:16:121;;14:183;-1:-1:-1;14:183:121:o;202:127::-;263:10;258:3;254:20;251:1;244:31;294:4;291:1;284:15;318:4;315:1;308:15;334:380;413:1;409:12;;;;456;;;477:61;;531:4;523:6;519:17;509:27;;477:61;584:2;576:6;573:14;553:18;550:38;547:161;;630:10;625:3;621:20;618:1;611:31;665:4;662:1;655:15;693:4;690:1;683:15;547:161;;334:380;;;:::o;845:518::-;947:2;942:3;939:11;936:421;;;983:5;980:1;973:16;1027:4;1024:1;1014:18;1097:2;1085:10;1081:19;1078:1;1074:27;1068:4;1064:38;1133:4;1121:10;1118:20;1115:47;;;-1:-1:-1;1156:4:121;1115:47;1211:2;1206:3;1202:12;1199:1;1195:20;1189:4;1185:31;1175:41;;1266:81;1284:2;1277:5;1274:13;1266:81;;;1343:1;1329:16;;1310:1;1299:13;1266:81;;;1270:3;;936:421;845:518;;;:::o;1539:1299::-;1659:10;;-1:-1:-1;;;;;1681:30:121;;1678:56;;;1714:18;;:::i;:::-;1743:97;1833:6;1793:38;1825:4;1819:11;1793:38;:::i;:::-;1787:4;1743:97;:::i;:::-;1889:4;1920:2;1909:14;;1937:1;1932:649;;;;2625:1;2642:6;2639:89;;;-1:-1:-1;2694:19:121;;;2688:26;2639:89;-1:-1:-1;;1496:1:121;1492:11;;;1488:24;1484:29;1474:40;1520:1;1516:11;;;1471:57;2741:81;;1902:930;;1932:649;792:1;785:14;;;829:4;816:18;;-1:-1:-1;;1968:20:121;;;2086:222;2100:7;2097:1;2094:14;2086:222;;;2182:19;;;2176:26;2161:42;;2289:4;2274:20;;;;2242:1;2230:14;;;;2116:12;2086:222;;;2090:3;2336:6;2327:7;2324:19;2321:201;;;2397:19;;;2391:26;-1:-1:-1;;2480:1:121;2476:14;;;2492:3;2472:24;2468:37;2464:42;2449:58;2434:74;;2321:201;-1:-1:-1;;;;2568:1:121;2552:14;;;2548:22;2535:36;;-1:-1:-1;1539:1299:121:o;:::-;1265:484:108;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1265:484:108:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;592:44:78;;634:2;592:44;;;;;186:4:121;174:17;;;156:36;;144:2;129:18;592:44:78;;;;;;;;2101:150;1697:5:108;;2101:150:78;;;347:25:121;;;335:2;320:18;2101:150:78;203:175:121;501:44:78;;544:1;501:44;;2423:152;2478:15;2423:152;;551:34;;;:::i;:::-;;;;;;;:::i;2257:160::-;1704:15:108;2257:160:78;;2623:271;;;;;;:::i;:::-;;;;1718:22:121;1706:35;;;1688:54;;1773:2;1758:18;;1751:34;;;;1801:18;;1794:34;;;;1859:2;1844:18;;1837:34;1908:35;;;1902:3;1887:19;;1880:64;1675:3;1660:19;2623:271:78;1435:515:121;1316:19:108;;;;;;2942:95:78;;;;;;:::i;643:62::-;;;;;;;;2349:42:121;2337:55;;;2319:74;;2307:2;2292:18;643:62:78;2140:259:121;711:63:78;;767:7;711:63;;1441:71:108;;;;;;:::i;:::-;1491:5;:14;1441:71;;;1823:272:78;;;:::i;551:34::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;1823:272::-;1891:14;1915:13;1938:17;1965;1992:22;2031:17;:15;:17::i;:::-;-1:-1:-1;;1697:5:108;;1583:14;;1697:5;;-1:-1:-1;1704:15:108;;-1:-1:-1;1704:15:108;;-1:-1:-1;1583:14:108;;1823:272:78:o;1214:603::-;1347:21;1339:44;;;1335:476;;1453:13;1484:17;1601:21;:37;;;:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1399:241;;;;;;;1662:6;1672:1;1662:11;1654:38;;;;;;;3906:2:121;1654:38:78;;;3888:21:121;3945:2;3925:18;;;3918:30;3984:16;3964:18;;;3957:44;4018:18;;1654:38:78;;;;;;;;;1746:27;1764:9;1746:15;:27;:::i;:::-;767:7;1714:59;1706:94;;;;;;;4536:2:121;1706:94:78;;;4518:21:121;4575:2;4555:18;;;4548:30;4614:24;4594:18;;;4587:52;4656:18;;1706:94:78;4334:346:121;1706:94:78;1385:426;;1335:476;1214:603::o;565:477:121:-;714:2;703:9;696:21;677:4;746:6;740:13;789:6;784:2;773:9;769:18;762:34;848:6;843:2;835:6;831:15;826:2;815:9;811:18;805:50;904:1;899:2;890:6;879:9;875:22;871:31;864:42;1033:2;963:66;958:2;950:6;946:15;942:88;931:9;927:104;923:113;915:121;;;565:477;;;;:::o;1047:133::-;1132:22;1125:5;1121:34;1114:5;1111:45;1101:73;;1170:1;1167;1160:12;1101:73;1047:133;:::o;1185:245::-;1243:6;1296:2;1284:9;1275:7;1271:23;1267:32;1264:52;;;1312:1;1309;1302:12;1264:52;1351:9;1338:23;1370:30;1394:5;1370:30;:::i;:::-;1419:5;1185:245;-1:-1:-1;;;1185:245:121:o;1955:180::-;2014:6;2067:2;2055:9;2046:7;2042:23;2038:32;2035:52;;;2083:1;2080;2073:12;2035:52;-1:-1:-1;2106:23:121;;1955:180;-1:-1:-1;1955:180:121:o;2588:437::-;2667:1;2663:12;;;;2710;;;2731:61;;2785:4;2777:6;2773:17;2763:27;;2731:61;2838:2;2830:6;2827:14;2807:18;2804:38;2801:218;;2875:77;2872:1;2865:88;2976:4;2973:1;2966:15;3004:4;3001:1;2994:15;2801:218;;2588:437;;;:::o;3030:669::-;3133:6;3141;3149;3157;3165;3218:3;3206:9;3197:7;3193:23;3189:33;3186:53;;;3235:1;3232;3225:12;3186:53;3267:9;3261:16;3286:30;3310:5;3286:30;:::i;:::-;3380:2;3365:18;;3359:25;3450:2;3435:18;;3429:25;3546:2;3531:18;;3525:25;3621:3;3606:19;;3600:26;3335:5;;-1:-1:-1;3359:25:121;;-1:-1:-1;3429:25:121;-1:-1:-1;3525:25:121;-1:-1:-1;3635:32:121;3600:26;3635:32;:::i;:::-;3686:7;3676:17;;;3030:669;;;;;;;;:::o;4047:282::-;4114:9;;;4135:11;;;4132:191;;;4179:77;4176:1;4169:88;4280:4;4277:1;4270:15;4308:4;4305:1;4298:15;4132:191;4047:282;;;;:::o", "linkReferences": {}, "immutableReferences": {"47993": [{"start": 420, "length": 32}, {"start": 703, "length": 32}, {"start": 765, "length": 32}]}}, "methodIdentifiers": {"SEQUENCER_UPTIME_GRACE_PERIOD()": "dc60eac9", "decimals()": "313ce567", "description()": "7284e416", "getAnswer(uint256)": "b5ab58dc", "getRoundData(uint80)": "9a6fc8f5", "getTimestamp(uint256)": "b633620c", "latestAnswer()": "50d25bcd", "latestRound()": "668a0f02", "latestRoundData()": "feaf968c", "latestTimestamp()": "8205bf6a", "price()": "a035b1fe", "sequencerUptimeOracle()": "c15ef47a", "setPrice(int256)": "f7a30806", "version()": "54fd4d50"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"_price\",\"type\":\"int256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"SEQUENCER_UPTIME_GRACE_PERIOD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"description\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"getAnswer\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint80\",\"name\":\"\",\"type\":\"uint80\"}],\"name\":\"getRoundData\",\"outputs\":[{\"internalType\":\"uint80\",\"name\":\"\",\"type\":\"uint80\"},{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint80\",\"name\":\"\",\"type\":\"uint80\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"getTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestAnswer\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"answer\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestRound\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestRoundData\",\"outputs\":[{\"internalType\":\"uint80\",\"name\":\"roundId\",\"type\":\"uint80\"},{\"internalType\":\"int256\",\"name\":\"answer\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"startedAt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"updatedAt\",\"type\":\"uint256\"},{\"internalType\":\"uint80\",\"name\":\"answeredInRound\",\"type\":\"uint80\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"updatedAt\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"price\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sequencerUptimeOracle\",\"outputs\":[{\"internalType\":\"contract AggregatorV2V3Interface\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"_price\",\"type\":\"int256\"}],\"name\":\"setPrice\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getAnswer(uint256)\":{\"details\":\"Unused in the trading module\"},\"getRoundData(uint80)\":{\"details\":\"Unused in the trading module\"},\"getTimestamp(uint256)\":{\"details\":\"Unused in the trading module\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"tests/Mocks.sol\":\"MockOracle\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e\",\"dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol\":{\"keccak256\":\"0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244\",\"dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"node_modules/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/IEtherFi.sol\":{\"keccak256\":\"0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635\",\"dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/oracles/AbstractCustomOracle.sol\":{\"keccak256\":\"0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524\",\"license\":\"BSUL-1.1\",\"urls\":[\"bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd\",\"dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/staking/AbstractStakingStrategy.sol\":{\"keccak256\":\"0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f\",\"dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg\"]},\"src/staking/StakingStrategy.sol\":{\"keccak256\":\"0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2\",\"dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]},\"src/withdraws/AbstractWithdrawRequestManager.sol\":{\"keccak256\":\"0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb\",\"dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu\"]},\"src/withdraws/ClonedCooldownHolder.sol\":{\"keccak256\":\"0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b\",\"dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM\"]},\"src/withdraws/EtherFi.sol\":{\"keccak256\":\"0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2\",\"dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L\"]},\"tests/Mocks.sol\":{\"keccak256\":\"0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876\",\"dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "int256", "name": "_price", "type": "int256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SEQUENCER_UPTIME_GRACE_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "description", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "getAnswer", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [{"internalType": "uint80", "name": "", "type": "uint80"}], "stateMutability": "pure", "type": "function", "name": "getRoundData", "outputs": [{"internalType": "uint80", "name": "", "type": "uint80"}, {"internalType": "int256", "name": "", "type": "int256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint80", "name": "", "type": "uint80"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "getTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestAnswer", "outputs": [{"internalType": "int256", "name": "answer", "type": "int256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestRound", "outputs": [{"internalType": "uint256", "name": "roundId", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestRoundData", "outputs": [{"internalType": "uint80", "name": "roundId", "type": "uint80"}, {"internalType": "int256", "name": "answer", "type": "int256"}, {"internalType": "uint256", "name": "startedAt", "type": "uint256"}, {"internalType": "uint256", "name": "updatedAt", "type": "uint256"}, {"internalType": "uint80", "name": "answeredInRound", "type": "uint80"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "latestTimestamp", "outputs": [{"internalType": "uint256", "name": "updatedAt", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "price", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "sequencerUptimeO<PERSON>le", "outputs": [{"internalType": "contract AggregatorV2V3Interface", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "int256", "name": "_price", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "setPrice"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "version", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"getAnswer(uint256)": {"details": "Unused in the trading module"}, "getRoundData(uint80)": {"details": "Unused in the trading module"}, "getTimestamp(uint256)": {"details": "Unused in the trading module"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"tests/Mocks.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055", "urls": ["bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e", "dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol": {"keccak256": "0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8", "urls": ["bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244", "dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "node_modules/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/IEtherFi.sol": {"keccak256": "0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc", "urls": ["bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635", "dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/oracles/AbstractCustomOracle.sol": {"keccak256": "0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524", "urls": ["bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd", "dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW"], "license": "BSUL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/staking/AbstractStakingStrategy.sol": {"keccak256": "0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31", "urls": ["bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f", "dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg"], "license": "BUSL-1.1"}, "src/staking/StakingStrategy.sol": {"keccak256": "0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9", "urls": ["bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2", "dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}, "src/withdraws/AbstractWithdrawRequestManager.sol": {"keccak256": "0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd", "urls": ["bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb", "dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu"], "license": "BUSL-1.1"}, "src/withdraws/ClonedCooldownHolder.sol": {"keccak256": "0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f", "urls": ["bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b", "dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM"], "license": "BUSL-1.1"}, "src/withdraws/EtherFi.sol": {"keccak256": "0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262", "urls": ["bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2", "dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L"], "license": "BUSL-1.1"}, "tests/Mocks.sol": {"keccak256": "0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a", "urls": ["bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876", "dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc"], "license": "UNLICENSED"}}, "version": 1}, "id": 108}