{"abi": [{"type": "constructor", "inputs": [{"name": "_token", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "token", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "view"}, {"type": "function", "name": "tokenPrecision", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "645:618:108:-:0;;;746:183;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1582:113:16;;;;;;;;;;;;;-1:-1:-1;;;1582:113:16;;;;;;;;;;;;;;;;-1:-1:-1;;;1582:113:16;;;1656:5;1648;:13;;;;;;:::i;:::-;-1:-1:-1;1671:7:16;:17;1681:7;1671;:17;:::i;:::-;-1:-1:-1;;815:5:108::1;:14:::0;;-1:-1:-1;;;;;;815:14:108::1;-1:-1:-1::0;;;;;815:14:108;::::1;::::0;;::::1;::::0;;;862:16:::1;::::0;;-1:-1:-1;;;862:16:108;;;;815:14;;-1:-1:-1;862:14:108::1;::::0;:16:::1;::::0;;::::1;::::0;::::1;::::0;;;;;;;;815:14;862:16:::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;856:22;::::0;:2:::1;:22;:::i;:::-;839:14;:39:::0;888:34:::1;894:10;906:15;888:5;:34::i;:::-;746:183:::0;645:618;;7362:208:16;-1:-1:-1;;;;;7432:21:16;;7428:91;;7476:32;;-1:-1:-1;;;7476:32:16;;7505:1;7476:32;;;4952:51:121;4925:18;;7476:32:16;;;;;;;;7428:91;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;:::-;7362:208;;:::o;5912:1107::-;-1:-1:-1;;;;;6001:18:16;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:16;;-1:-1:-1;5997:540:16;;-1:-1:-1;;;;;6211:15:16;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6290:50;;-1:-1:-1;;;6290:50:16;;-1:-1:-1;;;;;5364:32:121;;6290:50:16;;;5346:51:121;5413:18;;;5406:34;;;5456:18;;;5449:34;;;5319:18;;6290:50:16;5144:345:121;6240:115:16;-1:-1:-1;;;;;6475:15:16;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;-1:-1:-1;;;;;6551:16:16;;6547:425;;6714:12;:21;;;;;;;6547:425;;;-1:-1:-1;;;;;6925:13:16;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;-1:-1:-1;;;;;6987:25:16;6996:4;-1:-1:-1;;;;;6987:25:16;;7006:5;6987:25;;;;5640::121;;5628:2;5613:18;;5494:177;6987:25:16;;;;;;;;5912:1107;;;:::o;14:304:121:-;98:6;151:2;139:9;130:7;126:23;122:32;119:52;;;167:1;164;157:12;119:52;193:16;;-1:-1:-1;;;;;238:31:121;;228:42;;218:70;;284:1;281;274:12;218:70;307:5;14:304;-1:-1:-1;;;14:304:121:o;323:127::-;384:10;379:3;375:20;372:1;365:31;415:4;412:1;405:15;439:4;436:1;429:15;455:380;534:1;530:12;;;;577;;;598:61;;652:4;644:6;640:17;630:27;;598:61;705:2;697:6;694:14;674:18;671:38;668:161;;751:10;746:3;742:20;739:1;732:31;786:4;783:1;776:15;814:4;811:1;804:15;668:161;;455:380;;;:::o;966:518::-;1068:2;1063:3;1060:11;1057:421;;;1104:5;1101:1;1094:16;1148:4;1145:1;1135:18;1218:2;1206:10;1202:19;1199:1;1195:27;1189:4;1185:38;1254:4;1242:10;1239:20;1236:47;;;-1:-1:-1;1277:4:121;1236:47;1332:2;1327:3;1323:12;1320:1;1316:20;1310:4;1306:31;1296:41;;1387:81;1405:2;1398:5;1395:13;1387:81;;;1464:1;1450:16;;1431:1;1420:13;1387:81;;;1391:3;;1057:421;966:518;;;:::o;1660:1299::-;1780:10;;-1:-1:-1;;;;;1802:30:121;;1799:56;;;1835:18;;:::i;:::-;1864:97;1954:6;1914:38;1946:4;1940:11;1914:38;:::i;:::-;1908:4;1864:97;:::i;:::-;2010:4;2041:2;2030:14;;2058:1;2053:649;;;;2746:1;2763:6;2760:89;;;-1:-1:-1;2815:19:121;;;2809:26;2760:89;-1:-1:-1;;1617:1:121;1613:11;;;1609:24;1605:29;1595:40;1641:1;1637:11;;;1592:57;2862:81;;2023:930;;2053:649;913:1;906:14;;;950:4;937:18;;-1:-1:-1;;2089:20:121;;;2207:222;2221:7;2218:1;2215:14;2207:222;;;2303:19;;;2297:26;2282:42;;2410:4;2395:20;;;;2363:1;2351:14;;;;2237:12;2207:222;;;2211:3;2457:6;2448:7;2445:19;2442:201;;;2518:19;;;2512:26;-1:-1:-1;;2601:1:121;2597:14;;;2613:3;2593:24;2589:37;2585:42;2570:58;2555:74;;2442:201;-1:-1:-1;;;;2689:1:121;2673:14;;;2669:22;2656:36;;-1:-1:-1;1660:1299:121:o;2964:273::-;3032:6;3085:2;3073:9;3064:7;3060:23;3056:32;3053:52;;;3101:1;3098;3091:12;3053:52;3133:9;3127:16;3183:4;3176:5;3172:16;3165:5;3162:27;3152:55;;3203:1;3200;3193:12;3242:127;3303:10;3298:3;3294:20;3291:1;3284:31;3334:4;3331:1;3324:15;3358:4;3355:1;3348:15;3374:375;3462:1;3480:5;3494:249;3515:1;3505:8;3502:15;3494:249;;;3565:4;3560:3;3556:14;3550:4;3547:24;3544:50;;;3574:18;;:::i;:::-;3624:1;3614:8;3610:16;3607:49;;;3638:16;;;;3607:49;3721:1;3717:16;;;;;3677:15;;3494:249;;;3374:375;;;;;;:::o;3754:902::-;3803:5;3833:8;3823:80;;-1:-1:-1;3874:1:121;3888:5;;3823:80;3922:4;3912:76;;-1:-1:-1;3959:1:121;3973:5;;3912:76;4004:4;4022:1;4017:59;;;;4090:1;4085:174;;;;3997:262;;4017:59;4047:1;4038:10;;4061:5;;;4085:174;4122:3;4112:8;4109:17;4106:43;;;4129:18;;:::i;:::-;-1:-1:-1;;4185:1:121;4171:16;;4244:5;;3997:262;;4343:2;4333:8;4330:16;4324:3;4318:4;4315:13;4311:36;4305:2;4295:8;4292:16;4287:2;4281:4;4278:12;4274:35;4271:77;4268:203;;;-1:-1:-1;4380:19:121;;;4456:5;;4268:203;4503:42;-1:-1:-1;;4528:8:121;4522:4;4503:42;:::i;:::-;4581:6;4577:1;4573:6;4569:19;4560:7;4557:32;4554:58;;;4592:18;;:::i;:::-;4630:20;;-1:-1:-1;3754:902:121;;;;;:::o;4661:140::-;4719:5;4748:47;4789:4;4779:8;4775:19;4769:4;4748:47;:::i;5014:125::-;5079:9;;;5100:10;;;5097:36;;;5113:18;;:::i;5494:177::-;645:618:108;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "645:618:108:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89:16;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3902:186;;;;;;:::i;:::-;;:::i;:::-;;;1167:14:121;;1160:22;1142:41;;1130:2;1115:18;3902:186:16;1002:187:121;2803:97:16;2881:12;;2803:97;;;1340:25:121;;;1328:2;1313:18;2803:97:16;1194:177:121;4680:244:16;;;;;;:::i;:::-;;:::i;1110:151:108:-;;;;;;:::i;:::-;;:::i;:::-;;2688:82:16;;;2761:2;2128:36:121;;2116:2;2101:18;2688:82:16;1986:184:121;2933:116:16;;;;;;:::i;:::-;3024:18;;2998:7;3024:18;;;;;;;;;;;;2933:116;1962:93;;;:::i;3244:178::-;;;;;;:::i;:::-;;:::i;710:29:108:-;;;;;;935:169;;;;;;:::i;:::-;;:::i;3455:140:16:-;;;;;;:::i;:::-;3561:18;;;;3535:7;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3455:140;686:18:108;;;;;;;;;;;;2821:42:121;2809:55;;;2791:74;;2779:2;2764:18;686::108;2631:240:121;1760:89:16;1805:13;1837:5;1830:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89;:::o;3902:186::-;3975:4;735:10:23;4029:31:16;735:10:23;4045:7:16;4054:5;4029:8;:31::i;:::-;4077:4;4070:11;;;3902:186;;;;;:::o;4680:244::-;4767:4;735:10:23;4823:37:16;4839:4;735:10:23;4854:5:16;4823:15;:37::i;:::-;4870:26;4880:4;4886:2;4890:5;4870:9;:26::i;:::-;-1:-1:-1;4913:4:16;;4680:244;-1:-1:-1;;;;4680:244:16:o;1110:151:108:-;1161:25;1167:10;1179:6;1161:5;:25::i;:::-;1196:5;;1232:14;;1196:5;;;;;:14;;1211:10;;1249:4;;1223:23;;:6;:23;:::i;:::-;:30;;;;:::i;:::-;1196:58;;;;;;;;;;4163:42:121;4151:55;;;1196:58:108;;;4133:74:121;4223:18;;;4216:34;4106:18;;1196:58:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;1110:151;:::o;1962:93:16:-;2009:13;2041:7;2034:14;;;;;:::i;3244:178::-;3313:4;735:10:23;3367:27:16;735:10:23;3384:2:16;3388:5;3367:9;:27::i;935:169:108:-;985:5;;:53;;;;;1004:10;985:53;;;4745:74:121;1024:4:108;4835:18:121;;;4828:83;4927:18;;;4920:34;;;985:5:108;;;;;:18;;4718::121;;985:53:108;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;1048:49;1054:10;1082:14;;1066:6;1075:4;1066:13;;;;:::i;:::-;:30;;;;:::i;:::-;1048:5;:49::i;:::-;935:169;:::o;8630:128:16:-;8714:37;8723:5;8730:7;8739:5;8746:4;8714:8;:37::i;:::-;8630:128;;;:::o;10319:476::-;3561:18;;;;10418:24;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;10503:17;10484:36;;10480:309;;;10559:5;10540:16;:24;10536:130;;;10591:60;;;;;5197:42:121;5185:55;;10591:60:16;;;5167:74:121;5257:18;;;5250:34;;;5300:18;;;5293:34;;;5140:18;;10591:60:16;;;;;;;;10536:130;10707:57;10716:5;10723:7;10751:5;10732:16;:24;10758:5;10707:8;:57::i;:::-;10408:387;10319:476;;;:::o;5297:300::-;5380:18;;;5376:86;;5421:30;;;;;5448:1;5421:30;;;2791:74:121;2764:18;;5421:30:16;2631:240:121;5376:86:16;5475:16;;;5471:86;;5514:32;;;;;5543:1;5514:32;;;2791:74:121;2764:18;;5514:32:16;2631:240:121;5471:86:16;5566:24;5574:4;5580:2;5584:5;5566:7;:24::i;7888:206::-;7958:21;;;7954:89;;8002:30;;;;;8029:1;8002:30;;;2791:74:121;2764:18;;8002:30:16;2631:240:121;7954:89:16;8052:35;8060:7;8077:1;8081:5;8052:7;:35::i;7362:208::-;7432:21;;;7428:91;;7476:32;;;;;7505:1;7476:32;;;2791:74:121;2764:18;;7476:32:16;2631:240:121;7428:91:16;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;9605:432::-;9717:19;;;9713:89;;9759:32;;;;;9788:1;9759:32;;;2791:74:121;2764:18;;9759:32:16;2631:240:121;9713:89:16;9815:21;;;9811:90;;9859:31;;;;;9887:1;9859:31;;;2791:74:121;2764:18;;9859:31:16;2631:240:121;9811:90:16;9910:18;;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;9955:76;;;;10005:7;9989:31;;9998:5;9989:31;;;10014:5;9989:31;;;;1340:25:121;;1328:2;1313:18;;1194:177;9989:31:16;;;;;;;;9605:432;;;;:::o;5912:1107::-;6001:18;;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:16;;-1:-1:-1;5997:540:16;;6211:15;;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6290:50;;;;;5197:42:121;5185:55;;6290:50:16;;;5167:74:121;5257:18;;;5250:34;;;5300:18;;;5293:34;;;5140:18;;6290:50:16;4965:368:121;6240:115:16;6475:15;;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;6551:16;;;6547:425;;6714:12;:21;;;;;;;6547:425;;;6925:13;;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;6987:25;;6996:4;6987:25;;;7006:5;6987:25;;;;1340::121;;1328:2;1313:18;;1194:177;6987:25:16;;;;;;;;5912:1107;;;:::o;14:477:121:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;297:6;292:2;284:6;280:15;275:2;264:9;260:18;254:50;353:1;348:2;339:6;328:9;324:22;320:31;313:42;482:2;412:66;407:2;399:6;395:15;391:88;380:9;376:104;372:113;364:121;;;14:477;;;;:::o;496:196::-;564:20;;624:42;613:54;;603:65;;593:93;;682:1;679;672:12;593:93;496:196;;;:::o;697:300::-;765:6;773;826:2;814:9;805:7;801:23;797:32;794:52;;;842:1;839;832:12;794:52;865:29;884:9;865:29;:::i;:::-;855:39;963:2;948:18;;;;935:32;;-1:-1:-1;;;697:300:121:o;1376:374::-;1453:6;1461;1469;1522:2;1510:9;1501:7;1497:23;1493:32;1490:52;;;1538:1;1535;1528:12;1490:52;1561:29;1580:9;1561:29;:::i;:::-;1551:39;;1609:38;1643:2;1632:9;1628:18;1609:38;:::i;:::-;1376:374;;1599:48;;-1:-1:-1;;;1716:2:121;1701:18;;;;1688:32;;1376:374::o;1755:226::-;1814:6;1867:2;1855:9;1846:7;1842:23;1838:32;1835:52;;;1883:1;1880;1873:12;1835:52;-1:-1:-1;1928:23:121;;1755:226;-1:-1:-1;1755:226:121:o;2175:186::-;2234:6;2287:2;2275:9;2266:7;2262:23;2258:32;2255:52;;;2303:1;2300;2293:12;2255:52;2326:29;2345:9;2326:29;:::i;:::-;2316:39;2175:186;-1:-1:-1;;;2175:186:121:o;2366:260::-;2434:6;2442;2495:2;2483:9;2474:7;2470:23;2466:32;2463:52;;;2511:1;2508;2501:12;2463:52;2534:29;2553:9;2534:29;:::i;:::-;2524:39;;2582:38;2616:2;2605:9;2601:18;2582:38;:::i;:::-;2572:48;;2366:260;;;;;:::o;2876:437::-;2955:1;2951:12;;;;2998;;;3019:61;;3073:4;3065:6;3061:17;3051:27;;3019:61;3126:2;3118:6;3115:14;3095:18;3092:38;3089:218;;3163:77;3160:1;3153:88;3264:4;3261:1;3254:15;3292:4;3289:1;3282:15;3089:218;;2876:437;;;:::o;3318:184::-;3370:77;3367:1;3360:88;3467:4;3464:1;3457:15;3491:4;3488:1;3481:15;3507:168;3580:9;;;3611;;3628:15;;;3622:22;;3608:37;3598:71;;3649:18;;:::i;3680:274::-;3720:1;3746;3736:189;;3781:77;3778:1;3771:88;3882:4;3879:1;3872:15;3910:4;3907:1;3900:15;3736:189;-1:-1:-1;3939:9:121;;3680:274::o;4261:277::-;4328:6;4381:2;4369:9;4360:7;4356:23;4352:32;4349:52;;;4397:1;4394;4387:12;4349:52;4429:9;4423:16;4482:5;4475:13;4468:21;4461:5;4458:32;4448:60;;4504:1;4501;4494:12;5569:125;5634:9;;;5655:10;;;5652:36;;;5668:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "deposit(uint256)": "b6b55f25", "name()": "06fdde03", "symbol()": "95d89b41", "token()": "fc0c546a", "tokenPrecision()": "b5855eca", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "withdraw(uint256)": "2e1a7d4d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"_token\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token\",\"outputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenPrecision\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"tests/Mocks.sol\":\"MockWrapperERC20\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e\",\"dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol\":{\"keccak256\":\"0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244\",\"dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"node_modules/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/IEtherFi.sol\":{\"keccak256\":\"0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635\",\"dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/oracles/AbstractCustomOracle.sol\":{\"keccak256\":\"0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524\",\"license\":\"BSUL-1.1\",\"urls\":[\"bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd\",\"dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/staking/AbstractStakingStrategy.sol\":{\"keccak256\":\"0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f\",\"dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg\"]},\"src/staking/StakingStrategy.sol\":{\"keccak256\":\"0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2\",\"dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]},\"src/withdraws/AbstractWithdrawRequestManager.sol\":{\"keccak256\":\"0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb\",\"dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu\"]},\"src/withdraws/ClonedCooldownHolder.sol\":{\"keccak256\":\"0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b\",\"dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM\"]},\"src/withdraws/EtherFi.sol\":{\"keccak256\":\"0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2\",\"dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L\"]},\"tests/Mocks.sol\":{\"keccak256\":\"0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876\",\"dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "contract ERC20", "name": "_token", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "tokenPrecision", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "name()": {"details": "Returns the name of the token."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"tests/Mocks.sol": "MockWrapperERC20"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055", "urls": ["bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e", "dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol": {"keccak256": "0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8", "urls": ["bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244", "dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "node_modules/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/IEtherFi.sol": {"keccak256": "0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc", "urls": ["bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635", "dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/oracles/AbstractCustomOracle.sol": {"keccak256": "0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524", "urls": ["bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd", "dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW"], "license": "BSUL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/staking/AbstractStakingStrategy.sol": {"keccak256": "0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31", "urls": ["bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f", "dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg"], "license": "BUSL-1.1"}, "src/staking/StakingStrategy.sol": {"keccak256": "0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9", "urls": ["bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2", "dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}, "src/withdraws/AbstractWithdrawRequestManager.sol": {"keccak256": "0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd", "urls": ["bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb", "dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu"], "license": "BUSL-1.1"}, "src/withdraws/ClonedCooldownHolder.sol": {"keccak256": "0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f", "urls": ["bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b", "dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM"], "license": "BUSL-1.1"}, "src/withdraws/EtherFi.sol": {"keccak256": "0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262", "urls": ["bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2", "dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L"], "license": "BUSL-1.1"}, "tests/Mocks.sol": {"keccak256": "0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a", "urls": ["bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876", "dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc"], "license": "UNLICENSED"}}, "version": 1}, "id": 108}