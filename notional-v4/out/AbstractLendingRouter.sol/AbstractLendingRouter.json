{"abi": [{"type": "function", "name": "balanceOfCollateral", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "collateralBalance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimRewards", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewards", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "depositAssetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}, {"name": "depositData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}, {"name": "assetToRepay", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "forceWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "healthFactor", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "borrowed", "type": "uint256", "internalType": "uint256"}, {"name": "collateralValue", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApproved", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidate", "type": "uint256", "internalType": "uint256"}, {"name": "debtToRepay", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "sharesToLiquidator", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "migratePosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "migrateFrom", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "set<PERSON><PERSON><PERSON>al", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "error", "name": "CannotEnterPosition", "inputs": []}, {"type": "error", "name": "CannotExitPositionWithinCooldownPeriod", "inputs": []}, {"type": "error", "name": "CannotForceWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "CannotLiquidateZeroShares", "inputs": []}, {"type": "error", "name": "InsufficientSharesHeld", "inputs": []}, {"type": "error", "name": "InvalidLendingRouter", "inputs": []}, {"type": "error", "name": "LiquidatorHasPosition", "inputs": []}, {"type": "error", "name": "NoExistingPosition", "inputs": []}, {"type": "error", "name": "NotAuthorized", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "user", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"balanceOfCollateral(address,address)": "da3a855f", "claimRewards(address)": "ef5cfb8c", "enterPosition(address,address,uint256,uint256,bytes)": "de13c617", "exitPosition(address,address,address,uint256,uint256,bytes)": "ef9a0b61", "forceWithdraw(address,address,bytes)": "4a7f87e8", "healthFactor(address,address)": "576f5c40", "initiateWithdraw(address,address,bytes)": "c0b5de76", "isApproved(address,address)": "a389783e", "liquidate(address,address,uint256,uint256)": "c1342574", "migratePosition(address,address,address)": "c139cc98", "setApproval(address,bool)": "db9b7170"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"CannotEnterPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotExitPositionWithinCooldownPeriod\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"CannotForceWithdraw\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotLiquidateZeroShares\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientSharesHeld\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidLendingRouter\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LiquidatorHasPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoExistingPosition\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"NotAuthorized\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"balanceOfCollateral\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"collateralBalance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"claimRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewards\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositAssetAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"depositData\",\"type\":\"bytes\"}],\"name\":\"enterPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToRedeem\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"assetToRepay\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"exitPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"forceWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"healthFactor\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"borrowed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"collateralValue\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxBorrow\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApproved\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"debtToRepay\",\"type\":\"uint256\"}],\"name\":\"liquidate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidator\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"migrateFrom\",\"type\":\"address\"}],\"name\":\"migratePosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApproval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"balanceOfCollateral(address,address)\":{\"details\":\"Returns the balance of collateral of a user for a given vault.\",\"params\":{\"account\":\"The address of the account.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"collateralBalance\":\"The balance of collateral.\"}},\"claimRewards(address)\":{\"details\":\"Claims rewards for a user for a given vault.\",\"params\":{\"vault\":\"The address of the vault.\"},\"returns\":{\"rewards\":\"The rewards.\"}},\"enterPosition(address,address,uint256,uint256,bytes)\":{\"details\":\"Enters a position in the lending market.\",\"params\":{\"borrowAmount\":\"The amount of assets to borrow.\",\"depositAssetAmount\":\"The amount of margin to deposit.\",\"depositData\":\"The data to pass to the deposit function.\",\"onBehalf\":\"The address of the user to enter the position on behalf of.\",\"vault\":\"The address of the vault.\"}},\"exitPosition(address,address,address,uint256,uint256,bytes)\":{\"details\":\"Exits a position in the lending market. Can be called by the user or another lending router to migrate a position.\",\"params\":{\"assetToRepay\":\"The amount of assets to repay, if set to uint256.max the full debt will be repaid.\",\"onBehalf\":\"The address of the user to exit the position on behalf of.\",\"receiver\":\"The address of the receiver.\",\"redeemData\":\"Vault specific instructions for the exit.\",\"sharesToRedeem\":\"The amount of shares to redeem.\",\"vault\":\"The address of the vault.\"}},\"forceWithdraw(address,address,bytes)\":{\"details\":\"Forces a withdraw for a user for a given vault, only allowed if the health factor is negative.\",\"params\":{\"account\":\"The address of the account.\",\"data\":\"Vault specific instructions for the withdraw.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"requestId\":\"The request id.\"}},\"healthFactor(address,address)\":{\"details\":\"Returns the health factor of a user for a given vault.\",\"params\":{\"borrower\":\"The address of the borrower.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"borrowed\":\"The borrowed amount.\",\"collateralValue\":\"The collateral value.\",\"maxBorrow\":\"The max borrow amount.\"}},\"initiateWithdraw(address,address,bytes)\":{\"details\":\"Initiates a withdraw request for a user for a given vault.\",\"params\":{\"data\":\"Vault specific instructions for the withdraw.\",\"onBehalf\":\"The address of the user to initiate the withdraw on behalf of.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"requestId\":\"The request id.\"}},\"isApproved(address,address)\":{\"details\":\"Returns the authorization status of an address.\",\"params\":{\"operator\":\"The address to check the authorization status of.\",\"user\":\"The address to check the authorization status of.\"},\"returns\":{\"_0\":\"The authorization status.\"}},\"liquidate(address,address,uint256,uint256)\":{\"details\":\"Liquidates a position in the lending market.\",\"params\":{\"liquidateAccount\":\"The address of the account to liquidate.\",\"repaidShares\":\"The amount of shares to repay.\",\"seizedAssets\":\"The amount of assets to seize.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"sharesToLiquidator\":\"The amount of shares to liquidator.\"}},\"migratePosition(address,address,address)\":{\"details\":\"Migrates a position to the lending market.\",\"params\":{\"migrateFrom\":\"The address of the lending router to migrate the position from.\",\"onBehalf\":\"The address of the user to migrate the position on behalf of.\",\"vault\":\"The address of the vault.\"}},\"setApproval(address,bool)\":{\"details\":\"Authorizes an address to manage a user's position.\",\"params\":{\"approved\":\"The authorization status.\",\"operator\":\"The address to authorize.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/routers/AbstractLendingRouter.sol\":\"AbstractLendingRouter\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/routers/AbstractLendingRouter.sol\":{\"keccak256\":\"0x76c82e77d6495512ba1a397c63c10bed272b0b7e4c1427b382e0f1e03caac6e0\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b2a5aef92cf9f8b4366747cd0ebb8c0e99439f1e9f0f3de466e5e2aad7161788\",\"dweb:/ipfs/QmR7tnF6L6YwB6UnU61NX3N7NZTY3GuKFyfGfUnGphjJ23\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "CannotEnterPosition"}, {"inputs": [], "type": "error", "name": "CannotExitPositionWithinCooldownPeriod"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "CannotForceWithdraw"}, {"inputs": [], "type": "error", "name": "CannotLiquidateZeroShares"}, {"inputs": [], "type": "error", "name": "InsufficientSharesHeld"}, {"inputs": [], "type": "error", "name": "InvalidLendingRouter"}, {"inputs": [], "type": "error", "name": "LiquidatorHasPosition"}, {"inputs": [], "type": "error", "name": "NoExistingPosition"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "type": "error", "name": "NotAuthorized"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOfCollateral", "outputs": [{"internalType": "uint256", "name": "collateralBalance", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimRewards", "outputs": [{"internalType": "uint256[]", "name": "rewards", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "depositAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}, {"internalType": "bytes", "name": "depositData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "enterPosition"}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "sharesToRedeem", "type": "uint256"}, {"internalType": "uint256", "name": "assetToRepay", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "exitPosition"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "forceWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "healthFactor", "outputs": [{"internalType": "uint256", "name": "borrowed", "type": "uint256"}, {"internalType": "uint256", "name": "collateralValue", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidate", "type": "uint256"}, {"internalType": "uint256", "name": "debtToRepay", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate", "outputs": [{"internalType": "uint256", "name": "sharesToLiquidator", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "migrateFrom", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "migratePosition"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "set<PERSON><PERSON><PERSON>al"}], "devdoc": {"kind": "dev", "methods": {"balanceOfCollateral(address,address)": {"details": "Returns the balance of collateral of a user for a given vault.", "params": {"account": "The address of the account.", "vault": "The address of the vault."}, "returns": {"collateralBalance": "The balance of collateral."}}, "claimRewards(address)": {"details": "Claims rewards for a user for a given vault.", "params": {"vault": "The address of the vault."}, "returns": {"rewards": "The rewards."}}, "enterPosition(address,address,uint256,uint256,bytes)": {"details": "Enters a position in the lending market.", "params": {"borrowAmount": "The amount of assets to borrow.", "depositAssetAmount": "The amount of margin to deposit.", "depositData": "The data to pass to the deposit function.", "onBehalf": "The address of the user to enter the position on behalf of.", "vault": "The address of the vault."}}, "exitPosition(address,address,address,uint256,uint256,bytes)": {"details": "Exits a position in the lending market. Can be called by the user or another lending router to migrate a position.", "params": {"assetToRepay": "The amount of assets to repay, if set to uint256.max the full debt will be repaid.", "onBehalf": "The address of the user to exit the position on behalf of.", "receiver": "The address of the receiver.", "redeemData": "Vault specific instructions for the exit.", "sharesToRedeem": "The amount of shares to redeem.", "vault": "The address of the vault."}}, "forceWithdraw(address,address,bytes)": {"details": "Forces a withdraw for a user for a given vault, only allowed if the health factor is negative.", "params": {"account": "The address of the account.", "data": "<PERSON><PERSON> specific instructions for the withdraw.", "vault": "The address of the vault."}, "returns": {"requestId": "The request id."}}, "healthFactor(address,address)": {"details": "Returns the health factor of a user for a given vault.", "params": {"borrower": "The address of the borrower.", "vault": "The address of the vault."}, "returns": {"borrowed": "The borrowed amount.", "collateralValue": "The collateral value.", "maxBorrow": "The max borrow amount."}}, "initiateWithdraw(address,address,bytes)": {"details": "Initiates a withdraw request for a user for a given vault.", "params": {"data": "<PERSON><PERSON> specific instructions for the withdraw.", "onBehalf": "The address of the user to initiate the withdraw on behalf of.", "vault": "The address of the vault."}, "returns": {"requestId": "The request id."}}, "isApproved(address,address)": {"details": "Returns the authorization status of an address.", "params": {"operator": "The address to check the authorization status of.", "user": "The address to check the authorization status of."}, "returns": {"_0": "The authorization status."}}, "liquidate(address,address,uint256,uint256)": {"details": "Liquidates a position in the lending market.", "params": {"liquidateAccount": "The address of the account to liquidate.", "repaidShares": "The amount of shares to repay.", "seizedAssets": "The amount of assets to seize.", "vault": "The address of the vault."}, "returns": {"sharesToLiquidator": "The amount of shares to liquidator."}}, "migratePosition(address,address,address)": {"details": "Migrates a position to the lending market.", "params": {"migrateFrom": "The address of the lending router to migrate the position from.", "onBehalf": "The address of the user to migrate the position on behalf of.", "vault": "The address of the vault."}}, "setApproval(address,bool)": {"details": "Authorizes an address to manage a user's position.", "params": {"approved": "The authorization status.", "operator": "The address to authorize."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/routers/AbstractLendingRouter.sol": "AbstractLendingRouter"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/routers/AbstractLendingRouter.sol": {"keccak256": "0x76c82e77d6495512ba1a397c63c10bed272b0b7e4c1427b382e0f1e03caac6e0", "urls": ["bzz-raw://b2a5aef92cf9f8b4366747cd0ebb8c0e99439f1e9f0f3de466e5e2aad7161788", "dweb:/ipfs/QmR7tnF6L6YwB6UnU61NX3N7NZTY3GuKFyfGfUnGphjJ23"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}}, "version": 1}, "id": 88}