{"id": "68a05f9fb62ef5d8", "source_id_to_path": {"0": "node_modules/@openzeppelin/contracts/interfaces/IERC1155.sol", "1": "node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol", "2": "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol", "3": "node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol", "4": "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol", "5": "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol", "6": "node_modules/@openzeppelin/contracts/interfaces/IERC4626.sol", "7": "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "8": "node_modules/@openzeppelin/contracts/proxy/Clones.sol", "9": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "10": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "11": "node_modules/@openzeppelin/contracts/proxy/Proxy.sol", "12": "node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol", "13": "node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155.sol", "14": "node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol", "15": "node_modules/@openzeppelin/contracts/token/ERC1155/utils/ERC1155Holder.sol", "16": "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "17": "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "18": "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "19": "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "20": "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "21": "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol", "22": "node_modules/@openzeppelin/contracts/utils/Address.sol", "23": "node_modules/@openzeppelin/contracts/utils/Context.sol", "24": "node_modules/@openzeppelin/contracts/utils/Create2.sol", "25": "node_modules/@openzeppelin/contracts/utils/Errors.sol", "26": "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol", "27": "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol", "28": "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol", "29": "node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol", "30": "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol", "31": "node_modules/forge-std/src/Base.sol", "32": "node_modules/forge-std/src/StdAssertions.sol", "33": "node_modules/forge-std/src/StdChains.sol", "34": "node_modules/forge-std/src/StdCheats.sol", "35": "node_modules/forge-std/src/StdError.sol", "36": "node_modules/forge-std/src/StdInvariant.sol", "37": "node_modules/forge-std/src/StdJson.sol", "38": "node_modules/forge-std/src/StdMath.sol", "39": "node_modules/forge-std/src/StdStorage.sol", "40": "node_modules/forge-std/src/StdStyle.sol", "41": "node_modules/forge-std/src/StdToml.sol", "42": "node_modules/forge-std/src/StdUtils.sol", "43": "node_modules/forge-std/src/Test.sol", "44": "node_modules/forge-std/src/Vm.sol", "45": "node_modules/forge-std/src/console.sol", "46": "node_modules/forge-std/src/console2.sol", "47": "node_modules/forge-std/src/interfaces/IMulticall3.sol", "48": "node_modules/forge-std/src/safeconsole.sol", "49": "src/AbstractYieldStrategy.sol", "50": "src/interfaces/AggregatorV2V3Interface.sol", "51": "src/interfaces/Curve/IConvex.sol", "52": "src/interfaces/Curve/ICurve.sol", "53": "src/interfaces/Errors.sol", "54": "src/interfaces/IDinero.sol", "55": "src/interfaces/IEIP20NonStandard.sol", "56": "src/interfaces/IEthena.sol", "57": "src/interfaces/IEtherFi.sol", "58": "src/interfaces/ILendingRouter.sol", "59": "src/interfaces/IOrigin.sol", "60": "src/interfaces/IRewardManager.sol", "61": "src/interfaces/ISingleSidedLP.sol", "62": "src/interfaces/ITradingModule.sol", "63": "src/interfaces/IWETH.sol", "64": "src/interfaces/IWithdrawRequestManager.sol", "65": "src/interfaces/IYieldStrategy.sol", "66": "src/interfaces/Morpho/IMorpho.sol", "67": "src/interfaces/Morpho/IMorphoCallbacks.sol", "68": "src/interfaces/Morpho/IOracle.sol", "69": "src/oracles/AbstractCustomOracle.sol", "70": "src/oracles/AbstractLPOracle.sol", "71": "src/oracles/Curve2TokenOracle.sol", "72": "src/proxy/AddressRegistry.sol", "73": "src/proxy/Initializable.sol", "74": "src/proxy/TimelockUpgradeableProxy.sol", "75": "src/rewards/AbstractRewardManager.sol", "76": "src/rewards/ConvexRewardManager.sol", "77": "src/rewards/RewardManagerMixin.sol", "78": "src/routers/AbstractLendingRouter.sol", "79": "src/routers/MorphoLendingRouter.sol", "80": "src/single-sided-lp/AbstractSingleSidedLP.sol", "81": "src/single-sided-lp/CurveConvex2Token.sol", "82": "src/staking/AbstractStakingStrategy.sol", "83": "src/staking/StakingStrategy.sol", "84": "src/utils/Constants.sol", "85": "src/utils/TokenUtils.sol", "86": "src/utils/TypeConvert.sol", "87": "src/withdraws/AbstractWithdrawRequestManager.sol", "88": "src/withdraws/ClonedCooldownHolder.sol", "89": "src/withdraws/Dinero.sol", "90": "src/withdraws/Ethena.sol", "91": "src/withdraws/EtherFi.sol", "92": "src/withdraws/GenericERC20.sol", "93": "src/withdraws/GenericERC4626.sol", "94": "src/withdraws/Origin.sol", "95": "tests/Mocks.sol", "96": "tests/TestEnvironment.sol", "97": "tests/TestMorphoYieldStrategy.sol", "98": "tests/TestSingleSidedLPStrategy.sol", "99": "tests/TestSingleSidedLPStrategyImpl.sol", "100": "tests/TestWithdrawRequest.sol", "101": "tests/TestWithdrawRequestImpl.sol"}, "language": "Solidity"}