{"id": "2f59418e173d8cf4", "source_id_to_path": {"0": "node_modules/@openzeppelin/contracts/interfaces/IERC1155.sol", "1": "node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol", "2": "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol", "3": "node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol", "4": "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol", "5": "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol", "6": "node_modules/@openzeppelin/contracts/interfaces/IERC4626.sol", "7": "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "8": "node_modules/@openzeppelin/contracts/proxy/Clones.sol", "9": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol", "10": "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol", "11": "node_modules/@openzeppelin/contracts/proxy/Proxy.sol", "12": "node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol", "13": "node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155.sol", "14": "node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol", "15": "node_modules/@openzeppelin/contracts/token/ERC1155/utils/ERC1155Holder.sol", "16": "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol", "17": "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol", "18": "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "19": "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "20": "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "21": "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol", "22": "node_modules/@openzeppelin/contracts/utils/Address.sol", "23": "node_modules/@openzeppelin/contracts/utils/Context.sol", "24": "node_modules/@openzeppelin/contracts/utils/Create2.sol", "25": "node_modules/@openzeppelin/contracts/utils/Errors.sol", "26": "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol", "27": "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol", "28": "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol", "29": "node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol", "30": "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol", "31": "node_modules/forge-std/src/Base.sol", "32": "node_modules/forge-std/src/Script.sol", "33": "node_modules/forge-std/src/StdAssertions.sol", "34": "node_modules/forge-std/src/StdChains.sol", "35": "node_modules/forge-std/src/StdCheats.sol", "36": "node_modules/forge-std/src/StdError.sol", "37": "node_modules/forge-std/src/StdInvariant.sol", "38": "node_modules/forge-std/src/StdJson.sol", "39": "node_modules/forge-std/src/StdMath.sol", "40": "node_modules/forge-std/src/StdStorage.sol", "41": "node_modules/forge-std/src/StdStyle.sol", "42": "node_modules/forge-std/src/StdToml.sol", "43": "node_modules/forge-std/src/StdUtils.sol", "44": "node_modules/forge-std/src/Test.sol", "45": "node_modules/forge-std/src/Vm.sol", "46": "node_modules/forge-std/src/console.sol", "47": "node_modules/forge-std/src/console2.sol", "48": "node_modules/forge-std/src/interfaces/IMulticall3.sol", "49": "node_modules/forge-std/src/safeconsole.sol", "50": "script/DeployAddressRegistry.sol", "51": "script/DeployVault.sol", "52": "script/DeployWithdrawManager.sol", "53": "script/GnosisHelper.sol", "54": "src/AbstractYieldStrategy.sol", "55": "src/interfaces/AggregatorV2V3Interface.sol", "56": "src/interfaces/Balancer/IAura.sol", "57": "src/interfaces/Balancer/IBalancerPool.sol", "58": "src/interfaces/Balancer/IBalancerVault.sol", "59": "src/interfaces/Curve/IConvex.sol", "60": "src/interfaces/Curve/ICurve.sol", "61": "src/interfaces/Errors.sol", "62": "src/interfaces/IDinero.sol", "63": "src/interfaces/IEIP20NonStandard.sol", "64": "src/interfaces/IEthena.sol", "65": "src/interfaces/IEtherFi.sol", "66": "src/interfaces/ILendingRouter.sol", "67": "src/interfaces/IOrigin.sol", "68": "src/interfaces/IPendle.sol", "69": "src/interfaces/IRewardManager.sol", "70": "src/interfaces/ISingleSidedLP.sol", "71": "src/interfaces/ITradingModule.sol", "72": "src/interfaces/IWETH.sol", "73": "src/interfaces/IWithdrawRequestManager.sol", "74": "src/interfaces/IYieldStrategy.sol", "75": "src/interfaces/Morpho/IMorpho.sol", "76": "src/interfaces/Morpho/IMorphoCallbacks.sol", "77": "src/interfaces/Morpho/IOracle.sol", "78": "src/oracles/AbstractCustomOracle.sol", "79": "src/oracles/AbstractLPOracle.sol", "80": "src/oracles/Curve2TokenOracle.sol", "81": "src/oracles/PendlePTOracle.sol", "82": "src/proxy/AddressRegistry.sol", "83": "src/proxy/Initializable.sol", "84": "src/proxy/TimelockUpgradeableProxy.sol", "85": "src/rewards/AbstractRewardManager.sol", "86": "src/rewards/ConvexRewardManager.sol", "87": "src/rewards/RewardManagerMixin.sol", "88": "src/routers/AbstractLendingRouter.sol", "89": "src/routers/MorphoLendingRouter.sol", "90": "src/single-sided-lp/AbstractSingleSidedLP.sol", "91": "src/single-sided-lp/CurveConvex2Token.sol", "92": "src/staking/AbstractStakingStrategy.sol", "93": "src/staking/PendlePT.sol", "94": "src/staking/PendlePTLib.sol", "95": "src/staking/PendlePT_sUSDe.sol", "96": "src/staking/StakingStrategy.sol", "97": "src/utils/Constants.sol", "98": "src/utils/TokenUtils.sol", "99": "src/utils/TypeConvert.sol", "100": "src/withdraws/AbstractWithdrawRequestManager.sol", "101": "src/withdraws/ClonedCooldownHolder.sol", "102": "src/withdraws/Dinero.sol", "103": "src/withdraws/Ethena.sol", "104": "src/withdraws/EtherFi.sol", "105": "src/withdraws/GenericERC20.sol", "106": "src/withdraws/GenericERC4626.sol", "107": "src/withdraws/Origin.sol", "108": "tests/Mocks.sol", "109": "tests/TestDilutionAttack.sol", "110": "tests/TestEnvironment.sol", "111": "tests/TestMorphoYieldStrategy.sol", "112": "tests/TestPTStrategyImpl.sol", "113": "tests/TestRewardManager.sol", "114": "tests/TestSingleSidedLPStrategy.sol", "115": "tests/TestSingleSidedLPStrategyImpl.sol", "116": "tests/TestStakingStrategy.sol", "117": "tests/TestStakingStrategyImpl.sol", "118": "tests/TestTimelockProxy.sol", "119": "tests/TestWithdrawRequest.sol", "120": "tests/TestWithdrawRequestImpl.sol"}, "language": "Solidity"}