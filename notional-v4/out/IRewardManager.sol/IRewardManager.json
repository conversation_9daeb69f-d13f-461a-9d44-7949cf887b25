{"abi": [{"type": "function", "name": "claimRewardTokens", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getRewardDebt", "inputs": [{"name": "rewardToken", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewardDebt", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRewardSettings", "inputs": [], "outputs": [{"name": "rewardStates", "type": "tuple[]", "internalType": "struct VaultRewardState[]", "components": [{"name": "rewardToken", "type": "address", "internalType": "address"}, {"name": "lastAccumulatedTime", "type": "uint32", "internalType": "uint32"}, {"name": "endTime", "type": "uint32", "internalType": "uint32"}, {"name": "emissionRatePerYear", "type": "uint128", "internalType": "uint128"}, {"name": "accumulatedRewardPerVaultShare", "type": "uint128", "internalType": "uint128"}]}, {"name": "rewardPool", "type": "tuple", "internalType": "struct RewardPoolStorage", "components": [{"name": "rewardPool", "type": "address", "internalType": "address"}, {"name": "lastClaimTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "forceClaimAfter", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "migrateRewardPool", "inputs": [{"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "newRewardPool", "type": "tuple", "internalType": "struct RewardPoolStorage", "components": [{"name": "rewardPool", "type": "address", "internalType": "address"}, {"name": "lastClaimTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "forceClaimAfter", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAccountRewards", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "effectiveSupplyBefore", "type": "uint256", "internalType": "uint256"}, {"name": "accountSharesBefore", "type": "uint256", "internalType": "uint256"}, {"name": "accountSharesAfter", "type": "uint256", "internalType": "uint256"}, {"name": "sharesInEscrow", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "rewards", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateRewardToken", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}, {"name": "rewardToken", "type": "address", "internalType": "address"}, {"name": "emissionRatePerYear", "type": "uint128", "internalType": "uint128"}, {"name": "endTime", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "VaultRewardTransfer", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "VaultRewardUpdate", "inputs": [{"name": "rewardToken", "type": "address", "indexed": false, "internalType": "address"}, {"name": "emissionRatePerYear", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "endTime", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"claimRewardTokens()": "71df13da", "getRewardDebt(address,address)": "ada98cc3", "getRewardSettings()": "89e53e6b", "migrateRewardPool(address,(address,uint32,uint32))": "********", "updateAccountRewards(address,uint256,uint256,uint256,bool)": "49ba860d", "updateRewardToken(uint256,address,uint128,uint32)": "cd05b4e1"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"VaultRewardTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"emissionRatePerYear\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"endTime\",\"type\":\"uint32\"}],\"name\":\"VaultRewardUpdate\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"claimRewardTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getRewardDebt\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"rewardDebt\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardSettings\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastAccumulatedTime\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"endTime\",\"type\":\"uint32\"},{\"internalType\":\"uint128\",\"name\":\"emissionRatePerYear\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"accumulatedRewardPerVaultShare\",\"type\":\"uint128\"}],\"internalType\":\"struct VaultRewardState[]\",\"name\":\"rewardStates\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"rewardPool\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastClaimTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"forceClaimAfter\",\"type\":\"uint32\"}],\"internalType\":\"struct RewardPoolStorage\",\"name\":\"rewardPool\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"rewardPool\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"lastClaimTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"forceClaimAfter\",\"type\":\"uint32\"}],\"internalType\":\"struct RewardPoolStorage\",\"name\":\"newRewardPool\",\"type\":\"tuple\"}],\"name\":\"migrateRewardPool\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"effectiveSupplyBefore\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accountSharesBefore\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accountSharesAfter\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"sharesInEscrow\",\"type\":\"bool\"}],\"name\":\"updateAccountRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewards\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"rewardToken\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"emissionRatePerYear\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"endTime\",\"type\":\"uint32\"}],\"name\":\"updateRewardToken\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"getRewardDebt(address,address)\":{\"params\":{\"account\":\"Address of the account\",\"rewardToken\":\"Address of the reward token\"},\"returns\":{\"rewardDebt\":\"The reward debt for the account\"}},\"getRewardSettings()\":{\"returns\":{\"rewardPool\":\"Reward pool storage\",\"rewardStates\":\"Array of vault reward states\"}},\"migrateRewardPool(address,(address,uint32,uint32))\":{\"params\":{\"newRewardPool\":\"The new reward pool storage configuration\",\"poolToken\":\"The pool token to migrate\"}},\"updateAccountRewards(address,uint256,uint256,uint256,bool)\":{\"params\":{\"account\":\"Address of the account\",\"accountSharesAfter\":\"Number of shares after the operation\",\"accountSharesBefore\":\"Number of shares before the operation\",\"effectiveSupplyBefore\":\"Total vault shares before the operation\",\"sharesInEscrow\":\"Whether the shares are in escrow\"}},\"updateRewardToken(uint256,address,uint128,uint32)\":{\"params\":{\"emissionRatePerYear\":\"Emission rate per year for the token\",\"endTime\":\"End time for the emission rate\",\"index\":\"Index of the reward token\",\"rewardToken\":\"Address of the reward token\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"claimRewardTokens()\":{\"notice\":\"Claims all the rewards for the entire vault and updates the accumulators\"},\"getRewardDebt(address,address)\":{\"notice\":\"Returns the reward debt for the given reward token and account\"},\"getRewardSettings()\":{\"notice\":\"Returns the current reward claim method and reward state\"},\"migrateRewardPool(address,(address,uint32,uint32))\":{\"notice\":\"Migrates the reward pool to a new reward pool, needs to be called initially to set the reward pool storage and when the reward pool is updated.\"},\"updateAccountRewards(address,uint256,uint256,uint256,bool)\":{\"notice\":\"Updates account rewards during enter and exit vault operations, only callable via delegatecall from inside the vault\"},\"updateRewardToken(uint256,address,uint128,uint32)\":{\"notice\":\"Sets a secondary reward rate for a given token, only callable via the owner\"}},\"notice\":\"Each reward manager is responsible for claiming rewards for a given protocol. it will be called through a delegatecall from the vault to avoid token transfers of staked tokens.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IRewardManager.sol\":\"IRewardManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "address", "name": "account", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "VaultRewardTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address", "indexed": false}, {"internalType": "uint128", "name": "emissionRatePerYear", "type": "uint128", "indexed": false}, {"internalType": "uint32", "name": "endTime", "type": "uint32", "indexed": false}], "type": "event", "name": "VaultRewardUpdate", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "claimRewardTokens"}, {"inputs": [{"internalType": "address", "name": "rewardToken", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getRewardDebt", "outputs": [{"internalType": "uint256", "name": "rewardDebt", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getRewardSettings", "outputs": [{"internalType": "struct VaultRewardState[]", "name": "rewardStates", "type": "tuple[]", "components": [{"internalType": "address", "name": "rewardToken", "type": "address"}, {"internalType": "uint32", "name": "lastAccumulatedTime", "type": "uint32"}, {"internalType": "uint32", "name": "endTime", "type": "uint32"}, {"internalType": "uint128", "name": "emissionRatePerYear", "type": "uint128"}, {"internalType": "uint128", "name": "accumulatedRewardPerVaultShare", "type": "uint128"}]}, {"internalType": "struct RewardPoolStorage", "name": "rewardPool", "type": "tuple", "components": [{"internalType": "address", "name": "rewardPool", "type": "address"}, {"internalType": "uint32", "name": "lastClaimTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "forceClaimAfter", "type": "uint32"}]}]}, {"inputs": [{"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "struct RewardPoolStorage", "name": "newRewardPool", "type": "tuple", "components": [{"internalType": "address", "name": "rewardPool", "type": "address"}, {"internalType": "uint32", "name": "lastClaimTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "forceClaimAfter", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "migrateRewardPool"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "effectiveSupplyBefore", "type": "uint256"}, {"internalType": "uint256", "name": "accountSharesBefore", "type": "uint256"}, {"internalType": "uint256", "name": "accountSharesAfter", "type": "uint256"}, {"internalType": "bool", "name": "sharesInEscrow", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateAccountRewards", "outputs": [{"internalType": "uint256[]", "name": "rewards", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "rewardToken", "type": "address"}, {"internalType": "uint128", "name": "emissionRatePerYear", "type": "uint128"}, {"internalType": "uint32", "name": "endTime", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "updateRewardToken"}], "devdoc": {"kind": "dev", "methods": {"getRewardDebt(address,address)": {"params": {"account": "Address of the account", "rewardToken": "Address of the reward token"}, "returns": {"rewardDebt": "The reward debt for the account"}}, "getRewardSettings()": {"returns": {"rewardPool": "Reward pool storage", "rewardStates": "Array of vault reward states"}}, "migrateRewardPool(address,(address,uint32,uint32))": {"params": {"newRewardPool": "The new reward pool storage configuration", "poolToken": "The pool token to migrate"}}, "updateAccountRewards(address,uint256,uint256,uint256,bool)": {"params": {"account": "Address of the account", "accountSharesAfter": "Number of shares after the operation", "accountSharesBefore": "Number of shares before the operation", "effectiveSupplyBefore": "Total vault shares before the operation", "sharesInEscrow": "Whether the shares are in escrow"}}, "updateRewardToken(uint256,address,uint128,uint32)": {"params": {"emissionRatePerYear": "Emission rate per year for the token", "endTime": "End time for the emission rate", "index": "Index of the reward token", "rewardToken": "Address of the reward token"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"claimRewardTokens()": {"notice": "Claims all the rewards for the entire vault and updates the accumulators"}, "getRewardDebt(address,address)": {"notice": "Returns the reward debt for the given reward token and account"}, "getRewardSettings()": {"notice": "Returns the current reward claim method and reward state"}, "migrateRewardPool(address,(address,uint32,uint32))": {"notice": "Migrates the reward pool to a new reward pool, needs to be called initially to set the reward pool storage and when the reward pool is updated."}, "updateAccountRewards(address,uint256,uint256,uint256,bool)": {"notice": "Updates account rewards during enter and exit vault operations, only callable via delegatecall from inside the vault"}, "updateRewardToken(uint256,address,uint128,uint32)": {"notice": "Sets a secondary reward rate for a given token, only callable via the owner"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/IRewardManager.sol": "IRewardManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}}, "version": 1}, "id": 69}