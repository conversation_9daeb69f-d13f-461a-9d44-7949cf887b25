{"abi": [{"type": "function", "name": "balanceOfCollateral", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "collateralBalance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimRewards", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewards", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "depositAssetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}, {"name": "depositData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}, {"name": "assetToRepay", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "forceWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "healthFactor", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "borrowed", "type": "uint256", "internalType": "uint256"}, {"name": "collateralValue", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApproved", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "seizedAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repaidShares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "sharesToLiquidator", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "migratePosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "migrateFrom", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "set<PERSON><PERSON><PERSON>al", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"balanceOfCollateral(address,address)": "da3a855f", "claimRewards(address)": "ef5cfb8c", "enterPosition(address,address,uint256,uint256,bytes)": "de13c617", "exitPosition(address,address,address,uint256,uint256,bytes)": "ef9a0b61", "forceWithdraw(address,address,bytes)": "4a7f87e8", "healthFactor(address,address)": "576f5c40", "initiateWithdraw(address,address,bytes)": "c0b5de76", "isApproved(address,address)": "a389783e", "liquidate(address,address,uint256,uint256)": "c1342574", "migratePosition(address,address,address)": "c139cc98", "setApproval(address,bool)": "db9b7170"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"balanceOfCollateral\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"collateralBalance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"claimRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewards\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositAssetAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"depositData\",\"type\":\"bytes\"}],\"name\":\"enterPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToRedeem\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"assetToRepay\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"exitPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"forceWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"healthFactor\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"borrowed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"collateralValue\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxBorrow\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApproved\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"seizedAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repaidShares\",\"type\":\"uint256\"}],\"name\":\"liquidate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidator\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"migrateFrom\",\"type\":\"address\"}],\"name\":\"migratePosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApproval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"balanceOfCollateral(address,address)\":{\"details\":\"Returns the balance of collateral of a user for a given vault.\",\"params\":{\"account\":\"The address of the account.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"collateralBalance\":\"The balance of collateral.\"}},\"claimRewards(address)\":{\"details\":\"Claims rewards for a user for a given vault.\",\"params\":{\"vault\":\"The address of the vault.\"},\"returns\":{\"rewards\":\"The rewards.\"}},\"enterPosition(address,address,uint256,uint256,bytes)\":{\"details\":\"Enters a position in the lending market.\",\"params\":{\"borrowAmount\":\"The amount of assets to borrow.\",\"depositAssetAmount\":\"The amount of margin to deposit.\",\"depositData\":\"The data to pass to the deposit function.\",\"onBehalf\":\"The address of the user to enter the position on behalf of.\",\"vault\":\"The address of the vault.\"}},\"exitPosition(address,address,address,uint256,uint256,bytes)\":{\"details\":\"Exits a position in the lending market. Can be called by the user or another lending router to migrate a position.\",\"params\":{\"assetToRepay\":\"The amount of assets to repay, if set to uint256.max the full debt will be repaid.\",\"onBehalf\":\"The address of the user to exit the position on behalf of.\",\"receiver\":\"The address of the receiver.\",\"redeemData\":\"Vault specific instructions for the exit.\",\"sharesToRedeem\":\"The amount of shares to redeem.\",\"vault\":\"The address of the vault.\"}},\"forceWithdraw(address,address,bytes)\":{\"details\":\"Forces a withdraw for a user for a given vault, only allowed if the health factor is negative.\",\"params\":{\"account\":\"The address of the account.\",\"data\":\"Vault specific instructions for the withdraw.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"requestId\":\"The request id.\"}},\"healthFactor(address,address)\":{\"details\":\"Returns the health factor of a user for a given vault.\",\"params\":{\"borrower\":\"The address of the borrower.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"borrowed\":\"The borrowed amount.\",\"collateralValue\":\"The collateral value.\",\"maxBorrow\":\"The max borrow amount.\"}},\"initiateWithdraw(address,address,bytes)\":{\"details\":\"Initiates a withdraw request for a user for a given vault.\",\"params\":{\"data\":\"Vault specific instructions for the withdraw.\",\"onBehalf\":\"The address of the user to initiate the withdraw on behalf of.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"requestId\":\"The request id.\"}},\"isApproved(address,address)\":{\"details\":\"Returns the authorization status of an address.\",\"params\":{\"operator\":\"The address to check the authorization status of.\",\"user\":\"The address to check the authorization status of.\"},\"returns\":{\"_0\":\"The authorization status.\"}},\"liquidate(address,address,uint256,uint256)\":{\"details\":\"Liquidates a position in the lending market.\",\"params\":{\"liquidateAccount\":\"The address of the account to liquidate.\",\"repaidShares\":\"The amount of shares to repay.\",\"seizedAssets\":\"The amount of assets to seize.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"sharesToLiquidator\":\"The amount of shares to liquidator.\"}},\"migratePosition(address,address,address)\":{\"details\":\"Migrates a position to the lending market.\",\"params\":{\"migrateFrom\":\"The address of the lending router to migrate the position from.\",\"onBehalf\":\"The address of the user to migrate the position on behalf of.\",\"vault\":\"The address of the vault.\"}},\"setApproval(address,bool)\":{\"details\":\"Authorizes an address to manage a user's position.\",\"params\":{\"approved\":\"The authorization status.\",\"operator\":\"The address to authorize.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/ILendingRouter.sol\":\"ILendingRouter\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOfCollateral", "outputs": [{"internalType": "uint256", "name": "collateralBalance", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimRewards", "outputs": [{"internalType": "uint256[]", "name": "rewards", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "depositAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}, {"internalType": "bytes", "name": "depositData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "enterPosition"}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "sharesToRedeem", "type": "uint256"}, {"internalType": "uint256", "name": "assetToRepay", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "exitPosition"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "forceWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "healthFactor", "outputs": [{"internalType": "uint256", "name": "borrowed", "type": "uint256"}, {"internalType": "uint256", "name": "collateralValue", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "seizedAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repaidShares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate", "outputs": [{"internalType": "uint256", "name": "sharesToLiquidator", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "migrateFrom", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "migratePosition"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "set<PERSON><PERSON><PERSON>al"}], "devdoc": {"kind": "dev", "methods": {"balanceOfCollateral(address,address)": {"details": "Returns the balance of collateral of a user for a given vault.", "params": {"account": "The address of the account.", "vault": "The address of the vault."}, "returns": {"collateralBalance": "The balance of collateral."}}, "claimRewards(address)": {"details": "Claims rewards for a user for a given vault.", "params": {"vault": "The address of the vault."}, "returns": {"rewards": "The rewards."}}, "enterPosition(address,address,uint256,uint256,bytes)": {"details": "Enters a position in the lending market.", "params": {"borrowAmount": "The amount of assets to borrow.", "depositAssetAmount": "The amount of margin to deposit.", "depositData": "The data to pass to the deposit function.", "onBehalf": "The address of the user to enter the position on behalf of.", "vault": "The address of the vault."}}, "exitPosition(address,address,address,uint256,uint256,bytes)": {"details": "Exits a position in the lending market. Can be called by the user or another lending router to migrate a position.", "params": {"assetToRepay": "The amount of assets to repay, if set to uint256.max the full debt will be repaid.", "onBehalf": "The address of the user to exit the position on behalf of.", "receiver": "The address of the receiver.", "redeemData": "Vault specific instructions for the exit.", "sharesToRedeem": "The amount of shares to redeem.", "vault": "The address of the vault."}}, "forceWithdraw(address,address,bytes)": {"details": "Forces a withdraw for a user for a given vault, only allowed if the health factor is negative.", "params": {"account": "The address of the account.", "data": "<PERSON><PERSON> specific instructions for the withdraw.", "vault": "The address of the vault."}, "returns": {"requestId": "The request id."}}, "healthFactor(address,address)": {"details": "Returns the health factor of a user for a given vault.", "params": {"borrower": "The address of the borrower.", "vault": "The address of the vault."}, "returns": {"borrowed": "The borrowed amount.", "collateralValue": "The collateral value.", "maxBorrow": "The max borrow amount."}}, "initiateWithdraw(address,address,bytes)": {"details": "Initiates a withdraw request for a user for a given vault.", "params": {"data": "<PERSON><PERSON> specific instructions for the withdraw.", "onBehalf": "The address of the user to initiate the withdraw on behalf of.", "vault": "The address of the vault."}, "returns": {"requestId": "The request id."}}, "isApproved(address,address)": {"details": "Returns the authorization status of an address.", "params": {"operator": "The address to check the authorization status of.", "user": "The address to check the authorization status of."}, "returns": {"_0": "The authorization status."}}, "liquidate(address,address,uint256,uint256)": {"details": "Liquidates a position in the lending market.", "params": {"liquidateAccount": "The address of the account to liquidate.", "repaidShares": "The amount of shares to repay.", "seizedAssets": "The amount of assets to seize.", "vault": "The address of the vault."}, "returns": {"sharesToLiquidator": "The amount of shares to liquidator."}}, "migratePosition(address,address,address)": {"details": "Migrates a position to the lending market.", "params": {"migrateFrom": "The address of the lending router to migrate the position from.", "onBehalf": "The address of the user to migrate the position on behalf of.", "vault": "The address of the vault."}}, "setApproval(address,bool)": {"details": "Authorizes an address to manage a user's position.", "params": {"approved": "The authorization status.", "operator": "The address to authorize."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/ILendingRouter.sol": "ILendingRouter"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}}, "version": 1}, "id": 66}