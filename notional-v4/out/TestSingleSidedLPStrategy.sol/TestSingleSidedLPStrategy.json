{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "addressRegistry", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "view"}, {"type": "function", "name": "canInspectTransientVariables", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "defaultBorrow", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "defaultDeposit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "deployAddressRegistry", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "feeToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "view"}, {"type": "function", "name": "lendingRouter", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ILendingRouter"}], "stateMutability": "view"}, {"type": "function", "name": "manager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IWithdrawRequestManager"}], "stateMutability": "view"}, {"type": "function", "name": "maxEntryValuationSlippage", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxExitValuationSlippage", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxWithdrawValuationChange", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "o", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract MockOracle"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setup_migration_test", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "lendingRouter2", "type": "address", "internalType": "contract MorphoLendingRouter"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "strategyName", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "strategySymbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "test_RevertIf_callbacksCalledByNonMorpho", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_RevertsIf_MorphoWithdrawCollateral", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_cannotEnterAboveMaxPoolShare", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_claimRewards", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_collectFees", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition_RevertsIf_ExistingWithdrawRequest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition_RevertsIf_InvalidVault", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition_after_Exit_WithdrawRequest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition_stakeBeforeDeposit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition_tradeBeforeDeposit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_enterPosition_zeroBorrow", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_exitPosition_fullExit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_exitPosition_partialExit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_exitPosition_revertsIf_BeforeCooldownPeriod", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_exitPosition_tradeBeforeRedeem", "inputs": [{"name": "isFullExit", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_exitPosition_withdrawBeforeRedeem", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate_RevertsIf_AccountHasNoShares", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate_RevertsIf_CalledOnMorpho", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate_RevertsIf_InsufficientAssetsForRepayment", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate_RevertsIf_LiquidatorHasCollateralBalance", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate_RevertsIf_SharesToLiquidateIsZero", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_liquidate_tokenizesWithdrawRequest", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_migrate", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_migrate_RevertsIf_EnteringAnotherLendingRouter", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_migrate_RevertsIf_NotAuthorized", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_migrate_RevertsIf_NotCalledByLendingRouter", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_migrate_RevertsIf_PriorToCooldownPeriod", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_multiple_entries_exits", "inputs": [{"name": "userActions", "type": "uint256[10]", "internalType": "uint256[10]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_nativeBalance_RevertsIf_EnterPosition_or_Migrate", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_redeemNative_RevertsIf_NoSharesHeld", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_setApproval", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_setApproval_self", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_share_valuation", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "test_withdrawRequestValuation", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "w", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ERC20"}], "stateMutability": "view"}, {"type": "function", "name": "withdrawRequest", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract TestWithdrawRequest"}], "stateMutability": "view"}, {"type": "function", "name": "y", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IYieldStrategy"}], "stateMutability": "view"}, {"type": "event", "name": "SlotFound", "inputs": [{"name": "who", "type": "address", "indexed": false, "internalType": "address"}, {"name": "fsig", "type": "bytes4", "indexed": false, "internalType": "bytes4"}, {"name": "keysHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "slot", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TradeExecuted", "inputs": [{"name": "sellToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sellAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "buyAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WARNING_UninitedSlot", "inputs": [{"name": "who", "type": "address", "indexed": false, "internalType": "address"}, {"name": "slot", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "addressRegistry()": "f3ad65f4", "asset()": "38d52e0f", "canInspectTransientVariables()": "8b5a0c1e", "defaultBorrow()": "bed89eab", "defaultDeposit()": "ec738771", "deployAddressRegistry()": "0f5e5f34", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "feeToken()": "647846a5", "lendingRouter()": "3f564c67", "manager()": "481c6a75", "maxEntryValuationSlippage()": "1eea9389", "maxExitValuationSlippage()": "6f019ac9", "maxWithdrawValuationChange()": "df020663", "o()": "50cd4df2", "owner()": "8da5cb5b", "setUp()": "0a9254e4", "setup_migration_test(address)": "278915fd", "strategyName()": "e173ad25", "strategySymbol()": "ffacae88", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "test_RevertIf_callbacksCalledByNonMorpho()": "ca099e71", "test_RevertsIf_MorphoWithdrawCollateral()": "b48d21c8", "test_cannotEnterAboveMaxPoolShare()": "9cc31a33", "test_claimRewards()": "2518d2fd", "test_collectFees()": "610cb84e", "test_enterPosition()": "53b94532", "test_enterPosition_RevertsIf_ExistingWithdrawRequest()": "a75e4776", "test_enterPosition_RevertsIf_InvalidVault()": "529a4ce3", "test_enterPosition_after_Exit_WithdrawRequest()": "64c79a3f", "test_enterPosition_stakeBeforeDeposit()": "5b0bb975", "test_enterPosition_tradeBeforeDeposit()": "3711a8d0", "test_enterPosition_zeroBorrow()": "e6f707b0", "test_exitPosition_fullExit()": "717813a9", "test_exitPosition_partialExit()": "9459c6ba", "test_exitPosition_revertsIf_BeforeCooldownPeriod()": "82d09946", "test_exitPosition_tradeBeforeRedeem(bool)": "36e89325", "test_exitPosition_withdrawBeforeRedeem()": "a8cc54a9", "test_liquidate()": "bebd4fb7", "test_liquidate_RevertsIf_AccountHasNoShares()": "6645c7fa", "test_liquidate_RevertsIf_CalledOnMorpho()": "4ddb793c", "test_liquidate_RevertsIf_InsufficientAssetsForRepayment()": "5a79227b", "test_liquidate_RevertsIf_LiquidatorHasCollateralBalance()": "ce497941", "test_liquidate_RevertsIf_SharesToLiquidateIsZero()": "548dfb01", "test_liquidate_tokenizesWithdrawRequest()": "34af3d83", "test_migrate()": "282e5729", "test_migrate_RevertsIf_EnteringAnotherLendingRouter()": "c4987b02", "test_migrate_RevertsIf_NotAuthorized()": "cdd707e2", "test_migrate_RevertsIf_NotCalledByLendingRouter()": "a6dcee04", "test_migrate_RevertsIf_PriorToCooldownPeriod()": "68ec5d3f", "test_multiple_entries_exits(uint256[10])": "e1e65bfc", "test_nativeBalance_RevertsIf_EnterPosition_or_Migrate()": "4be3c842", "test_redeemNative_RevertsIf_NoSharesHeld()": "ee019088", "test_setApproval()": "caf2cd0e", "test_setApproval_self()": "310fd13d", "test_share_valuation()": "84776f3d", "test_withdrawRequestValuation()": "684b768d", "w()": "205c9cc7", "withdrawRequest()": "380e687a", "y()": "a56dfe4a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes4\",\"name\":\"fsig\",\"type\":\"bytes4\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"keysHash\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"slot\",\"type\":\"uint256\"}],\"name\":\"SlotFound\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"sellAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"buyAmount\",\"type\":\"uint256\"}],\"name\":\"TradeExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"slot\",\"type\":\"uint256\"}],\"name\":\"WARNING_UninitedSlot\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"addressRegistry\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"asset\",\"outputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"canInspectTransientVariables\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"defaultBorrow\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"defaultDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deployAddressRegistry\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeToken\",\"outputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lendingRouter\",\"outputs\":[{\"internalType\":\"contract ILendingRouter\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"manager\",\"outputs\":[{\"internalType\":\"contract IWithdrawRequestManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxEntryValuationSlippage\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxExitValuationSlippage\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxWithdrawValuationChange\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"o\",\"outputs\":[{\"internalType\":\"contract MockOracle\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"setup_migration_test\",\"outputs\":[{\"internalType\":\"contract MorphoLendingRouter\",\"name\":\"lendingRouter2\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"strategyName\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"strategySymbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_RevertIf_callbacksCalledByNonMorpho\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_RevertsIf_MorphoWithdrawCollateral\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_cannotEnterAboveMaxPoolShare\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_claimRewards\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_collectFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition_RevertsIf_ExistingWithdrawRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition_RevertsIf_InvalidVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition_after_Exit_WithdrawRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition_stakeBeforeDeposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition_tradeBeforeDeposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_enterPosition_zeroBorrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_exitPosition_fullExit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_exitPosition_partialExit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_exitPosition_revertsIf_BeforeCooldownPeriod\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"isFullExit\",\"type\":\"bool\"}],\"name\":\"test_exitPosition_tradeBeforeRedeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_exitPosition_withdrawBeforeRedeem\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate_RevertsIf_AccountHasNoShares\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate_RevertsIf_CalledOnMorpho\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate_RevertsIf_InsufficientAssetsForRepayment\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate_RevertsIf_LiquidatorHasCollateralBalance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate_RevertsIf_SharesToLiquidateIsZero\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_liquidate_tokenizesWithdrawRequest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_migrate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_migrate_RevertsIf_EnteringAnotherLendingRouter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_migrate_RevertsIf_NotAuthorized\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_migrate_RevertsIf_NotCalledByLendingRouter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_migrate_RevertsIf_PriorToCooldownPeriod\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[10]\",\"name\":\"userActions\",\"type\":\"uint256[10]\"}],\"name\":\"test_multiple_entries_exits\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_nativeBalance_RevertsIf_EnterPosition_or_Migrate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_redeemNative_RevertsIf_NoSharesHeld\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_setApproval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_setApproval_self\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_share_valuation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test_withdrawRequestValuation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"w\",\"outputs\":[{\"internalType\":\"contract ERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawRequest\",\"outputs\":[{\"internalType\":\"contract TestWithdrawRequest\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"y\",\"outputs\":[{\"internalType\":\"contract IYieldStrategy\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"tests/TestSingleSidedLPStrategy.sol\":\"TestSingleSidedLPStrategy\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c\",\"dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a\",\"dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ\"]},\"node_modules/@openzeppelin/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d\",\"dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e\",\"dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol\":{\"keccak256\":\"0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244\",\"dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh\"]},\"node_modules/@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f\",\"dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"node_modules/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"node_modules/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"node_modules/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"node_modules/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"node_modules/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"node_modules/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"node_modules/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"node_modules/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"node_modules/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"node_modules/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"node_modules/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"node_modules/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"node_modules/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"node_modules/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"node_modules/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"node_modules/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"node_modules/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"node_modules/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Curve/IConvex.sol\":{\"keccak256\":\"0x72201de544f9d37f7993fd296507092e1ce217a95367b55232264c7bb49b2127\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://519191f39f9f3c319a38fd898eba05a1d8a7c9fac580fb5d2c6da26cbae7dab6\",\"dweb:/ipfs/QmZdqAcDQjeWZtezKm1HaS1NefEcnY9566u7VPpZh2k7nb\"]},\"src/interfaces/Curve/ICurve.sol\":{\"keccak256\":\"0xc9c5ab52d4e6c8a541177552533a0629cec42a5eaf37edba117dc7cbb064f826\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://75ef6d199300bb6b1751462b9b5921a4f6a4d9a5cbd993efde47f0e92b32699c\",\"dweb:/ipfs/Qmcr5yQoR3vwfoupjoqKK8C55YmX2nFV8ee6XUuuHtPt65\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/IEIP20NonStandard.sol\":{\"keccak256\":\"0xcd73239cf684d4ee718e2c6cf9a380e298d21948cd1eaa1843e16b2ec1438e5a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4779a6b1fb6c29de8bc5f8139a2693b4ea93ad32698120f665cbd06094c36db9\",\"dweb:/ipfs/QmdVndD4AgxaS4Uivyt5GEuC4Bn51shw5EmFYnvHRzizq4\"]},\"src/interfaces/IEtherFi.sol\":{\"keccak256\":\"0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635\",\"dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ISingleSidedLP.sol\":{\"keccak256\":\"0xf90948287aaefb48273728c5065f5573ad36008bf828e0f3c2332a780bd110ff\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2a2769919b026afb6c66488d0520afb2ec639101b4981a1c279b9c95139157ab\",\"dweb:/ipfs/QmcWezqKeQe9cEqdZ3WLXG45xNDmsFLoWwQ7dn9jxbJrdf\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/oracles/AbstractCustomOracle.sol\":{\"keccak256\":\"0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524\",\"license\":\"BSUL-1.1\",\"urls\":[\"bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd\",\"dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW\"]},\"src/oracles/AbstractLPOracle.sol\":{\"keccak256\":\"0xd9c5f8c7b85bff5e55a9ce405de28feb689fcfeab349bd92c355187a19938fd5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a919097aa77c032aeb68f33ed5e445f0967bd15e98faee3a7512e3daf9523024\",\"dweb:/ipfs/QmSXjyYccWZNYYb68B12aAQjKCpgFu8Xdpeuw8AP2kFSBc\"]},\"src/oracles/Curve2TokenOracle.sol\":{\"keccak256\":\"0xc3ff53c1c05056f497f2e22540a20d699cdc353d0208df53d95ecd87f7a6b3f7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://64127adbac2e571689182c034acf8c0bddd244f2459a956a12cd46c090d5088d\",\"dweb:/ipfs/QmPXF3GaGUjoX2cb278mzLk3ZWM66W8xL5Y8uPLoqvkJrU\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/proxy/TimelockUpgradeableProxy.sol\":{\"keccak256\":\"0x4854e20c5c6598955ce141e2eb8af08caa627edda51d721b2104138eb6cd594f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1b52d8cd72d9c3cc8d2bd0d7282c54205127ccc970e8dc0956c14339bcb87994\",\"dweb:/ipfs/QmS1wC538xpZgngfXcdk5jZz48H4CwWMvJqt75GktDxKUs\"]},\"src/rewards/AbstractRewardManager.sol\":{\"keccak256\":\"0xb17a03a9005fd5ddf37b885ebf9e871e38f6b5651295350ff331e0a61022d258\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://9e98988d52eed68483f1de11bb7b1bc65bac5a7ec351aa1a093587633bf4f0b6\",\"dweb:/ipfs/QmV8fSZgvLbu6tAr9Mh6gMipULb6Gd2pW7E9hZe19X8pH1\"]},\"src/rewards/ConvexRewardManager.sol\":{\"keccak256\":\"0xb24143b2249890aefc6eb112a0f1132aad11a6ba64777945a042fa4321aec7cb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d499699d8108107aff16f94929d7139c1d73b4893519b845ea8f644fa17c188d\",\"dweb:/ipfs/QmYWCCe1QYaNi6tTEspeD2zvRpdJR9jeAWuBqSgbVbiNkN\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/routers/AbstractLendingRouter.sol\":{\"keccak256\":\"0x76c82e77d6495512ba1a397c63c10bed272b0b7e4c1427b382e0f1e03caac6e0\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b2a5aef92cf9f8b4366747cd0ebb8c0e99439f1e9f0f3de466e5e2aad7161788\",\"dweb:/ipfs/QmR7tnF6L6YwB6UnU61NX3N7NZTY3GuKFyfGfUnGphjJ23\"]},\"src/routers/MorphoLendingRouter.sol\":{\"keccak256\":\"0xbbc17116830e10ccefbb483822f1213f6a5a4974fe5db1462affd12a9798de17\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b33ba907950b9bd634742029b62923de3ee765b9f2a2bfc8b575773152351828\",\"dweb:/ipfs/QmY9fAU3rF7gEnJ1ABGV6L4smbaRBNyL7o5KFHX1ozAZYM\"]},\"src/single-sided-lp/AbstractSingleSidedLP.sol\":{\"keccak256\":\"0x4a9225e291fe8de0ceb1b763a5e1a40ef1e54f83dcecf71e975dee61e0968748\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2d0140129a34f13c29f511b51edafc450af55d6545b0c9ab27678128f16d3c6e\",\"dweb:/ipfs/QmP9Czzzr2bWo7d4WiocrsPNmisaouot5ve81w4y6ZNDss\"]},\"src/single-sided-lp/CurveConvex2Token.sol\":{\"keccak256\":\"0x309493cf701f7ea11e23c1094e4674cf857f09bc88ec0d895ea8ee57f7fd1520\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d329481e61b87d817bfa76856812a7fe9c72e93208e4cc02cf4c6d12aeafadab\",\"dweb:/ipfs/QmTtFf9nTAWREnCXKNurNtETsMaWu3LMgA7sMBBvhXKtQq\"]},\"src/staking/AbstractStakingStrategy.sol\":{\"keccak256\":\"0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f\",\"dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg\"]},\"src/staking/StakingStrategy.sol\":{\"keccak256\":\"0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2\",\"dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]},\"src/utils/TypeConvert.sol\":{\"keccak256\":\"0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686\",\"dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB\"]},\"src/withdraws/AbstractWithdrawRequestManager.sol\":{\"keccak256\":\"0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb\",\"dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu\"]},\"src/withdraws/ClonedCooldownHolder.sol\":{\"keccak256\":\"0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b\",\"dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM\"]},\"src/withdraws/EtherFi.sol\":{\"keccak256\":\"0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2\",\"dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L\"]},\"tests/Mocks.sol\":{\"keccak256\":\"0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876\",\"dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc\"]},\"tests/TestEnvironment.sol\":{\"keccak256\":\"0xdc00d25077999c75f1fa061e9c872570cc8475e8d9dce04f1156f194f95414ac\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://8c633bdda8cc2377f8f8192ea97cb4a4dacd0f476dcfa56bfd758e2a590beb4f\",\"dweb:/ipfs/QmfPSJp24cf1o48gfg7pqJbXZZs3qXYCkDGTTmi6KWxB6Y\"]},\"tests/TestMorphoYieldStrategy.sol\":{\"keccak256\":\"0x6aa8aa1583984ffddb9836172be3ca3db9c53bfab998a4f8e10a385395ca0f48\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://2ea37030ee5c510c669fc50d514580916a228a803b4960fb336139943d739fe2\",\"dweb:/ipfs/QmNWQ6A54J8EZNSLrs4uztmD6SWrzZYVsEfSX3tA7ZRFWx\"]},\"tests/TestSingleSidedLPStrategy.sol\":{\"keccak256\":\"0x78cadac5f28c648692c04c847be7d3a43c42db13ff7cb412c5bd286cc2c23bac\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://35263a51d1efd21ca4b32cdcb3e3d53b1bda576024787f404d3790ee0f926e28\",\"dweb:/ipfs/QmcpMDoWq3TaCE14LQhbeUu1nsx1hwPcxvPCD8PRuzNLEF\"]},\"tests/TestWithdrawRequest.sol\":{\"keccak256\":\"0x58c45acffa7201d5c03892bda880dbf539dd8daa53c39d4494b7261cbc606cb9\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://cba6894deb85bb8c4058ea41ea6a22f59576a82f0d55a8031960d4f779e12669\",\"dweb:/ipfs/QmbAfT3aXJkw5VfkRYQZMJuMeMNw8kCAECGmjkGHnwr2XJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "who", "type": "address", "indexed": false}, {"internalType": "bytes4", "name": "fsig", "type": "bytes4", "indexed": false}, {"internalType": "bytes32", "name": "keysHash", "type": "bytes32", "indexed": false}, {"internalType": "uint256", "name": "slot", "type": "uint256", "indexed": false}], "type": "event", "name": "SlotFound", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sellToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "buyToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "sellAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "buyAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "TradeExecuted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "who", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "slot", "type": "uint256", "indexed": false}], "type": "event", "name": "WARNING_UninitedSlot", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "addressRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "asset", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "canInspectTransientVariables", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "defaultBorrow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "defaultDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "deployAddressRegistry"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lendingRouter", "outputs": [{"internalType": "contract ILendingRouter", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "manager", "outputs": [{"internalType": "contract IWithdrawRequestManager", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "maxEntryValuationSlippage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "maxExitValuationSlippage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "maxWithdrawValuationChange", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "o", "outputs": [{"internalType": "contract MockOracle", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setup_migration_test", "outputs": [{"internalType": "contract MorphoLendingRouter", "name": "lendingRouter2", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "strategyName", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "strategySymbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_RevertIf_callbacksCalledByNonMorpho"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_RevertsIf_MorphoWithdrawCollateral"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_cannotEnterAboveMaxPoolShare"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_claimRewards"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_collectFees"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition_RevertsIf_ExistingWithdrawRequest"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition_RevertsIf_InvalidVault"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition_after_Exit_WithdrawRequest"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition_stakeBeforeDeposit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition_tradeBeforeDeposit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_enterPosition_zeroBorrow"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_exitPosition_fullExit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_exitPosition_partialExit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_exitPosition_revertsIf_BeforeCooldownPeriod"}, {"inputs": [{"internalType": "bool", "name": "isFullExit", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "test_exitPosition_tradeBeforeRedeem"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_exitPosition_withdrawBeforeRedeem"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate_RevertsIf_AccountHasNoShares"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate_RevertsIf_CalledOnMorpho"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate_RevertsIf_InsufficientAssetsForRepayment"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate_RevertsIf_LiquidatorHasCollateralBalance"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate_RevertsIf_SharesToLiquidateIsZero"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_liquidate_tokenizesWithdrawRequest"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_migrate"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_migrate_RevertsIf_EnteringAnotherLendingRouter"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_migrate_RevertsIf_NotAuthorized"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_migrate_RevertsIf_NotCalledByLendingRouter"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_migrate_RevertsIf_PriorToCooldownPeriod"}, {"inputs": [{"internalType": "uint256[10]", "name": "userActions", "type": "uint256[10]"}], "stateMutability": "nonpayable", "type": "function", "name": "test_multiple_entries_exits"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_nativeBalance_RevertsIf_EnterPosition_or_Migrate"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_redeemNative_RevertsIf_NoSharesHeld"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_setApproval"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_setApproval_self"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_share_valuation"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test_withdrawRequestValuation"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "w", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "withdrawRequest", "outputs": [{"internalType": "contract TestWithdrawRequest", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "y", "outputs": [{"internalType": "contract IYieldStrategy", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"tests/TestSingleSidedLPStrategy.sol": "TestSingleSidedLPStrategy"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC1967.sol": {"keccak256": "0xbf2aefe54b76d7f7bcd4f6da1080b7b1662611937d870b880db584d09cea56b5", "urls": ["bzz-raw://f5e7e2f12e0feec75296e57f51f82fdaa8bd1551f4b8cc6560442c0bf60f818c", "dweb:/ipfs/QmcW9wDMaQ8RbQibMarfp17a3bABzY5KraWe2YDwuUrUoz"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0xa1ad192cd45317c788618bef5cb1fb3ca4ce8b230f6433ac68cc1d850fb81618", "urls": ["bzz-raw://b43447bb85a53679d269a403c693b9d88d6c74177dfb35eddca63abaf7cf110a", "dweb:/ipfs/QmXSDmpd4bNZj1PDgegr6C4w1jDaWHXCconC3rYiw9TSkQ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0x20462ddb2665e9521372c76b001d0ce196e59dbbd989de9af5576cad0bd5628b", "urls": ["bzz-raw://f417fd12aeec8fbfaceaa30e3a08a0724c0bc39de363e2acf6773c897abbaf6d", "dweb:/ipfs/QmU4Hko6sApdweVM92CsiuLKkCk8HfyBeutF89PCTz5Tye"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055", "urls": ["bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e", "dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol": {"keccak256": "0xaad20f8713b5cd98114278482d5d91b9758f9727048527d582e8e88fd4901fd8", "urls": ["bzz-raw://5396e8dbb000c2fada59b7d2093b9c7c870fd09413ab0fdaba45d882959c6244", "dweb:/ipfs/QmXQn5XckSiUsUBpMYuiFeqnojRX4rKa9jmgjCPeTuPmhh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Address.sol": {"keccak256": "0x6d0ae6e206645341fd122d278c2cb643dea260c190531f2f3f6a0426e77b00c0", "urls": ["bzz-raw://032d1201d839435be2c85b72e33206b3ea980c569d6ebf7fa57d811ab580a82f", "dweb:/ipfs/QmeqQjAtMvdZT2tG7zm39itcRJkuwu8AEReK6WRnLJ18DD"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "node_modules/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "node_modules/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "node_modules/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "node_modules/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "node_modules/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "node_modules/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "node_modules/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "node_modules/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "node_modules/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "node_modules/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "node_modules/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "node_modules/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "node_modules/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "node_modules/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "node_modules/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "node_modules/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "node_modules/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "node_modules/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Curve/IConvex.sol": {"keccak256": "0x72201de544f9d37f7993fd296507092e1ce217a95367b55232264c7bb49b2127", "urls": ["bzz-raw://519191f39f9f3c319a38fd898eba05a1d8a7c9fac580fb5d2c6da26cbae7dab6", "dweb:/ipfs/QmZdqAcDQjeWZtezKm1HaS1NefEcnY9566u7VPpZh2k7nb"], "license": "MIT"}, "src/interfaces/Curve/ICurve.sol": {"keccak256": "0xc9c5ab52d4e6c8a541177552533a0629cec42a5eaf37edba117dc7cbb064f826", "urls": ["bzz-raw://75ef6d199300bb6b1751462b9b5921a4f6a4d9a5cbd993efde47f0e92b32699c", "dweb:/ipfs/Qmcr5yQoR3vwfoupjoqKK8C55YmX2nFV8ee6XUuuHtPt65"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/IEIP20NonStandard.sol": {"keccak256": "0xcd73239cf684d4ee718e2c6cf9a380e298d21948cd1eaa1843e16b2ec1438e5a", "urls": ["bzz-raw://4779a6b1fb6c29de8bc5f8139a2693b4ea93ad32698120f665cbd06094c36db9", "dweb:/ipfs/QmdVndD4AgxaS4Uivyt5GEuC4Bn51shw5EmFYnvHRzizq4"], "license": "GPL-3.0-only"}, "src/interfaces/IEtherFi.sol": {"keccak256": "0xf942f28a3afe3b27b3f88cb5e61e8ec89f9ced124a092b8a92c556ce393c87cc", "urls": ["bzz-raw://d3a768947ab4897dc5e1aa020e048dafa038c4dce7bdc2321ef7997b5a9d9635", "dweb:/ipfs/QmXBRptLhbq4kKwnyCa8UmXMsq9fyg7BEXDAuWX41YKNwg"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ISingleSidedLP.sol": {"keccak256": "0xf90948287aaefb48273728c5065f5573ad36008bf828e0f3c2332a780bd110ff", "urls": ["bzz-raw://2a2769919b026afb6c66488d0520afb2ec639101b4981a1c279b9c95139157ab", "dweb:/ipfs/QmcWezqKeQe9cEqdZ3WLXG45xNDmsFLoWwQ7dn9jxbJrdf"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/oracles/AbstractCustomOracle.sol": {"keccak256": "0x372010ab9b2f888880687ac56d8a48bd7bdc66e0b8b463a6889439df3cc50524", "urls": ["bzz-raw://43b286ab67f530f0673f10f8346d669606ad8f320d21324bda025b563c8a3ccd", "dweb:/ipfs/QmWKYgq7jzXjzE5mTTQyS3GwLwwegzvKtwgKcuHVZUTfTW"], "license": "BSUL-1.1"}, "src/oracles/AbstractLPOracle.sol": {"keccak256": "0xd9c5f8c7b85bff5e55a9ce405de28feb689fcfeab349bd92c355187a19938fd5", "urls": ["bzz-raw://a919097aa77c032aeb68f33ed5e445f0967bd15e98faee3a7512e3daf9523024", "dweb:/ipfs/QmSXjyYccWZNYYb68B12aAQjKCpgFu8Xdpeuw8AP2kFSBc"], "license": "BUSL-1.1"}, "src/oracles/Curve2TokenOracle.sol": {"keccak256": "0xc3ff53c1c05056f497f2e22540a20d699cdc353d0208df53d95ecd87f7a6b3f7", "urls": ["bzz-raw://64127adbac2e571689182c034acf8c0bddd244f2459a956a12cd46c090d5088d", "dweb:/ipfs/QmPXF3GaGUjoX2cb278mzLk3ZWM66W8xL5Y8uPLoqvkJrU"], "license": "BUSL-1.1"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/proxy/TimelockUpgradeableProxy.sol": {"keccak256": "0x4854e20c5c6598955ce141e2eb8af08caa627edda51d721b2104138eb6cd594f", "urls": ["bzz-raw://1b52d8cd72d9c3cc8d2bd0d7282c54205127ccc970e8dc0956c14339bcb87994", "dweb:/ipfs/QmS1wC538xpZgngfXcdk5jZz48H4CwWMvJqt75GktDxKUs"], "license": "BUSL-1.1"}, "src/rewards/AbstractRewardManager.sol": {"keccak256": "0xb17a03a9005fd5ddf37b885ebf9e871e38f6b5651295350ff331e0a61022d258", "urls": ["bzz-raw://9e98988d52eed68483f1de11bb7b1bc65bac5a7ec351aa1a093587633bf4f0b6", "dweb:/ipfs/QmV8fSZgvLbu6tAr9Mh6gMipULb6Gd2pW7E9hZe19X8pH1"], "license": "BUSL-1.1"}, "src/rewards/ConvexRewardManager.sol": {"keccak256": "0xb24143b2249890aefc6eb112a0f1132aad11a6ba64777945a042fa4321aec7cb", "urls": ["bzz-raw://d499699d8108107aff16f94929d7139c1d73b4893519b845ea8f644fa17c188d", "dweb:/ipfs/QmYWCCe1QYaNi6tTEspeD2zvRpdJR9jeAWuBqSgbVbiNkN"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/routers/AbstractLendingRouter.sol": {"keccak256": "0x76c82e77d6495512ba1a397c63c10bed272b0b7e4c1427b382e0f1e03caac6e0", "urls": ["bzz-raw://b2a5aef92cf9f8b4366747cd0ebb8c0e99439f1e9f0f3de466e5e2aad7161788", "dweb:/ipfs/QmR7tnF6L6YwB6UnU61NX3N7NZTY3GuKFyfGfUnGphjJ23"], "license": "BUSL-1.1"}, "src/routers/MorphoLendingRouter.sol": {"keccak256": "0xbbc17116830e10ccefbb483822f1213f6a5a4974fe5db1462affd12a9798de17", "urls": ["bzz-raw://b33ba907950b9bd634742029b62923de3ee765b9f2a2bfc8b575773152351828", "dweb:/ipfs/QmY9fAU3rF7gEnJ1ABGV6L4smbaRBNyL7o5KFHX1ozAZYM"], "license": "BUSL-1.1"}, "src/single-sided-lp/AbstractSingleSidedLP.sol": {"keccak256": "0x4a9225e291fe8de0ceb1b763a5e1a40ef1e54f83dcecf71e975dee61e0968748", "urls": ["bzz-raw://2d0140129a34f13c29f511b51edafc450af55d6545b0c9ab27678128f16d3c6e", "dweb:/ipfs/QmP9Czzzr2bWo7d4WiocrsPNmisaouot5ve81w4y6ZNDss"], "license": "BUSL-1.1"}, "src/single-sided-lp/CurveConvex2Token.sol": {"keccak256": "0x309493cf701f7ea11e23c1094e4674cf857f09bc88ec0d895ea8ee57f7fd1520", "urls": ["bzz-raw://d329481e61b87d817bfa76856812a7fe9c72e93208e4cc02cf4c6d12aeafadab", "dweb:/ipfs/QmTtFf9nTAWREnCXKNurNtETsMaWu3LMgA7sMBBvhXKtQq"], "license": "BUSL-1.1"}, "src/staking/AbstractStakingStrategy.sol": {"keccak256": "0x5d3f09c1c87b82f8f957bfe3a021d8d88fc46968d168bf9eed348d8376355c31", "urls": ["bzz-raw://cb4926c6dabaf94ad2a4bee35dbb6ed6cf8d585e69e22db255388511183e221f", "dweb:/ipfs/QmU8cevoKumCicq3NRCsTR54yaGHvKNBfWr3U52jKPF6Eg"], "license": "BUSL-1.1"}, "src/staking/StakingStrategy.sol": {"keccak256": "0xa7485229c1a9ed15f54b5076a82be164e3b30da4bfd1b003998fe842817ccbd9", "urls": ["bzz-raw://e73d5fadf2eefc6d8d549432dfe9f14dbd094ff4999a95310bc2b944b717aae2", "dweb:/ipfs/QmczEuKgyvRPowHLMKVpMKv79wDBC9Y2zuNo4Jn2SjC2kQ"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}, "src/utils/TypeConvert.sol": {"keccak256": "0x64294c317af447ec7d655dc63a6190f7a6f84efcf5b33cc6137142b3aaa0aaa5", "urls": ["bzz-raw://bb94e6ad81d47016060d0cee5ca1f015b39d15c1186e34241f815e83623f3686", "dweb:/ipfs/QmeVeBH1pmBS12iHPfQEFRZBN7xajpwGKNuGMgKGMiFjpB"], "license": "BUSL-1.1"}, "src/withdraws/AbstractWithdrawRequestManager.sol": {"keccak256": "0x7669be55f7a0dae08562e3d98016c39153ddcc1babc90878290a05e0168995fd", "urls": ["bzz-raw://7ecfc52d7b345db8fd8d2918028e77b48b93ded54f8181eb57ccc41c8550b7bb", "dweb:/ipfs/Qmca4mg9kjo1UXSyBWJW3jU53oVgFaJok6tB17fbJxMNQu"], "license": "BUSL-1.1"}, "src/withdraws/ClonedCooldownHolder.sol": {"keccak256": "0x57910c69de6d06a3596abb17bd53578554ea5757a0762cef5356f1cb6744fc6f", "urls": ["bzz-raw://b69defaa605e7b1a90b64bece2e64248cf07ad29a693893fe02558dcf6b77b9b", "dweb:/ipfs/Qmeu8H52tVyUuBn4aRn1UmTCQiH6fAuhJG5ocnEtn8RGGM"], "license": "BUSL-1.1"}, "src/withdraws/EtherFi.sol": {"keccak256": "0xc174f8de439d961c9536ad7c203b61e98e441eec116b6ac099c8405c782bb262", "urls": ["bzz-raw://cf0c0d94140067cc7fa6332d5c9a67b81964476245e558eea5dbcaaaa10ba4d2", "dweb:/ipfs/QmVbXVH4HQMkm36t2Y18G6QKmJDiSQvsP2NNP7V9Nkgp5L"], "license": "BUSL-1.1"}, "tests/Mocks.sol": {"keccak256": "0xce62165750326937b389f6a9c1116711383459782c7ec3bcc7e694ffb60d7d5a", "urls": ["bzz-raw://baf7f90565436945494b2f74c8bbbbe9dfa3ca79ad6acf6f46a286c0f8257876", "dweb:/ipfs/QmeFrQ3mTJ17bHbHQMPLpenv7DQR4jJTAQpHNiZF6mYeQc"], "license": "UNLICENSED"}, "tests/TestEnvironment.sol": {"keccak256": "0xdc00d25077999c75f1fa061e9c872570cc8475e8d9dce04f1156f194f95414ac", "urls": ["bzz-raw://8c633bdda8cc2377f8f8192ea97cb4a4dacd0f476dcfa56bfd758e2a590beb4f", "dweb:/ipfs/QmfPSJp24cf1o48gfg7pqJbXZZs3qXYCkDGTTmi6KWxB6Y"], "license": "UNLICENSED"}, "tests/TestMorphoYieldStrategy.sol": {"keccak256": "0x6aa8aa1583984ffddb9836172be3ca3db9c53bfab998a4f8e10a385395ca0f48", "urls": ["bzz-raw://2ea37030ee5c510c669fc50d514580916a228a803b4960fb336139943d739fe2", "dweb:/ipfs/QmNWQ6A54J8EZNSLrs4uztmD6SWrzZYVsEfSX3tA7ZRFWx"], "license": "UNLICENSED"}, "tests/TestSingleSidedLPStrategy.sol": {"keccak256": "0x78cadac5f28c648692c04c847be7d3a43c42db13ff7cb412c5bd286cc2c23bac", "urls": ["bzz-raw://35263a51d1efd21ca4b32cdcb3e3d53b1bda576024787f404d3790ee0f926e28", "dweb:/ipfs/QmcpMDoWq3TaCE14LQhbeUu1nsx1hwPcxvPCD8PRuzNLEF"], "license": "UNLICENSED"}, "tests/TestWithdrawRequest.sol": {"keccak256": "0x58c45acffa7201d5c03892bda880dbf539dd8daa53c39d4494b7261cbc606cb9", "urls": ["bzz-raw://cba6894deb85bb8c4058ea41ea6a22f59576a82f0d55a8031960d4f779e12669", "dweb:/ipfs/QmbAfT3aXJkw5VfkRYQZMJuMeMNw8kCAECGmjkGHnwr2XJ"], "license": "UNLICENSED"}}, "version": 1}, "id": 114}