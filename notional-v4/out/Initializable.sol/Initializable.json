{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "error", "name": "InvalidInitialization", "inputs": []}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b505f805460ff1916600117905561012f806100285f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c8063439fab911461002d575b5f5ffd5b61004061003b3660046100b4565b610042565b005b5f5460ff161561007e576040517ff92ee8a900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b5f80547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001660011790556100b0828282565b5050565b5f5f602083850312156100c5575f5ffd5b823567ffffffffffffffff8111156100db575f5ffd5b8301601f810185136100eb575f5ffd5b803567ffffffffffffffff811115610101575f5ffd5b856020828401011115610112575f5ffd5b602091909101959094509250505056fea164736f6c634300081d000a", "sourceMap": "129:354:83:-:0;;;189:49;;;;;;;;;-1:-1:-1;213:11:83;:18;;-1:-1:-1;;213:18:83;227:4;213:18;;;129:354;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b5060043610610029575f3560e01c8063439fab911461002d575b5f5ffd5b61004061003b3660046100b4565b610042565b005b5f5460ff161561007e576040517ff92ee8a900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b5f80547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff001660011790556100b0828282565b5050565b5f5f602083850312156100c5575f5ffd5b823567ffffffffffffffff8111156100db575f5ffd5b8301601f810185136100eb575f5ffd5b803567ffffffffffffffff811115610101575f5ffd5b856020828401011115610112575f5ffd5b602091909101959094509250505056fea164736f6c634300081d000a", "sourceMap": "129:354:83:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;244:169;;;;;;:::i;:::-;;:::i;:::-;;;308:11;;;;304:47;;;328:23;;;;;;;;;;;;;;304:47;361:11;:18;;;;375:4;361:18;;;389:17;401:4;;389:17;:::i;:::-;244:169;;:::o;14:586:121:-;84:6;92;145:2;133:9;124:7;120:23;116:32;113:52;;;161:1;158;151:12;113:52;201:9;188:23;234:18;226:6;223:30;220:50;;;266:1;263;256:12;220:50;289:22;;342:4;334:13;;330:27;-1:-1:-1;320:55:121;;371:1;368;361:12;320:55;411:2;398:16;437:18;429:6;426:30;423:50;;;469:1;466;459:12;423:50;514:7;509:2;500:6;496:2;492:15;488:24;485:37;482:57;;;535:1;532;525:12;482:57;566:2;558:11;;;;;588:6;;-1:-1:-1;14:586:121;-1:-1:-1;;;14:586:121:o", "linkReferences": {}}, "methodIdentifiers": {"initialize(bytes)": "439fab91"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/proxy/Initializable.sol\":\"Initializable\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/proxy/Initializable.sol": "Initializable"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}}, "version": 1}, "id": 83}