{"abi": [{"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "accrueInterest", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrow", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "assetsBorrowed", "type": "uint256", "internalType": "uint256"}, {"name": "sharesBorrowed", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "createMarket", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enableIrm", "inputs": [{"name": "irm", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enableLltv", "inputs": [{"name": "lltv", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "extSloads", "inputs": [{"name": "slots", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"type": "function", "name": "feeRecipient", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "flashLoan", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isAuthorized", "inputs": [{"name": "authorizer", "type": "address", "internalType": "address"}, {"name": "authorized", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isIrmEnabled", "inputs": [{"name": "irm", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isLltvEnabled", "inputs": [{"name": "lltv", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "seizedAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repaidShares", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "nonce", "inputs": [{"name": "authorizer", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "repay", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "assetsRepaid", "type": "uint256", "internalType": "uint256"}, {"name": "sharesRepaid", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAuthorization", "inputs": [{"name": "authorized", "type": "address", "internalType": "address"}, {"name": "newIsAuthorized", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAuthorizationWithSig", "inputs": [{"name": "authorization", "type": "tuple", "internalType": "struct Authorization", "components": [{"name": "authorizer", "type": "address", "internalType": "address"}, {"name": "authorized", "type": "address", "internalType": "address"}, {"name": "isAuthorized", "type": "bool", "internalType": "bool"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"name": "signature", "type": "tuple", "internalType": "struct Signature", "components": [{"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFee", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "new<PERSON>ee", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeRecipient", "inputs": [{"name": "newFeeRecipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supply", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "assetsSupplied", "type": "uint256", "internalType": "uint256"}, {"name": "sharesSupplied", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "supplyCollateral", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "assetsWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "sharesWithdrawn", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawCollateral", "inputs": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "accrueInterest((address,address,address,address,uint256))": "151c1ade", "borrow((address,address,address,address,uint256),uint256,uint256,address,address)": "50d8cd4b", "createMarket((address,address,address,address,uint256))": "8c1358a2", "enableIrm(address)": "5a64f51e", "enableLltv(uint256)": "4d98a93b", "extSloads(bytes32[])": "7784c685", "feeRecipient()": "46904840", "flashLoan(address,uint256,bytes)": "e0232b42", "isAuthorized(address,address)": "65e4ad9e", "isIrmEnabled(address)": "f2b863ce", "isLltvEnabled(uint256)": "b485f3b8", "liquidate((address,address,address,address,uint256),address,uint256,uint256,bytes)": "d8eabcb8", "nonce(address)": "70ae92d2", "owner()": "8da5cb5b", "repay((address,address,address,address,uint256),uint256,uint256,address,bytes)": "20b76e81", "setAuthorization(address,bool)": "eecea000", "setAuthorizationWithSig((address,address,bool,uint256,uint256),(uint8,bytes32,bytes32))": "8069218f", "setFee((address,address,address,address,uint256),uint256)": "2b4f013c", "setFeeRecipient(address)": "e74b981b", "setOwner(address)": "13af4035", "supply((address,address,address,address,uint256),uint256,uint256,address,bytes)": "a99aad89", "supplyCollateral((address,address,address,address,uint256),uint256,address,bytes)": "238d6579", "withdraw((address,address,address,address,uint256),uint256,uint256,address,address)": "5c2bea49", "withdrawCollateral((address,address,address,address,uint256),uint256,address,address)": "8720316d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"}],\"name\":\"accrueInterest\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"borrow\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsBorrowed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesBorrowed\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"}],\"name\":\"createMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"}],\"name\":\"enableIrm\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"name\":\"enableLltv\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"slots\",\"type\":\"bytes32[]\"}],\"name\":\"extSloads\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeRecipient\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"flashLoan\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"authorizer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"authorized\",\"type\":\"address\"}],\"name\":\"isAuthorized\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"}],\"name\":\"isIrmEnabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"name\":\"isLltvEnabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"seizedAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repaidShares\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"liquidate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"authorizer\",\"type\":\"address\"}],\"name\":\"nonce\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"repay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsRepaid\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesRepaid\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"authorized\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"newIsAuthorized\",\"type\":\"bool\"}],\"name\":\"setAuthorization\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"authorizer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"authorized\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isAuthorized\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"internalType\":\"struct Authorization\",\"name\":\"authorization\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"internalType\":\"struct Signature\",\"name\":\"signature\",\"type\":\"tuple\"}],\"name\":\"setAuthorizationWithSig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"newFee\",\"type\":\"uint256\"}],\"name\":\"setFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newFeeRecipient\",\"type\":\"address\"}],\"name\":\"setFeeRecipient\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"setOwner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"supply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsSupplied\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesSupplied\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"supplyCollateral\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsWithdrawn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesWithdrawn\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"withdrawCollateral\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This interface is used for factorizing IMorphoStaticTyping and IMorpho.Consider using the IMorpho interface instead of this one.\",\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Warning: Every EIP-712 signed message based on this domain separator can be reused on chains sharing the same chain id and on forks because the domain separator would be the same.\"},\"borrow((address,address,address,address,uint256),uint256,uint256,address,address)\":{\"details\":\"Either `assets` or `shares` should be zero. Most use cases should rely on `assets` as an input so the caller is guaranteed to borrow `assets` of tokens, but the possibility to mint a specific amount of shares is given for full compatibility and precision.`msg.sender` must be authorized to manage `onBehalf`'s positions.Borrowing a large amount can revert for overflow.Borrowing an amount of shares may lead to borrow fewer assets than expected due to slippage. Consider using the `assets` parameter to avoid this.\",\"params\":{\"assets\":\"The amount of assets to borrow.\",\"marketParams\":\"The market to borrow assets from.\",\"onBehalf\":\"The address that will own the increased borrow position.\",\"receiver\":\"The address that will receive the borrowed assets.\",\"shares\":\"The amount of shares to mint.\"},\"returns\":{\"assetsBorrowed\":\"The amount of assets borrowed.\",\"sharesBorrowed\":\"The amount of shares minted.\"}},\"createMarket((address,address,address,address,uint256))\":{\"details\":\"Here is the list of assumptions on the market's dependencies (tokens, IRM and oracle) that guarantees Morpho behaves as expected: - The token should be ERC-20 compliant, except that it can omit return values on `transfer` and `transferFrom`. - The token balance of Morpho should only decrease on `transfer` and `transferFrom`. In particular, tokens with burn functions are not supported. - The token should not re-enter Morpho on `transfer` nor `transferFrom`. - The token balance of the sender (resp. receiver) should decrease (resp. increase) by exactly the given amount on `transfer` and `transferFrom`. In particular, tokens with fees on transfer are not supported. - The IRM should not re-enter Morpho. - The oracle should return a price with the correct scaling.Here is a list of assumptions on the market's dependencies which, if broken, could break Morpho's liveness properties (funds could get stuck): - The token should not revert on `transfer` and `transferFrom` if balances and approvals are right. - The amount of assets supplied and borrowed should not go above ~1e35 (otherwise the computation of `toSharesUp` and `toSharesDown` can overflow). - The IRM should not revert on `borrowRate`. - The IRM should not return a very high borrow rate (otherwise the computation of `interest` in `_accrueInterest` can overflow). - The oracle should not revert `price`. - The oracle should not return a very high price (otherwise the computation of `maxBorrow` in `_isHealthy` or of `assetsRepaid` in `liquidate` can overflow).The borrow share price of a market with less than 1e4 assets borrowed can be decreased by manipulations, to the point where `totalBorrowShares` is very large and borrowing overflows.\"},\"enableIrm(address)\":{\"details\":\"Warning: It is not possible to disable an IRM.\"},\"enableLltv(uint256)\":{\"details\":\"Warning: It is not possible to disable a LLTV.\"},\"feeRecipient()\":{\"details\":\"The recipient receives the fees of a given market through a supply position on that market.\"},\"flashLoan(address,uint256,bytes)\":{\"details\":\"Flash loans have access to the whole balance of the contract (the liquidity and deposited collateral of all markets combined, plus donations).Warning: Not ERC-3156 compliant but compatibility is easily reached: - `flashFee` is zero. - `maxFlashLoan` is the token's balance of this contract. - The receiver of `assets` is the caller.\",\"params\":{\"assets\":\"The amount of assets to flash loan.\",\"data\":\"Arbitrary data to pass to the `onMorphoFlashLoan` callback.\",\"token\":\"The token to flash loan.\"}},\"isAuthorized(address,address)\":{\"details\":\"Anyone is authorized to modify their own positions, regardless of this variable.\"},\"liquidate((address,address,address,address,uint256),address,uint256,uint256,bytes)\":{\"details\":\"Either `seizedAssets` or `repaidShares` should be zero.Seizing more than the collateral balance will underflow and revert without any error message.Repaying more than the borrow balance will underflow and revert without any error message.An attacker can front-run a liquidation with a small repay making the transaction revert for underflow.\",\"params\":{\"borrower\":\"The owner of the position.\",\"data\":\"Arbitrary data to pass to the `onMorphoLiquidate` callback. Pass empty data if not needed.\",\"marketParams\":\"The market of the position.\",\"repaidShares\":\"The amount of shares to repay.\",\"seizedAssets\":\"The amount of collateral to seize.\"},\"returns\":{\"_0\":\"The amount of assets seized.\",\"_1\":\"The amount of assets repaid.\"}},\"owner()\":{\"details\":\"It has the power to change the owner.It has the power to set fees on markets and set the fee recipient.It has the power to enable but not disable IRMs and LLTVs.\"},\"repay((address,address,address,address,uint256),uint256,uint256,address,bytes)\":{\"details\":\"Either `assets` or `shares` should be zero. To repay max, pass the `shares`'s balance of `onBehalf`.Repaying an amount corresponding to more shares than borrowed will revert for underflow.It is advised to use the `shares` input when repaying the full position to avoid reverts due to conversion roundings between shares and assets.An attacker can front-run a repay with a small repay making the transaction revert for underflow.\",\"params\":{\"assets\":\"The amount of assets to repay.\",\"data\":\"Arbitrary data to pass to the `onMorphoRepay` callback. Pass empty data if not needed.\",\"marketParams\":\"The market to repay assets to.\",\"onBehalf\":\"The address of the owner of the debt position.\",\"shares\":\"The amount of shares to burn.\"},\"returns\":{\"assetsRepaid\":\"The amount of assets repaid.\",\"sharesRepaid\":\"The amount of shares burned.\"}},\"setAuthorization(address,bool)\":{\"params\":{\"authorized\":\"The authorized address.\",\"newIsAuthorized\":\"The new authorization status.\"}},\"setAuthorizationWithSig((address,address,bool,uint256,uint256),(uint8,bytes32,bytes32))\":{\"details\":\"Warning: Reverts if the signature has already been submitted.The signature is malleable, but it has no impact on the security here.The nonce is passed as argument to be able to revert with a different error message.\",\"params\":{\"authorization\":\"The `Authorization` struct.\",\"signature\":\"The signature.\"}},\"setFee((address,address,address,address,uint256),uint256)\":{\"details\":\"Warning: The recipient can be the zero address.\",\"params\":{\"newFee\":\"The new fee, scaled by WAD.\"}},\"setFeeRecipient(address)\":{\"details\":\"Warning: If the fee recipient is set to the zero address, fees will accrue there and will be lost.Modifying the fee recipient will allow the new recipient to claim any pending fees not yet accrued. To ensure that the current recipient receives all due fees, accrue interest manually prior to making any changes.\"},\"setOwner(address)\":{\"details\":\"Warning: No two-step transfer ownership.Warning: The owner can be set to the zero address.\"},\"supply((address,address,address,address,uint256),uint256,uint256,address,bytes)\":{\"details\":\"Either `assets` or `shares` should be zero. Most use cases should rely on `assets` as an input so the caller is guaranteed to have `assets` tokens pulled from their balance, but the possibility to mint a specific amount of shares is given for full compatibility and precision.Supplying a large amount can revert for overflow.Supplying an amount of shares may lead to supply more or fewer assets than expected due to slippage. Consider using the `assets` parameter to avoid this.\",\"params\":{\"assets\":\"The amount of assets to supply.\",\"data\":\"Arbitrary data to pass to the `onMorphoSupply` callback. Pass empty data if not needed.\",\"marketParams\":\"The market to supply assets to.\",\"onBehalf\":\"The address that will own the increased supply position.\",\"shares\":\"The amount of shares to mint.\"},\"returns\":{\"assetsSupplied\":\"The amount of assets supplied.\",\"sharesSupplied\":\"The amount of shares minted.\"}},\"supplyCollateral((address,address,address,address,uint256),uint256,address,bytes)\":{\"details\":\"Interest are not accrued since it's not required and it saves gas.Supplying a large amount can revert for overflow.\",\"params\":{\"assets\":\"The amount of collateral to supply.\",\"data\":\"Arbitrary data to pass to the `onMorphoSupplyCollateral` callback. Pass empty data if not needed.\",\"marketParams\":\"The market to supply collateral to.\",\"onBehalf\":\"The address that will own the increased collateral position.\"}},\"withdraw((address,address,address,address,uint256),uint256,uint256,address,address)\":{\"details\":\"Either `assets` or `shares` should be zero. To withdraw max, pass the `shares`'s balance of `onBehalf`.`msg.sender` must be authorized to manage `onBehalf`'s positions.Withdrawing an amount corresponding to more shares than supplied will revert for underflow.It is advised to use the `shares` input when withdrawing the full position to avoid reverts due to conversion roundings between shares and assets.\",\"params\":{\"assets\":\"The amount of assets to withdraw.\",\"marketParams\":\"The market to withdraw assets from.\",\"onBehalf\":\"The address of the owner of the supply position.\",\"receiver\":\"The address that will receive the withdrawn assets.\",\"shares\":\"The amount of shares to burn.\"},\"returns\":{\"assetsWithdrawn\":\"The amount of assets withdrawn.\",\"sharesWithdrawn\":\"The amount of shares burned.\"}},\"withdrawCollateral((address,address,address,address,uint256),uint256,address,address)\":{\"details\":\"`msg.sender` must be authorized to manage `onBehalf`'s positions.Withdrawing an amount corresponding to more collateral than supplied will revert for underflow.\",\"params\":{\"assets\":\"The amount of collateral to withdraw.\",\"marketParams\":\"The market to withdraw collateral from.\",\"onBehalf\":\"The address of the owner of the collateral position.\",\"receiver\":\"The address that will receive the collateral assets.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"notice\":\"The EIP-712 domain separator.\"},\"accrueInterest((address,address,address,address,uint256))\":{\"notice\":\"Accrues interest for the given market `marketParams`.\"},\"borrow((address,address,address,address,uint256),uint256,uint256,address,address)\":{\"notice\":\"Borrows `assets` or `shares` on behalf of `onBehalf` and sends the assets to `receiver`.\"},\"createMarket((address,address,address,address,uint256))\":{\"notice\":\"Creates the market `marketParams`.\"},\"enableIrm(address)\":{\"notice\":\"Enables `irm` as a possible IRM for market creation.\"},\"enableLltv(uint256)\":{\"notice\":\"Enables `lltv` as a possible LLTV for market creation.\"},\"extSloads(bytes32[])\":{\"notice\":\"Returns the data stored on the different `slots`.\"},\"feeRecipient()\":{\"notice\":\"The fee recipient of all markets.\"},\"flashLoan(address,uint256,bytes)\":{\"notice\":\"Executes a flash loan.\"},\"isAuthorized(address,address)\":{\"notice\":\"Whether `authorized` is authorized to modify `authorizer`'s position on all markets.\"},\"isIrmEnabled(address)\":{\"notice\":\"Whether the `irm` is enabled.\"},\"isLltvEnabled(uint256)\":{\"notice\":\"Whether the `lltv` is enabled.\"},\"liquidate((address,address,address,address,uint256),address,uint256,uint256,bytes)\":{\"notice\":\"Liquidates the given `repaidShares` of debt asset or seize the given `seizedAssets` of collateral on the given market `marketParams` of the given `borrower`'s position, optionally calling back the caller's `onMorphoLiquidate` function with the given `data`.\"},\"nonce(address)\":{\"notice\":\"The `authorizer`'s current nonce. Used to prevent replay attacks with EIP-712 signatures.\"},\"owner()\":{\"notice\":\"The owner of the contract.\"},\"repay((address,address,address,address,uint256),uint256,uint256,address,bytes)\":{\"notice\":\"Repays `assets` or `shares` on behalf of `onBehalf`, optionally calling back the caller's `onMorphoRepay` function with the given `data`.\"},\"setAuthorization(address,bool)\":{\"notice\":\"Sets the authorization for `authorized` to manage `msg.sender`'s positions.\"},\"setAuthorizationWithSig((address,address,bool,uint256,uint256),(uint8,bytes32,bytes32))\":{\"notice\":\"Sets the authorization for `authorization.authorized` to manage `authorization.authorizer`'s positions.\"},\"setFee((address,address,address,address,uint256),uint256)\":{\"notice\":\"Sets the `newFee` for the given market `marketParams`.\"},\"setFeeRecipient(address)\":{\"notice\":\"Sets `newFeeRecipient` as `feeRecipient` of the fee.\"},\"setOwner(address)\":{\"notice\":\"Sets `newOwner` as `owner` of the contract.\"},\"supply((address,address,address,address,uint256),uint256,uint256,address,bytes)\":{\"notice\":\"Supplies `assets` or `shares` on behalf of `onBehalf`, optionally calling back the caller's `onMorphoSupply` function with the given `data`.\"},\"supplyCollateral((address,address,address,address,uint256),uint256,address,bytes)\":{\"notice\":\"Supplies `assets` of collateral on behalf of `onBehalf`, optionally calling back the caller's `onMorphoSupplyCollateral` function with the given `data`.\"},\"withdraw((address,address,address,address,uint256),uint256,uint256,address,address)\":{\"notice\":\"Withdraws `assets` or `shares` on behalf of `onBehalf` and sends the assets to `receiver`.\"},\"withdrawCollateral((address,address,address,address,uint256),uint256,address,address)\":{\"notice\":\"Withdraws `assets` of collateral on behalf of `onBehalf` and sends the assets to `receiver`.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Morpho/IMorpho.sol\":\"IMorphoBase\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "accrueInterest"}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow", "outputs": [{"internalType": "uint256", "name": "assetsBorrowed", "type": "uint256"}, {"internalType": "uint256", "name": "sharesBorrowed", "type": "uint256"}]}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "createMarket"}, {"inputs": [{"internalType": "address", "name": "irm", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "enableIrm"}, {"inputs": [{"internalType": "uint256", "name": "lltv", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "enableLltv"}, {"inputs": [{"internalType": "bytes32[]", "name": "slots", "type": "bytes32[]"}], "stateMutability": "view", "type": "function", "name": "extSloads", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "flashLoan"}, {"inputs": [{"internalType": "address", "name": "authorizer", "type": "address"}, {"internalType": "address", "name": "authorized", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isAuthorized", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "irm", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isIrmEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "lltv", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "isLltvEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "seizedAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repaidShares", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "authorizer", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "repay", "outputs": [{"internalType": "uint256", "name": "assetsRepaid", "type": "uint256"}, {"internalType": "uint256", "name": "sharesRepaid", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "authorized", "type": "address"}, {"internalType": "bool", "name": "newIsAuthorized", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setAuthorization"}, {"inputs": [{"internalType": "struct Authorization", "name": "authorization", "type": "tuple", "components": [{"internalType": "address", "name": "authorizer", "type": "address"}, {"internalType": "address", "name": "authorized", "type": "address"}, {"internalType": "bool", "name": "isAuthorized", "type": "bool"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}]}, {"internalType": "struct Signature", "name": "signature", "type": "tuple", "components": [{"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setAuthorizationWithSig"}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "new<PERSON>ee", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setFee"}, {"inputs": [{"internalType": "address", "name": "newFeeRecipient", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeRecipient"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "supply", "outputs": [{"internalType": "uint256", "name": "assetsSupplied", "type": "uint256"}, {"internalType": "uint256", "name": "sharesSupplied", "type": "uint256"}]}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "supplyCollateral"}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "assetsWithdrawn", "type": "uint256"}, {"internalType": "uint256", "name": "sharesWithdrawn", "type": "uint256"}]}, {"inputs": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdrawCollateral"}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Warning: Every EIP-712 signed message based on this domain separator can be reused on chains sharing the same chain id and on forks because the domain separator would be the same."}, "borrow((address,address,address,address,uint256),uint256,uint256,address,address)": {"details": "Either `assets` or `shares` should be zero. Most use cases should rely on `assets` as an input so the caller is guaranteed to borrow `assets` of tokens, but the possibility to mint a specific amount of shares is given for full compatibility and precision.`msg.sender` must be authorized to manage `onBehalf`'s positions.Borrowing a large amount can revert for overflow.Borrowing an amount of shares may lead to borrow fewer assets than expected due to slippage. Consider using the `assets` parameter to avoid this.", "params": {"assets": "The amount of assets to borrow.", "marketParams": "The market to borrow assets from.", "onBehalf": "The address that will own the increased borrow position.", "receiver": "The address that will receive the borrowed assets.", "shares": "The amount of shares to mint."}, "returns": {"assetsBorrowed": "The amount of assets borrowed.", "sharesBorrowed": "The amount of shares minted."}}, "createMarket((address,address,address,address,uint256))": {"details": "Here is the list of assumptions on the market's dependencies (tokens, IRM and oracle) that guarantees Morpho behaves as expected: - The token should be ERC-20 compliant, except that it can omit return values on `transfer` and `transferFrom`. - The token balance of Morpho should only decrease on `transfer` and `transferFrom`. In particular, tokens with burn functions are not supported. - The token should not re-enter Morpho on `transfer` nor `transferFrom`. - The token balance of the sender (resp. receiver) should decrease (resp. increase) by exactly the given amount on `transfer` and `transferFrom`. In particular, tokens with fees on transfer are not supported. - The IRM should not re-enter Morpho. - The oracle should return a price with the correct scaling.Here is a list of assumptions on the market's dependencies which, if broken, could break Morpho's liveness properties (funds could get stuck): - The token should not revert on `transfer` and `transferFrom` if balances and approvals are right. - The amount of assets supplied and borrowed should not go above ~1e35 (otherwise the computation of `toSharesUp` and `toSharesDown` can overflow). - The IRM should not revert on `borrowRate`. - The IRM should not return a very high borrow rate (otherwise the computation of `interest` in `_accrueInterest` can overflow). - The oracle should not revert `price`. - The oracle should not return a very high price (otherwise the computation of `maxBorrow` in `_isHealthy` or of `assetsRepaid` in `liquidate` can overflow).The borrow share price of a market with less than 1e4 assets borrowed can be decreased by manipulations, to the point where `totalBorrowShares` is very large and borrowing overflows."}, "enableIrm(address)": {"details": "Warning: It is not possible to disable an IRM."}, "enableLltv(uint256)": {"details": "Warning: It is not possible to disable a LLTV."}, "feeRecipient()": {"details": "The recipient receives the fees of a given market through a supply position on that market."}, "flashLoan(address,uint256,bytes)": {"details": "Flash loans have access to the whole balance of the contract (the liquidity and deposited collateral of all markets combined, plus donations).Warning: Not ERC-3156 compliant but compatibility is easily reached: - `flashFee` is zero. - `maxFlashLoan` is the token's balance of this contract. - The receiver of `assets` is the caller.", "params": {"assets": "The amount of assets to flash loan.", "data": "Arbitrary data to pass to the `onMorphoFlashLoan` callback.", "token": "The token to flash loan."}}, "isAuthorized(address,address)": {"details": "Anyone is authorized to modify their own positions, regardless of this variable."}, "liquidate((address,address,address,address,uint256),address,uint256,uint256,bytes)": {"details": "Either `seizedAssets` or `repaidShares` should be zero.Seizing more than the collateral balance will underflow and revert without any error message.Repaying more than the borrow balance will underflow and revert without any error message.An attacker can front-run a liquidation with a small repay making the transaction revert for underflow.", "params": {"borrower": "The owner of the position.", "data": "Arbitrary data to pass to the `onMorphoLiquidate` callback. Pass empty data if not needed.", "marketParams": "The market of the position.", "repaidShares": "The amount of shares to repay.", "seizedAssets": "The amount of collateral to seize."}, "returns": {"_0": "The amount of assets seized.", "_1": "The amount of assets repaid."}}, "owner()": {"details": "It has the power to change the owner.It has the power to set fees on markets and set the fee recipient.It has the power to enable but not disable IRMs and LLTVs."}, "repay((address,address,address,address,uint256),uint256,uint256,address,bytes)": {"details": "Either `assets` or `shares` should be zero. To repay max, pass the `shares`'s balance of `onBehalf`.Repaying an amount corresponding to more shares than borrowed will revert for underflow.It is advised to use the `shares` input when repaying the full position to avoid reverts due to conversion roundings between shares and assets.An attacker can front-run a repay with a small repay making the transaction revert for underflow.", "params": {"assets": "The amount of assets to repay.", "data": "Arbitrary data to pass to the `onMorphoRepay` callback. Pass empty data if not needed.", "marketParams": "The market to repay assets to.", "onBehalf": "The address of the owner of the debt position.", "shares": "The amount of shares to burn."}, "returns": {"assetsRepaid": "The amount of assets repaid.", "sharesRepaid": "The amount of shares burned."}}, "setAuthorization(address,bool)": {"params": {"authorized": "The authorized address.", "newIsAuthorized": "The new authorization status."}}, "setAuthorizationWithSig((address,address,bool,uint256,uint256),(uint8,bytes32,bytes32))": {"details": "Warning: Reverts if the signature has already been submitted.The signature is malleable, but it has no impact on the security here.The nonce is passed as argument to be able to revert with a different error message.", "params": {"authorization": "The `Authorization` struct.", "signature": "The signature."}}, "setFee((address,address,address,address,uint256),uint256)": {"details": "Warning: The recipient can be the zero address.", "params": {"newFee": "The new fee, scaled by WAD."}}, "setFeeRecipient(address)": {"details": "Warning: If the fee recipient is set to the zero address, fees will accrue there and will be lost.Modifying the fee recipient will allow the new recipient to claim any pending fees not yet accrued. To ensure that the current recipient receives all due fees, accrue interest manually prior to making any changes."}, "setOwner(address)": {"details": "Warning: No two-step transfer ownership.Warning: The owner can be set to the zero address."}, "supply((address,address,address,address,uint256),uint256,uint256,address,bytes)": {"details": "Either `assets` or `shares` should be zero. Most use cases should rely on `assets` as an input so the caller is guaranteed to have `assets` tokens pulled from their balance, but the possibility to mint a specific amount of shares is given for full compatibility and precision.Supplying a large amount can revert for overflow.Supplying an amount of shares may lead to supply more or fewer assets than expected due to slippage. Consider using the `assets` parameter to avoid this.", "params": {"assets": "The amount of assets to supply.", "data": "Arbitrary data to pass to the `onMorphoSupply` callback. Pass empty data if not needed.", "marketParams": "The market to supply assets to.", "onBehalf": "The address that will own the increased supply position.", "shares": "The amount of shares to mint."}, "returns": {"assetsSupplied": "The amount of assets supplied.", "sharesSupplied": "The amount of shares minted."}}, "supplyCollateral((address,address,address,address,uint256),uint256,address,bytes)": {"details": "Interest are not accrued since it's not required and it saves gas.Supplying a large amount can revert for overflow.", "params": {"assets": "The amount of collateral to supply.", "data": "Arbitrary data to pass to the `onMorphoSupplyCollateral` callback. Pass empty data if not needed.", "marketParams": "The market to supply collateral to.", "onBehalf": "The address that will own the increased collateral position."}}, "withdraw((address,address,address,address,uint256),uint256,uint256,address,address)": {"details": "Either `assets` or `shares` should be zero. To withdraw max, pass the `shares`'s balance of `onBehalf`.`msg.sender` must be authorized to manage `onBehalf`'s positions.Withdrawing an amount corresponding to more shares than supplied will revert for underflow.It is advised to use the `shares` input when withdrawing the full position to avoid reverts due to conversion roundings between shares and assets.", "params": {"assets": "The amount of assets to withdraw.", "marketParams": "The market to withdraw assets from.", "onBehalf": "The address of the owner of the supply position.", "receiver": "The address that will receive the withdrawn assets.", "shares": "The amount of shares to burn."}, "returns": {"assetsWithdrawn": "The amount of assets withdrawn.", "sharesWithdrawn": "The amount of shares burned."}}, "withdrawCollateral((address,address,address,address,uint256),uint256,address,address)": {"details": "`msg.sender` must be authorized to manage `on<PERSON>eh<PERSON>f<PERSON>'s positions.Withdrawing an amount corresponding to more collateral than supplied will revert for underflow.", "params": {"assets": "The amount of collateral to withdraw.", "marketParams": "The market to withdraw collateral from.", "onBehalf": "The address of the owner of the collateral position.", "receiver": "The address that will receive the collateral assets."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"DOMAIN_SEPARATOR()": {"notice": "The EIP-712 domain separator."}, "accrueInterest((address,address,address,address,uint256))": {"notice": "Accrues interest for the given market `marketParams`."}, "borrow((address,address,address,address,uint256),uint256,uint256,address,address)": {"notice": "Borrows `assets` or `shares` on behalf of `onBehalf` and sends the assets to `receiver`."}, "createMarket((address,address,address,address,uint256))": {"notice": "Creates the market `marketParams`."}, "enableIrm(address)": {"notice": "Enables `irm` as a possible IRM for market creation."}, "enableLltv(uint256)": {"notice": "Enables `lltv` as a possible LLTV for market creation."}, "extSloads(bytes32[])": {"notice": "Returns the data stored on the different `slots`."}, "feeRecipient()": {"notice": "The fee recipient of all markets."}, "flashLoan(address,uint256,bytes)": {"notice": "Executes a flash loan."}, "isAuthorized(address,address)": {"notice": "Whether `authorized` is authorized to modify `authorizer`'s position on all markets."}, "isIrmEnabled(address)": {"notice": "Whether the `irm` is enabled."}, "isLltvEnabled(uint256)": {"notice": "Whether the `lltv` is enabled."}, "liquidate((address,address,address,address,uint256),address,uint256,uint256,bytes)": {"notice": "Liquidates the given `repaidShares` of debt asset or seize the given `seizedAssets` of collateral on the given market `marketParams` of the given `borrower`'s position, optionally calling back the caller's `onMorphoLiquidate` function with the given `data`."}, "nonce(address)": {"notice": "The `authorizer`'s current nonce. Used to prevent replay attacks with EIP-712 signatures."}, "owner()": {"notice": "The owner of the contract."}, "repay((address,address,address,address,uint256),uint256,uint256,address,bytes)": {"notice": "Repays `assets` or `shares` on behalf of `onBehalf`, optionally calling back the caller's `onMorphoRepay` function with the given `data`."}, "setAuthorization(address,bool)": {"notice": "Sets the authorization for `authorized` to manage `msg.sender`'s positions."}, "setAuthorizationWithSig((address,address,bool,uint256,uint256),(uint8,bytes32,bytes32))": {"notice": "Sets the authorization for `authorization.authorized` to manage `authorization.authorizer`'s positions."}, "setFee((address,address,address,address,uint256),uint256)": {"notice": "Sets the `newFee` for the given market `marketParams`."}, "setFeeRecipient(address)": {"notice": "Sets `newFeeRecipient` as `feeRecipient` of the fee."}, "setOwner(address)": {"notice": "Sets `new<PERSON>wner` as `owner` of the contract."}, "supply((address,address,address,address,uint256),uint256,uint256,address,bytes)": {"notice": "Supplies `assets` or `shares` on behalf of `onBehalf`, optionally calling back the caller's `onMorphoSupply` function with the given `data`."}, "supplyCollateral((address,address,address,address,uint256),uint256,address,bytes)": {"notice": "Supplies `assets` of collateral on behalf of `onBehalf`, optionally calling back the caller's `onMorphoSupplyCollateral` function with the given `data`."}, "withdraw((address,address,address,address,uint256),uint256,uint256,address,address)": {"notice": "Withdraws `assets` or `shares` on behalf of `onBehalf` and sends the assets to `receiver`."}, "withdrawCollateral((address,address,address,address,uint256),uint256,address,address)": {"notice": "Withdraws `assets` of collateral on behalf of `onBehalf` and sends the assets to `receiver`."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Morpho/IMorpho.sol": "IMorphoBase"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 75}