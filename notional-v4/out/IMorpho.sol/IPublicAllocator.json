{"abi": [{"type": "function", "name": "reallocateTo", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "withdrawals", "type": "tuple[]", "internalType": "struct <PERSON><PERSON><PERSON>[]", "components": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "amount", "type": "uint128", "internalType": "uint128"}]}, {"name": "supplyMarketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"reallocateTo(address,((address,address,address,address,uint256),uint128)[],(address,address,address,address,uint256))": "833947fd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"}],\"internalType\":\"struct Withdrawal[]\",\"name\":\"withdrawals\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"supplyMarketParams\",\"type\":\"tuple\"}],\"name\":\"reallocateTo\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This interface is used for factorizing IPublicAllocatorStaticTyping and IPublicAllocator.Consider using the IPublicAllocator interface instead of this one.\",\"kind\":\"dev\",\"methods\":{\"reallocateTo(address,((address,address,address,address,uint256),uint128)[],(address,address,address,address,uint256))\":{\"details\":\"Will call MetaMorpho's `reallocate`.Checks that the flow caps are respected.Will revert when `withdrawals` contains a duplicate or is not sorted.Will revert if `withdrawals` contains the supply market.Will revert if a withdrawal amount is larger than available liquidity.\",\"params\":{\"supplyMarketParams\":\"The market receiving total withdrawn to.\",\"vault\":\"The MetaMorpho vault to reallocate.\",\"withdrawals\":\"The markets to withdraw from,and the amounts to withdraw.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"reallocateTo(address,((address,address,address,address,uint256),uint128)[],(address,address,address,address,uint256))\":{\"notice\":\"Reallocates from a list of markets to one market.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/Morpho/IMorpho.sol\":\"IPublicAllocator\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "struct <PERSON><PERSON><PERSON>[]", "name": "withdrawals", "type": "tuple[]", "components": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint128", "name": "amount", "type": "uint128"}]}, {"internalType": "struct MarketParams", "name": "supplyMarketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}], "stateMutability": "payable", "type": "function", "name": "reallocateTo"}], "devdoc": {"kind": "dev", "methods": {"reallocateTo(address,((address,address,address,address,uint256),uint128)[],(address,address,address,address,uint256))": {"details": "Will call MetaMorpho's `reallocate`.Checks that the flow caps are respected.Will revert when `withdrawals` contains a duplicate or is not sorted.Will revert if `withdrawals` contains the supply market.Will revert if a withdrawal amount is larger than available liquidity.", "params": {"supplyMarketParams": "The market receiving total withdrawn to.", "vault": "The MetaMorpho vault to reallocate.", "withdrawals": "The markets to withdraw from,and the amounts to withdraw."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"reallocateTo(address,((address,address,address,address,uint256),uint128)[],(address,address,address,address,uint256))": {"notice": "Reallocates from a list of markets to one market."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/Morpho/IMorpho.sol": "IPublicAllocator"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 75}