{"abi": [{"type": "function", "name": "STAKING_TOKEN", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "WITHDRAW_TOKEN", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "YIELD_TOKEN", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "canFinalizeWithdrawRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "finalizeAndRedeemWithdrawRequest", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "withdrawYieldTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "sharesToBurn", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokensWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "finalized", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "finalizeRequestManual", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "tokensWithdrawn", "type": "uint256", "internalType": "uint256"}, {"name": "finalized", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getWithdrawRequest", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "w", "type": "tuple", "internalType": "struct WithdrawRequest", "components": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "yieldTokenAmount", "type": "uint120", "internalType": "uint120"}, {"name": "sharesAmount", "type": "uint120", "internalType": "uint120"}]}, {"name": "s", "type": "tuple", "internalType": "struct TokenizedWithdrawRequest", "components": [{"name": "totalYieldTokenAmount", "type": "uint120", "internalType": "uint120"}, {"name": "totalWithdraw", "type": "uint120", "internalType": "uint120"}, {"name": "finalized", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getWithdrawRequestValue", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "hasRequest", "type": "bool", "internalType": "bool"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "yieldTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "sharesAmount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApprovedVault", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isPendingWithdrawRequest", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "rescueTokens", "inputs": [{"name": "cooldownHolder", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApp<PERSON>Vault", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "isApproved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stakeTokens", "inputs": [{"name": "depositToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "yieldTokensMinted", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenizeWithdrawRequest", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "sharesAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "didTokenize", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "ApprovedVault", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "isApproved", "type": "bool", "indexed": true, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "InitiateWithdrawRequest", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "yieldTokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "sharesAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "requestId", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "WithdrawRequestTokenized", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sharesAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"STAKING_TOKEN()": "0479d644", "WITHDRAW_TOKEN()": "3ed3a054", "YIELD_TOKEN()": "544bc96d", "canFinalizeWithdrawRequest(uint256)": "c2ded8c1", "finalizeAndRedeemWithdrawRequest(address,uint256,uint256)": "ed020beb", "finalizeRequestManual(address,address)": "a7b87572", "getWithdrawRequest(address,address)": "afbf911a", "getWithdrawRequestValue(address,address,address,uint256)": "32df6ff2", "initiateWithdraw(address,uint256,uint256,bytes)": "7c86cff5", "isApprovedVault(address)": "df78a625", "isPendingWithdrawRequest(address,address)": "37504d9c", "rescueTokens(address,address,address,uint256)": "d5fc623c", "setApprovedVault(address,bool)": "d665761a", "stakeTokens(address,uint256,bytes)": "e7c35c3c", "tokenizeWithdrawRequest(address,address,uint256)": "838f705b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"isApproved\",\"type\":\"bool\"}],\"name\":\"ApprovedVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"yieldTokenAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"name\":\"InitiateWithdrawRequest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"}],\"name\":\"WithdrawRequestTokenized\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"STAKING_TOKEN\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"WITHDRAW_TOKEN\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"YIELD_TOKEN\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"name\":\"canFinalizeWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"withdrawYieldTokenAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesToBurn\",\"type\":\"uint256\"}],\"name\":\"finalizeAndRedeemWithdrawRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tokensWithdrawn\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"finalized\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"finalizeRequestManual\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tokensWithdrawn\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"finalized\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getWithdrawRequest\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"uint120\",\"name\":\"yieldTokenAmount\",\"type\":\"uint120\"},{\"internalType\":\"uint120\",\"name\":\"sharesAmount\",\"type\":\"uint120\"}],\"internalType\":\"struct WithdrawRequest\",\"name\":\"w\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint120\",\"name\":\"totalYieldTokenAmount\",\"type\":\"uint120\"},{\"internalType\":\"uint120\",\"name\":\"totalWithdraw\",\"type\":\"uint120\"},{\"internalType\":\"bool\",\"name\":\"finalized\",\"type\":\"bool\"}],\"internalType\":\"struct TokenizedWithdrawRequest\",\"name\":\"s\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"getWithdrawRequestValue\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"hasRequest\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"yieldTokenAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"isApprovedVault\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isPendingWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"cooldownHolder\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"rescueTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isApproved\",\"type\":\"bool\"}],\"name\":\"setApprovedVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"depositToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"stakeTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"yieldTokensMinted\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesAmount\",\"type\":\"uint256\"}],\"name\":\"tokenizeWithdrawRequest\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"didTokenize\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"STAKING_TOKEN()\":{\"returns\":{\"_0\":\"stakingToken the staking token of the withdraw request manager\"}},\"WITHDRAW_TOKEN()\":{\"returns\":{\"_0\":\"withdrawToken the withdraw token of the withdraw request manager\"}},\"YIELD_TOKEN()\":{\"returns\":{\"_0\":\"yieldToken the yield token of the withdraw request manager\"}},\"canFinalizeWithdrawRequest(uint256)\":{\"params\":{\"requestId\":\"the request id of the withdraw request\"},\"returns\":{\"_0\":\"canFinalize whether the withdraw request can be finalized\"}},\"finalizeAndRedeemWithdrawRequest(address,uint256,uint256)\":{\"params\":{\"account\":\"the account to finalize and redeem the withdraw request for\",\"sharesToBurn\":\"the amount of shares to burn for the yield token\",\"withdrawYieldTokenAmount\":\"the amount of yield tokens to withdraw\"},\"returns\":{\"finalized\":\"whether the withdraw request was finalized\",\"tokensWithdrawn\":\"amount of withdraw tokens redeemed from the withdraw requests\"}},\"finalizeRequestManual(address,address)\":{\"details\":\"No access control is enforced on this function but no tokens are transferred off the request manager either.\"},\"getWithdrawRequest(address,address)\":{\"params\":{\"account\":\"the account to get the withdraw request for\",\"vault\":\"the vault to get the withdraw request for\"},\"returns\":{\"s\":\"the tokenized withdraw request\",\"w\":\"the withdraw request\"}},\"getWithdrawRequestValue(address,address,address,uint256)\":{\"params\":{\"account\":\"the account to get the withdraw request for\",\"asset\":\"the asset to get the value for\",\"shares\":\"the amount of shares to get the value for\",\"vault\":\"the vault to get the withdraw request for\"},\"returns\":{\"hasRequest\":\"whether the account has a withdraw request\",\"value\":\"the value of the withdraw request in terms of the asset\"}},\"initiateWithdraw(address,uint256,uint256,bytes)\":{\"details\":\"Only approved vaults can initiate withdraw requests\",\"params\":{\"account\":\"the account to initiate the withdraw request for\",\"data\":\"additional data for the withdraw request\",\"sharesAmount\":\"the amount of shares to withdraw, used to mark the shares to yield token ratio at the time of the withdraw request\",\"yieldTokenAmount\":\"the amount of yield tokens to withdraw\"},\"returns\":{\"requestId\":\"the request id of the withdraw request\"}},\"isApprovedVault(address)\":{\"params\":{\"vault\":\"the vault to check the approval for\"},\"returns\":{\"_0\":\"isApproved whether the vault is approved\"}},\"isPendingWithdrawRequest(address,address)\":{\"params\":{\"account\":\"the account to check the pending withdraw request for\",\"vault\":\"the vault to check the pending withdraw request for\"},\"returns\":{\"_0\":\"isPending whether the vault has a pending withdraw request\"}},\"rescueTokens(address,address,address,uint256)\":{\"params\":{\"amount\":\"the amount of tokens to rescue\",\"cooldownHolder\":\"the cooldown holder to rescue tokens from\",\"receiver\":\"the receiver of the rescued tokens\",\"token\":\"the token to rescue\"}},\"setApprovedVault(address,bool)\":{\"params\":{\"isApproved\":\"whether the vault is approved\",\"vault\":\"the vault to set the approval for\"}},\"stakeTokens(address,uint256,bytes)\":{\"details\":\"Only approved vaults can stake tokens\",\"params\":{\"amount\":\"the amount of tokens to stake\",\"data\":\"additional data for the stake\",\"depositToken\":\"the token to stake, will be transferred from the vault\"}},\"tokenizeWithdrawRequest(address,address,uint256)\":{\"details\":\"Only approved vaults can tokenize withdraw requests\",\"params\":{\"from\":\"the account that is being liquidated\",\"sharesAmount\":\"the amount of shares to the liquidator\",\"to\":\"the liquidator\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"STAKING_TOKEN()\":{\"notice\":\"Returns the token that will be used to stake\"},\"WITHDRAW_TOKEN()\":{\"notice\":\"Returns the token that will be the result of the withdraw request\"},\"YIELD_TOKEN()\":{\"notice\":\"Returns the token that will be the result of staking\"},\"canFinalizeWithdrawRequest(uint256)\":{\"notice\":\"Returns whether a withdraw request can be finalized\"},\"finalizeAndRedeemWithdrawRequest(address,uint256,uint256)\":{\"notice\":\"Attempts to redeem active withdraw requests during vault exit\"},\"finalizeRequestManual(address,address)\":{\"notice\":\"Finalizes withdraw requests outside of a vault exit. This may be required in cases if an account is negligent in exiting their vault position and letting the withdraw request sit idle could result in losses. The withdraw request is finalized and stored in a tokenized withdraw request where the account has the full claim on the withdraw.\"},\"getWithdrawRequest(address,address)\":{\"notice\":\"Returns the withdraw request and tokenized withdraw request for an account\"},\"getWithdrawRequestValue(address,address,address,uint256)\":{\"notice\":\"Returns the value of a withdraw request in terms of the asset\"},\"initiateWithdraw(address,uint256,uint256,bytes)\":{\"notice\":\"Initiates a withdraw request\"},\"isApprovedVault(address)\":{\"notice\":\"Returns whether a vault is approved to initiate withdraw requests\"},\"isPendingWithdrawRequest(address,address)\":{\"notice\":\"Returns whether a vault has a pending withdraw request\"},\"rescueTokens(address,address,address,uint256)\":{\"notice\":\"Allows the emergency exit role to rescue tokens from the withdraw request manager\"},\"setApprovedVault(address,bool)\":{\"notice\":\"Sets whether a vault is approved to initiate withdraw requests\"},\"stakeTokens(address,uint256,bytes)\":{\"notice\":\"Stakes the deposit token to the yield token and transfers it back to the vault\"},\"tokenizeWithdrawRequest(address,address,uint256)\":{\"notice\":\"If an account has an illiquid withdraw request, this method will tokenize their claim on it during liquidation.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/IWithdrawRequestManager.sol\":\"IWithdrawRequestManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "bool", "name": "isApproved", "type": "bool", "indexed": true}], "type": "event", "name": "ApprovedVault", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "yieldTokenAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": false}], "type": "event", "name": "InitiateWithdrawRequest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "WithdrawRequestTokenized", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "STAKING_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "WITHDRAW_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "YIELD_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "canFinalizeWithdrawRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "withdrawYieldTokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "sharesToBurn", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeAndRedeemWithdrawRequest", "outputs": [{"internalType": "uint256", "name": "tokensWithdrawn", "type": "uint256"}, {"internalType": "bool", "name": "finalized", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "finalizeRequestManual", "outputs": [{"internalType": "uint256", "name": "tokensWithdrawn", "type": "uint256"}, {"internalType": "bool", "name": "finalized", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getWithdrawRequest", "outputs": [{"internalType": "struct WithdrawRequest", "name": "w", "type": "tuple", "components": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "uint120", "name": "yieldTokenAmount", "type": "uint120"}, {"internalType": "uint120", "name": "sharesAmount", "type": "uint120"}]}, {"internalType": "struct TokenizedWithdrawRequest", "name": "s", "type": "tuple", "components": [{"internalType": "uint120", "name": "totalYieldTokenAmount", "type": "uint120"}, {"internalType": "uint120", "name": "totalWithdraw", "type": "uint120"}, {"internalType": "bool", "name": "finalized", "type": "bool"}]}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getWithdrawRequestValue", "outputs": [{"internalType": "bool", "name": "hasRequest", "type": "bool"}, {"internalType": "uint256", "name": "value", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "yieldTokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedVault", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isPendingWithdrawRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "cooldownHolder", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "rescueTokens"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bool", "name": "isApproved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApp<PERSON>Vault"}, {"inputs": [{"internalType": "address", "name": "depositToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "stakeTokens", "outputs": [{"internalType": "uint256", "name": "yieldTokensMinted", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "sharesAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "tokenizeWithdrawRequest", "outputs": [{"internalType": "bool", "name": "didTokenize", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"STAKING_TOKEN()": {"returns": {"_0": "stakingToken the staking token of the withdraw request manager"}}, "WITHDRAW_TOKEN()": {"returns": {"_0": "withdrawToken the withdraw token of the withdraw request manager"}}, "YIELD_TOKEN()": {"returns": {"_0": "yieldToken the yield token of the withdraw request manager"}}, "canFinalizeWithdrawRequest(uint256)": {"params": {"requestId": "the request id of the withdraw request"}, "returns": {"_0": "canFinalize whether the withdraw request can be finalized"}}, "finalizeAndRedeemWithdrawRequest(address,uint256,uint256)": {"params": {"account": "the account to finalize and redeem the withdraw request for", "sharesToBurn": "the amount of shares to burn for the yield token", "withdrawYieldTokenAmount": "the amount of yield tokens to withdraw"}, "returns": {"finalized": "whether the withdraw request was finalized", "tokensWithdrawn": "amount of withdraw tokens redeemed from the withdraw requests"}}, "finalizeRequestManual(address,address)": {"details": "No access control is enforced on this function but no tokens are transferred off the request manager either."}, "getWithdrawRequest(address,address)": {"params": {"account": "the account to get the withdraw request for", "vault": "the vault to get the withdraw request for"}, "returns": {"s": "the tokenized withdraw request", "w": "the withdraw request"}}, "getWithdrawRequestValue(address,address,address,uint256)": {"params": {"account": "the account to get the withdraw request for", "asset": "the asset to get the value for", "shares": "the amount of shares to get the value for", "vault": "the vault to get the withdraw request for"}, "returns": {"hasRequest": "whether the account has a withdraw request", "value": "the value of the withdraw request in terms of the asset"}}, "initiateWithdraw(address,uint256,uint256,bytes)": {"details": "Only approved vaults can initiate withdraw requests", "params": {"account": "the account to initiate the withdraw request for", "data": "additional data for the withdraw request", "sharesAmount": "the amount of shares to withdraw, used to mark the shares to yield token ratio at the time of the withdraw request", "yieldTokenAmount": "the amount of yield tokens to withdraw"}, "returns": {"requestId": "the request id of the withdraw request"}}, "isApprovedVault(address)": {"params": {"vault": "the vault to check the approval for"}, "returns": {"_0": "isApproved whether the vault is approved"}}, "isPendingWithdrawRequest(address,address)": {"params": {"account": "the account to check the pending withdraw request for", "vault": "the vault to check the pending withdraw request for"}, "returns": {"_0": "isPending whether the vault has a pending withdraw request"}}, "rescueTokens(address,address,address,uint256)": {"params": {"amount": "the amount of tokens to rescue", "cooldownHolder": "the cooldown holder to rescue tokens from", "receiver": "the receiver of the rescued tokens", "token": "the token to rescue"}}, "setApprovedVault(address,bool)": {"params": {"isApproved": "whether the vault is approved", "vault": "the vault to set the approval for"}}, "stakeTokens(address,uint256,bytes)": {"details": "Only approved vaults can stake tokens", "params": {"amount": "the amount of tokens to stake", "data": "additional data for the stake", "depositToken": "the token to stake, will be transferred from the vault"}}, "tokenizeWithdrawRequest(address,address,uint256)": {"details": "Only approved vaults can tokenize withdraw requests", "params": {"from": "the account that is being liquidated", "sharesAmount": "the amount of shares to the liquidator", "to": "the liquidator"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"STAKING_TOKEN()": {"notice": "Returns the token that will be used to stake"}, "WITHDRAW_TOKEN()": {"notice": "Returns the token that will be the result of the withdraw request"}, "YIELD_TOKEN()": {"notice": "Returns the token that will be the result of staking"}, "canFinalizeWithdrawRequest(uint256)": {"notice": "Returns whether a withdraw request can be finalized"}, "finalizeAndRedeemWithdrawRequest(address,uint256,uint256)": {"notice": "Attempts to redeem active withdraw requests during vault exit"}, "finalizeRequestManual(address,address)": {"notice": "Finalizes withdraw requests outside of a vault exit. This may be required in cases if an account is negligent in exiting their vault position and letting the withdraw request sit idle could result in losses. The withdraw request is finalized and stored in a tokenized withdraw request where the account has the full claim on the withdraw."}, "getWithdrawRequest(address,address)": {"notice": "Returns the withdraw request and tokenized withdraw request for an account"}, "getWithdrawRequestValue(address,address,address,uint256)": {"notice": "Returns the value of a withdraw request in terms of the asset"}, "initiateWithdraw(address,uint256,uint256,bytes)": {"notice": "Initiates a withdraw request"}, "isApprovedVault(address)": {"notice": "Returns whether a vault is approved to initiate withdraw requests"}, "isPendingWithdrawRequest(address,address)": {"notice": "Returns whether a vault has a pending withdraw request"}, "rescueTokens(address,address,address,uint256)": {"notice": "Allows the emergency exit role to rescue tokens from the withdraw request manager"}, "setApprovedVault(address,bool)": {"notice": "Sets whether a vault is approved to initiate withdraw requests"}, "stakeTokens(address,uint256,bytes)": {"notice": "Stakes the deposit token to the yield token and transfers it back to the vault"}, "tokenizeWithdrawRequest(address,address,uint256)": {"notice": "If an account has an illiquid withdraw request, this method will tokenize their claim on it during liquidation."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/IWithdrawRequestManager.sol": "IWithdrawRequestManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}}, "version": 1}, "id": 73}