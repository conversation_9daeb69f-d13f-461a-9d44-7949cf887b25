{"abi": [{"type": "function", "name": "canExecuteTrade", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "dexId", "type": "uint16", "internalType": "uint16"}, {"name": "trade", "type": "tuple", "internalType": "struct Trade", "components": [{"name": "tradeType", "type": "uint8", "internalType": "enum TradeType"}, {"name": "sellToken", "type": "address", "internalType": "address"}, {"name": "buyToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "limit", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "exchangeData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "executeTrade", "inputs": [{"name": "dexId", "type": "uint16", "internalType": "uint16"}, {"name": "trade", "type": "tuple", "internalType": "struct Trade", "components": [{"name": "tradeType", "type": "uint8", "internalType": "enum TradeType"}, {"name": "sellToken", "type": "address", "internalType": "address"}, {"name": "buyToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "limit", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "exchangeData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "amountSold", "type": "uint256", "internalType": "uint256"}, {"name": "amountBought", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "executeTradeWithDynamicSlippage", "inputs": [{"name": "dexId", "type": "uint16", "internalType": "uint16"}, {"name": "trade", "type": "tuple", "internalType": "struct Trade", "components": [{"name": "tradeType", "type": "uint8", "internalType": "enum TradeType"}, {"name": "sellToken", "type": "address", "internalType": "address"}, {"name": "buyToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "limit", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "exchangeData", "type": "bytes", "internalType": "bytes"}]}, {"name": "dynamicSlippageLimit", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "amountSold", "type": "uint256", "internalType": "uint256"}, {"name": "amountBought", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "getExecutionData", "inputs": [{"name": "dexId", "type": "uint16", "internalType": "uint16"}, {"name": "from", "type": "address", "internalType": "address"}, {"name": "trade", "type": "tuple", "internalType": "struct Trade", "components": [{"name": "tradeType", "type": "uint8", "internalType": "enum TradeType"}, {"name": "sellToken", "type": "address", "internalType": "address"}, {"name": "buyToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "limit", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "exchangeData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "params", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getLimitAmount", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "tradeType", "type": "uint8", "internalType": "enum TradeType"}, {"name": "sellToken", "type": "address", "internalType": "address"}, {"name": "buyToken", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "slippageLimit", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "limitAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOraclePrice", "inputs": [{"name": "inToken", "type": "address", "internalType": "address"}, {"name": "outToken", "type": "address", "internalType": "address"}], "outputs": [{"name": "answer", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "priceOracles", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "oracle", "type": "address", "internalType": "contract AggregatorV2V3Interface"}, {"name": "rateDecimals", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "setMaxOracleFreshness", "inputs": [{"name": "newMaxOracleFreshnessInSeconds", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPriceO<PERSON>le", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "contract AggregatorV2V3Interface"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setTokenPermissions", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "permissions", "type": "tuple", "internalType": "struct ITradingModule.TokenPermissions", "components": [{"name": "allowSell", "type": "bool", "internalType": "bool"}, {"name": "dex<PERSON><PERSON><PERSON>", "type": "uint32", "internalType": "uint32"}, {"name": "tradeTypeFlags", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "allowSell", "type": "bool", "internalType": "bool"}, {"name": "dex<PERSON><PERSON><PERSON>", "type": "uint32", "internalType": "uint32"}, {"name": "tradeTypeFlags", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "event", "name": "MaxOracleFreshnessUpdated", "inputs": [{"name": "currentValue", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "newValue", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "PriceOracleUpdated", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "oracle", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TokenPermissionsUpdated", "inputs": [{"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "permissions", "type": "tuple", "indexed": false, "internalType": "struct ITradingModule.TokenPermissions", "components": [{"name": "allowSell", "type": "bool", "internalType": "bool"}, {"name": "dex<PERSON><PERSON><PERSON>", "type": "uint32", "internalType": "uint32"}, {"name": "tradeTypeFlags", "type": "uint32", "internalType": "uint32"}]}], "anonymous": false}, {"type": "event", "name": "TradeExecuted", "inputs": [{"name": "sellToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyToken", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sellAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "buyAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"canExecuteTrade(address,uint16,(uint8,address,address,uint256,uint256,uint256,bytes))": "a014a2fb", "executeTrade(uint16,(uint8,address,address,uint256,uint256,uint256,bytes))": "2ba8c23c", "executeTradeWithDynamicSlippage(uint16,(uint8,address,address,uint256,uint256,uint256,bytes),uint32)": "5caf9fda", "getExecutionData(uint16,address,(uint8,address,address,uint256,uint256,uint256,bytes))": "792d44f4", "getLimitAmount(address,uint8,address,address,uint256,uint32)": "4f00f623", "getOraclePrice(address,address)": "4c2d8eff", "priceOracles(address)": "01374518", "setMaxOracleFreshness(uint32)": "203a9ec1", "setPriceOracle(address,address)": "67a74ddc", "setTokenPermissions(address,address,(bool,uint32,uint32))": "ddf11c38", "tokenWhitelist(address,address)": "bfb8afa7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"currentValue\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"newValue\",\"type\":\"uint32\"}],\"name\":\"MaxOracleFreshnessUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"}],\"name\":\"PriceOracleUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"allowSell\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"dexFlags\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"tradeTypeFlags\",\"type\":\"uint32\"}],\"indexed\":false,\"internalType\":\"struct ITradingModule.TokenPermissions\",\"name\":\"permissions\",\"type\":\"tuple\"}],\"name\":\"TokenPermissionsUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"sellAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"buyAmount\",\"type\":\"uint256\"}],\"name\":\"TradeExecuted\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"dexId\",\"type\":\"uint16\"},{\"components\":[{\"internalType\":\"enum TradeType\",\"name\":\"tradeType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"exchangeData\",\"type\":\"bytes\"}],\"internalType\":\"struct Trade\",\"name\":\"trade\",\"type\":\"tuple\"}],\"name\":\"canExecuteTrade\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"dexId\",\"type\":\"uint16\"},{\"components\":[{\"internalType\":\"enum TradeType\",\"name\":\"tradeType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"exchangeData\",\"type\":\"bytes\"}],\"internalType\":\"struct Trade\",\"name\":\"trade\",\"type\":\"tuple\"}],\"name\":\"executeTrade\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountSold\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountBought\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"dexId\",\"type\":\"uint16\"},{\"components\":[{\"internalType\":\"enum TradeType\",\"name\":\"tradeType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"exchangeData\",\"type\":\"bytes\"}],\"internalType\":\"struct Trade\",\"name\":\"trade\",\"type\":\"tuple\"},{\"internalType\":\"uint32\",\"name\":\"dynamicSlippageLimit\",\"type\":\"uint32\"}],\"name\":\"executeTradeWithDynamicSlippage\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountSold\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountBought\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"dexId\",\"type\":\"uint16\"},{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"enum TradeType\",\"name\":\"tradeType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"exchangeData\",\"type\":\"bytes\"}],\"internalType\":\"struct Trade\",\"name\":\"trade\",\"type\":\"tuple\"}],\"name\":\"getExecutionData\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"params\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"enum TradeType\",\"name\":\"tradeType\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"sellToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"buyToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"slippageLimit\",\"type\":\"uint32\"}],\"name\":\"getLimitAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"limitAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"inToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"outToken\",\"type\":\"address\"}],\"name\":\"getOraclePrice\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"answer\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"decimals\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"priceOracles\",\"outputs\":[{\"internalType\":\"contract AggregatorV2V3Interface\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"uint8\",\"name\":\"rateDecimals\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"newMaxOracleFreshnessInSeconds\",\"type\":\"uint32\"}],\"name\":\"setMaxOracleFreshness\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"contract AggregatorV2V3Interface\",\"name\":\"oracle\",\"type\":\"address\"}],\"name\":\"setPriceOracle\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"allowSell\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"dexFlags\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"tradeTypeFlags\",\"type\":\"uint32\"}],\"internalType\":\"struct ITradingModule.TokenPermissions\",\"name\":\"permissions\",\"type\":\"tuple\"}],\"name\":\"setTokenPermissions\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"tokenWhitelist\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"allowSell\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"dexFlags\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"tradeTypeFlags\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/ITradingModule.sol\":\"ITradingModule\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32", "name": "currentValue", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "newValue", "type": "uint32", "indexed": false}], "type": "event", "name": "MaxOracleFreshnessUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "address", "name": "oracle", "type": "address", "indexed": false}], "type": "event", "name": "PriceOracleUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "struct ITradingModule.TokenPermissions", "name": "permissions", "type": "tuple", "components": [{"internalType": "bool", "name": "allowSell", "type": "bool"}, {"internalType": "uint32", "name": "dex<PERSON><PERSON><PERSON>", "type": "uint32"}, {"internalType": "uint32", "name": "tradeTypeFlags", "type": "uint32"}], "indexed": false}], "type": "event", "name": "TokenPermissionsUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sellToken", "type": "address", "indexed": true}, {"internalType": "address", "name": "buyToken", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "sellAmount", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "buyAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "TradeExecuted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint16", "name": "dexId", "type": "uint16"}, {"internalType": "struct Trade", "name": "trade", "type": "tuple", "components": [{"internalType": "enum TradeType", "name": "tradeType", "type": "uint8"}, {"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "exchangeData", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "canExecuteTrade", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint16", "name": "dexId", "type": "uint16"}, {"internalType": "struct Trade", "name": "trade", "type": "tuple", "components": [{"internalType": "enum TradeType", "name": "tradeType", "type": "uint8"}, {"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "exchangeData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "executeTrade", "outputs": [{"internalType": "uint256", "name": "amountSold", "type": "uint256"}, {"internalType": "uint256", "name": "amountBought", "type": "uint256"}]}, {"inputs": [{"internalType": "uint16", "name": "dexId", "type": "uint16"}, {"internalType": "struct Trade", "name": "trade", "type": "tuple", "components": [{"internalType": "enum TradeType", "name": "tradeType", "type": "uint8"}, {"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "exchangeData", "type": "bytes"}]}, {"internalType": "uint32", "name": "dynamicSlippageLimit", "type": "uint32"}], "stateMutability": "payable", "type": "function", "name": "executeTradeWithDynamicSlippage", "outputs": [{"internalType": "uint256", "name": "amountSold", "type": "uint256"}, {"internalType": "uint256", "name": "amountBought", "type": "uint256"}]}, {"inputs": [{"internalType": "uint16", "name": "dexId", "type": "uint16"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "struct Trade", "name": "trade", "type": "tuple", "components": [{"internalType": "enum TradeType", "name": "tradeType", "type": "uint8"}, {"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bytes", "name": "exchangeData", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "getExecutionData", "outputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "params", "type": "bytes"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "enum TradeType", "name": "tradeType", "type": "uint8"}, {"internalType": "address", "name": "sellToken", "type": "address"}, {"internalType": "address", "name": "buyToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint32", "name": "slippageLimit", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getLimitAmount", "outputs": [{"internalType": "uint256", "name": "limitAmount", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "inToken", "type": "address"}, {"internalType": "address", "name": "outToken", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getOraclePrice", "outputs": [{"internalType": "int256", "name": "answer", "type": "int256"}, {"internalType": "int256", "name": "decimals", "type": "int256"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "priceOracles", "outputs": [{"internalType": "contract AggregatorV2V3Interface", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "rateDecimals", "type": "uint8"}]}, {"inputs": [{"internalType": "uint32", "name": "newMaxOracleFreshnessInSeconds", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "setMaxOracleFreshness"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "contract AggregatorV2V3Interface", "name": "oracle", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setPriceO<PERSON>le"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "struct ITradingModule.TokenPermissions", "name": "permissions", "type": "tuple", "components": [{"internalType": "bool", "name": "allowSell", "type": "bool"}, {"internalType": "uint32", "name": "dex<PERSON><PERSON><PERSON>", "type": "uint32"}, {"internalType": "uint32", "name": "tradeTypeFlags", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setTokenPermissions"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "allowSell", "type": "bool"}, {"internalType": "uint32", "name": "dex<PERSON><PERSON><PERSON>", "type": "uint32"}, {"internalType": "uint32", "name": "tradeTypeFlags", "type": "uint32"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/interfaces/ITradingModule.sol": "ITradingModule"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}}, "version": 1}, "id": 71}