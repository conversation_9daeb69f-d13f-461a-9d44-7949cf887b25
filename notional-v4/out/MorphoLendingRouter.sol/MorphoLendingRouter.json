{"abi": [{"type": "function", "name": "allocateAndEnterPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "depositAssetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}, {"name": "depositData", "type": "bytes", "internalType": "bytes"}, {"name": "allocationData", "type": "tuple[]", "internalType": "struct MorphoAllocation[]", "components": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "feeAmount", "type": "uint256", "internalType": "uint256"}, {"name": "withdrawals", "type": "tuple[]", "internalType": "struct <PERSON><PERSON><PERSON>[]", "components": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "amount", "type": "uint128", "internalType": "uint128"}]}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "allocateAndMigratePosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "migrateFrom", "type": "address", "internalType": "address"}, {"name": "allocationData", "type": "tuple[]", "internalType": "struct MorphoAllocation[]", "components": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "feeAmount", "type": "uint256", "internalType": "uint256"}, {"name": "withdrawals", "type": "tuple[]", "internalType": "struct <PERSON><PERSON><PERSON>[]", "components": [{"name": "marketParams", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}, {"name": "amount", "type": "uint128", "internalType": "uint128"}]}]}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "balanceOfCollateral", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "collateralBalance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimRewards", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "rewards", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "enterPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "depositAssetAmount", "type": "uint256", "internalType": "uint256"}, {"name": "borrowAmount", "type": "uint256", "internalType": "uint256"}, {"name": "depositData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exitPosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "sharesToRedeem", "type": "uint256", "internalType": "uint256"}, {"name": "assetToRepay", "type": "uint256", "internalType": "uint256"}, {"name": "redeemData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "forceWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "healthFactor", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "borrowed", "type": "uint256", "internalType": "uint256"}, {"name": "collateralValue", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initializeMarket", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initiateWithdraw", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApproved", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "liquidateAccount", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "sharesToLiquidate", "type": "uint256", "internalType": "uint256"}, {"name": "debtToRepay", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "sharesToLiquidator", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "marketParams", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct MarketParams", "components": [{"name": "loanToken", "type": "address", "internalType": "address"}, {"name": "collateralToken", "type": "address", "internalType": "address"}, {"name": "oracle", "type": "address", "internalType": "address"}, {"name": "irm", "type": "address", "internalType": "address"}, {"name": "lltv", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "migratePosition", "inputs": [{"name": "onBehalf", "type": "address", "internalType": "address"}, {"name": "vault", "type": "address", "internalType": "address"}, {"name": "migrateFrom", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "onMorphoFlashLoan", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "onMorphoLiquidate", "inputs": [{"name": "repaidAssets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "onMorphoRepay", "inputs": [{"name": "assetToRepay", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "set<PERSON><PERSON><PERSON>al", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "error", "name": "CannotEnterPosition", "inputs": []}, {"type": "error", "name": "CannotExitPositionWithinCooldownPeriod", "inputs": []}, {"type": "error", "name": "CannotForceWithdraw", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "CannotLiquidateZeroShares", "inputs": []}, {"type": "error", "name": "InsufficientAssetsForRepayment", "inputs": [{"name": "assetsToRepay", "type": "uint256", "internalType": "uint256"}, {"name": "assetsWithdrawn", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InsufficientSharesHeld", "inputs": []}, {"type": "error", "name": "InvalidLendingRouter", "inputs": []}, {"type": "error", "name": "LiquidatorHasPosition", "inputs": []}, {"type": "error", "name": "NoExistingPosition", "inputs": []}, {"type": "error", "name": "NotAuthorized", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "user", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "1042:9613:89:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1042:9613:89:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7201:1505;;;;;;;;;;-1:-1:-1;7201:1505:89;;;;;:::i;:::-;;:::i;:::-;;3595:323;;;;;;:::i;:::-;;:::i;4546:822::-;;;;;;;;;;-1:-1:-1;4546:822:89;;;;;:::i;:::-;;:::i;7647:646:88:-;;;;;;;;;;-1:-1:-1;7647:646:88;;;;;:::i;:::-;;:::i;:::-;;;3208:25:121;;;3196:2;3181:18;7647:646:88;;;;;;;;1315:468:89;;;;;;;;;;-1:-1:-1;1315:468:89;;;;;:::i;:::-;;:::i;9818:834::-;;;;;;;;;;-1:-1:-1;9818:834:89;;;;;:::i;:::-;;:::i;:::-;;;;4352:25:121;;;4408:2;4393:18;;4386:34;;;;4436:18;;;4429:34;4340:2;4325:18;9818:834:89;4150:319:121;1889:140:88;;;;;;;;;;-1:-1:-1;1889:140:88;;;;;:::i;:::-;;:::i;:::-;;;4639:14:121;;4632:22;4614:41;;4602:2;4587:18;1889:140:88;4474:187:121;7351:255:88;;;;;;;;;;-1:-1:-1;7351:255:88;;;;;:::i;:::-;;:::i;5009:2301::-;;;;;;;;;;-1:-1:-1;5009:2301:88;;;;;:::i;:::-;;:::i;2444:532::-;;;;;;;;;;-1:-1:-1;2444:532:88;;;;;:::i;:::-;;:::i;9176:374:89:-;;;;;;;;;;-1:-1:-1;9176:374:89;;;;;:::i;:::-;;:::i;1789:153::-;;;;;;;;;;-1:-1:-1;1789:153:89;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;9556:256::-;;;;;;;;;;-1:-1:-1;9556:256:89;;;;;:::i;:::-;;:::i;1634:214:88:-;;;;;;;;;;-1:-1:-1;1634:214:88;;;;;:::i;:::-;;:::i;2070:333::-;;;;;;;;;;-1:-1:-1;2070:333:88;;;;;:::i;:::-;;:::i;8334:202::-;;;;;;;;;;-1:-1:-1;8334:202:88;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;4043:925::-;;;;;;;;;;-1:-1:-1;4043:925:88;;;;;:::i;:::-;;:::i;3163:426:89:-;;;;;;:::i;:::-;;:::i;7201:1505::-;7303:10;19626:42:75;7303:29:89;7295:38;;;;;;7358:19;;;;;;;7575:76;;;;7586:4;7575:76;:::i;:::-;7344:307;;;;;;;;;;;;;;7662:23;7688:125;7715:11;7728:5;7735;7742:9;:33;;7773:1;7742:33;;;7754:8;7742:33;7777:14;7793:10;7688:13;:125::i;:::-;7662:151;;7828:9;7824:295;;;7996:68;-1:-1:-1;;;;;7996:29:89;;8026:8;8044:4;8051:12;7996:29;:68::i;:::-;-1:-1:-1;8096:12:89;7824:295;8199:12;8181:15;:30;8177:223;;;8328:61;;;;;;;;13833:25:121;;;13874:18;;;13867:34;;;13806:18;;8328:61:89;;;;;;;;8177:223;8487:30;;;8537:53;-1:-1:-1;;;;;8537:25:89;;8563:8;8487:30;8537:25;:53::i;:::-;8643:56;-1:-1:-1;;;;;8643:25:89;;19626:42:75;8686:12:89;8643:25;:56::i;:::-;7285:1421;;;;;;;;;7201:1505;;;:::o;3595:323::-;3797:8;3807:5;1322:10:88;-1:-1:-1;;;;;1322:22:88;;;;;;:59;;;1349:32;1360:8;1370:10;1349;:32::i;:::-;1348:33;1322:59;1318:132;;;1404:35;;;;;1418:10;1404:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1404:35:88;13912:346:121;1318:132:88;3824:32:89::1;3834:5;3841:14;;3824:9;:32::i;:::-;3866:45;3882:8;3892:5;3899:11;3866:15;:45::i;:::-;1558:5:88::0;-1:-1:-1;;;;;1543:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3595:323:89;;;;;;;:::o;4546:822::-;4646:10;19626:42:75;4646:29:89;4638:38;;;;;;4701:16;;;;;;4895:70;;;;4906:4;4895:70;:::i;:::-;4687:278;;;;;;;;;;;;4976:94;4992:8;5002:5;5009;5025:18;5016:6;:27;;;;:::i;:::-;5045:11;5058;4976:15;:94::i;:::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2083:21:89;;;;;;:14;:21;;;;;;2054:50;;;;;;;;;;;;;;;;;;;;;;2122:177;;;;;;;;;;;;;;;;;;;;;;;;;2247:10;2122:177;;;;;2277:11;2122:177;;;;;;;5203:52;;;;;19626:42:75;;5203:13:89;;:52;;2122:177;;5220:6;;-1:-1:-1;5231:8:89;;5249:4;;5203:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;5311:50:89;;-1:-1:-1;;;;;;5311:25:89;;19626:42:75;5354:6:89;5311:25;:50::i;:::-;4628:740;;;;;;;4546:822;;;:::o;7647:646:88:-;7741:17;7936:16;7961:17;7982:28;7995:7;8004:5;7982:12;:28::i;:::-;7935:75;;;;;8036:9;8024:8;:21;8020:62;;8054:28;;;;;-1:-1:-1;;;;;16795:55:121;;8054:28:88;;;16777:74:121;16750:18;;8054:28:88;16631:226:121;8020:62:88;8105:39;8123:5;8130:7;8139:4;;8105:17;:39::i;:::-;8093:51;;8258:5;-1:-1:-1;;;;;8243:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7760:533;;7647:646;;;;;;:::o;1315:468:89:-;1445:10;-1:-1:-1;;;;;1410:45:89;676:42:97;-1:-1:-1;;;;;1410:29:89;;:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1410:45:89;;1402:54;;;;;;-1:-1:-1;;;;;1530:21:89;;;1567:1;1530:21;;;:14;:21;;;;;:25;;:39;1522:48;;;;;;-1:-1:-1;;;;;1588:21:89;;;;;;:14;:21;;;;;;;;:26;;:31;1580:40;;;;;;1655:70;;;;;;;;-1:-1:-1;;;;;1655:70:89;;;;;;;;;;;;1631:21;;;-1:-1:-1;1631:21:89;;;:14;:21;;;;;;;;:94;;;;;;;;;;;;;;;;;;;19626:42:75;1736:19:89;1756;1631:21;1756:12;:19::i;:::-;1736:40;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1315:468;;;:::o;9818:834::-;9898:16;9916:23;9941:17;9970:21;9994:19;10007:5;9994:12;:19::i;:::-;9970:43;;10023:5;10031:11;10040:1;10031:8;:11::i;:::-;10123:24;;;;;10023:19;;-1:-1:-1;19626:42:75;;10123:21:89;;:24;;10145:1;;10123:24;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;10184:29:89;;;;;;;;17316:25:121;;;-1:-1:-1;;;;;17377:55:121;;17357:18;;;17350:83;10157:24:89;;-1:-1:-1;19626:42:75;;-1:-1:-1;10184:15:89;;17289:18:121;;10184:29:89;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10246:17;;;;;;;;3208:25:121;;;10157:56:89;;-1:-1:-1;10223:20:89;;19626:42:75;;10246:13:89;;3181:18:121;;10246:17:89;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10278:21;;;;10223:40;;-1:-1:-1;10278:25:89;;;10274:214;;10409:6;:24;;;10401:33;;10372:6;:24;;;10364:33;;10339:8;:21;;;10331:30;;:66;;;;:::i;:::-;10330:104;;;;:::i;:::-;10319:115;;10274:214;;;10476:1;10465:12;;10274:214;10547:37;;;;;-1:-1:-1;;;;;16795:55:121;;;10547:37:89;;;16777:74:121;10588:4:89;;10547:27;;;;;;16750:18:121;;10547:37:89;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10524:8;:19;;;10516:28;;:68;;;;:::i;:::-;10515:77;;;;:::i;:::-;10497:95;;10641:4;10632:1;:6;;;10614:15;:24;;;;:::i;:::-;:31;;;;:::i;:::-;10602:43;;9960:692;;;;9818:834;;;;;:::o;1889:140:88:-;-1:-1:-1;;;;;1994:18:88;;;1971:4;1994:18;;;;;;;;;;;:28;;;;;;;;;;;;1889:140;;;;;:::o;7351:255::-;7518:17;7492:8;7502:5;1322:10;-1:-1:-1;;;;;1322:22:88;;;;;;:59;;;1349:32;1360:8;1370:10;1349;:32::i;:::-;1348:33;1322:59;1318:132;;;1404:35;;;;;1418:10;1404:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1404:35:88;13912:346:121;1318:132:88;7559:40:::1;7577:5;7584:8;7594:4;;7559:17;:40::i;:::-;7547:52;;1558:5:::0;-1:-1:-1;;;;;1543:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5009:2301;5182:26;5224:17;5245:1;5224:22;5220:62;;5255:27;;;;;;;;;;;;;;5220:62;5366:52;;;;;5314:10;5366:52;;;14086:74:121;;;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;5314:10:88;5293:18;;676:42:97;;5366:33:88;;14059:18:121;;5366:52:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5586:22;;5334:84;;-1:-1:-1;;;;;;5586:36:88;;5582:72;;5631:23;;;;;;;;;;;;;;5582:72;5665:21;5689:44;5709:16;5727:5;5689:19;:44::i;:::-;5665:68;;5747:13;5764:1;5747:18;5743:55;;5774:24;;;;;;;;;;;;;;5743:55;6025:100;;;;;-1:-1:-1;;;;;21532:55:121;;;6025:100:88;;;21514:74:121;21624:55;;;21604:18;;;21597:83;21696:18;;;21689:34;;;21739:18;;;21732:34;;;6025:36:88;;;;;21486:19:121;;6025:100:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6231:79;6242:10;6254:5;6261:16;6279:17;6298:11;6231:10;:79::i;:::-;6490:87;;;;;-1:-1:-1;;;;;21997:55:121;;;6490:87:88;;;21979:74:121;22089:55;;;22069:18;;;22062:83;22161:18;;;22154:34;;;6210:100:88;;-1:-1:-1;6490:37:88;;;;;;21952:18:121;;6490:87:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7233:13;7211:18;:35;7207:96;;7248:55;;;;;-1:-1:-1;;;;;14104:55:121;;;7248::88;;;14086:74:121;14196:55;;14176:18;;;14169:83;676:42:97;;7248:30:88;;14059:18:121;;7248:55:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7207:96;5210:2100;;;5009:2301;;;;;;:::o;2444:532::-;2582:8;2592:5;1322:10;-1:-1:-1;;;;;1322:22:88;;;;;;:59;;;1349:32;1360:8;1370:10;1349;:32::i;:::-;1348:33;1322:59;1318:132;;;1404:35;;;;;1418:10;1404:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1404:35:88;13912:346:121;1318:132:88;2614:45:::1;::::0;;;;-1:-1:-1;;;;;16795:55:121;;2614:45:88::1;::::0;::::1;16777:74:121::0;676:42:97::1;::::0;2614:32:88::1;::::0;16750:18:121;;2614:45:88::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2609:81;;2668:22;;;;;;;;;;;;;;2609:81;2829:57;::::0;;;;-1:-1:-1;;;;;14104:55:121;;;2829:57:88::1;::::0;::::1;14086:74:121::0;14196:55;;;14176:18;;;14169:83;2791:20:88::1;::::0;2829:40;;::::1;::::0;::::1;::::0;14059:18:121;;2829:57:88::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2790:96;;;;2897:72;2912:8;2922:5;2929:1;2932:12;2946:9;;;;;;;;;;;::::0;2957:11:::1;2897:14;:72::i;:::-;2599:377;1558:5:::0;-1:-1:-1;;;;;1543:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2444:532;;;;;:::o;9176:374:89:-;9282:10;19626:42:75;9282:29:89;9274:38;;;;;;9323:13;;9360:36;;;;9371:4;9360:36;:::i;:::-;9322:74;;-1:-1:-1;9322:74:89;-1:-1:-1;9407:70:89;-1:-1:-1;;;;;9407:29:89;;9322:74;9457:4;9464:12;9407:29;:70::i;:::-;9487:56;-1:-1:-1;;;;;9487:25:89;;19626:42:75;9530:12:89;9487:25;:56::i;:::-;9264:286;;9176:374;;;:::o;1789:153::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1885:50:89;1898:5;1920;-1:-1:-1;;;;;1905:27:89;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2083:21:89;;;;;;:14;:21;;;;;;;2054:50;;;;;;;;;;;;;;;;;;;;;;2122:177;;;;;;;;;;;;;;;;;;;;;;2247:10;2122:177;;;;;;;;;;2277:11;;2122:177;;;;;1948:358;9556:256;9647:25;9684:21;9708:19;9721:5;9708:12;:19::i;:::-;9684:43;-1:-1:-1;19626:42:75;9757:15:89;9773:11;9684:43;9773:8;:11::i;:::-;9757:37;;;;;;;;;;;;;17316:25:121;;;;-1:-1:-1;;;;;17377:55:121;;17357:18;;;17350:83;17289:18;;9757:37:89;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;;9737:68;;;9556:256;-1:-1:-1;;;;9556:256:89:o;1634:214:88:-;1732:10;-1:-1:-1;;;;;1720:22:88;;;1716:70;;1751:35;;;;;1765:10;1751:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1751:35:88;13912:346:121;1716:70:88;1809:10;1796:12;:24;;;;;;;;;;;-1:-1:-1;;;;;1796:34:88;;;;;;;;;;;;;:45;;;;;;;;;;;;;1634:214::o;2070:333::-;2279:8;2289:5;1322:10;-1:-1:-1;;;;;1322:22:88;;;;;;:59;;;1349:32;1360:8;1370:10;1349;:32::i;:::-;1348:33;1322:59;1318:132;;;1404:35;;;;;1418:10;1404:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1404:35:88;13912:346:121;1318:132:88;2306:90:::1;2321:8;2331:5;2338:18;2358:12;2372:11;;2306:90;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;::::0;;;-1:-1:-1;2306:90:88;-1:-1:-1;2306:14:88::1;::::0;-1:-1:-1;;2306:90:88:i:1;:::-;1558:5:::0;-1:-1:-1;;;;;1543:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8334:202;8389:24;8451:5;-1:-1:-1;;;;;8432:45:88;;8478:10;8490:38;8510:10;8522:5;8490:19;:38::i;:::-;8432:97;;;;;;;;;;-1:-1:-1;;;;;23511:55:121;;;8432:97:88;;;23493:74:121;23583:18;;;23576:34;23466:18;;8432:97:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;8432:97:88;;;;;;;;;;;;:::i;4043:925::-;4274:8;4284:5;1322:10;-1:-1:-1;;;;;1322:22:88;;;;;;:59;;;1349:32;1360:8;1370:10;1349;:32::i;:::-;1348:33;1322:59;1318:132;;;1404:35;;;;;1418:10;1404:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1404:35:88;13912:346:121;1318:132:88;4301:27:::1;4312:8;4322:5;4301:10;:27::i;:::-;4339:13;4370:5;-1:-1:-1::0;;;;;4355:27:88::1;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4339:45:::0;-1:-1:-1;4398:16:88;;4394:439:::1;;4430:90;4445:8;4455:5;4462;4469:8;4479:14;4495:12;4509:10;;4430:14;:90::i;:::-;4394:439;;;4551:17;4571:20;4582:8;4571:10;:20::i;:::-;:44;;4613:1;4571:44;;;4594:8;4571:44;4551:64;;4629:23;4655:76;4669:8;4679:5;4686;4693:9;4704:14;4720:10;;4655:76;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;::::0;;;;-1:-1:-1;4655:13:88::1;::::0;-1:-1:-1;;;4655:76:88:i:1;:::-;4629:102:::0;-1:-1:-1;4749:19:88;;4745:77:::1;;4770:52;-1:-1:-1::0;;;;;4770:25:88;::::1;4796:8:::0;4806:15;4770:25:::1;:52::i;:::-;4537:296;;4394:439;4847:36;4867:8;4877:5;4847:19;:36::i;:::-;4887:1;4847:41:::0;4843:119:::1;;4904:47;::::0;;;;-1:-1:-1;;;;;14104:55:121;;;4904:47:88::1;::::0;::::1;14086:74:121::0;14196:55;;14176:18;;;14169:83;676:42:97::1;::::0;4904:30:88::1;::::0;14059:18:121;;4904:47:88::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4843:119;4291:677;1558:5:::0;-1:-1:-1;;;;;1543:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4043:925;;;;;;;;;:::o;3163:426:89:-;3436:8;3446:5;1322:10:88;-1:-1:-1;;;;;1322:22:88;;;;;;:59;;;1349:32;1360:8;1370:10;1349;:32::i;:::-;1348:33;1322:59;1318:132;;;1404:35;;;;;1418:10;1404:35;;;14086:74:121;-1:-1:-1;;;;;14196:55:121;;14176:18;;;14169:83;14059:18;;1404:35:88;13912:346:121;1318:132:88;3463:32:89::1;3473:5;3480:14;;3463:9;:32::i;:::-;3505:77;3519:8;3529:5;3536:18;3556:12;3570:11;;3505:13;:77::i;:::-;1558:5:88::0;-1:-1:-1;;;;;1543:41:88;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3163:426:89;;;;;;;;;;:::o;10767:888:88:-;10981:23;;-1:-1:-1;;;;;11035:23:88;;;:49;;11075:9;11035:49;;;11061:11;11035:49;11016:68;;11094:18;11115:39;11135:11;11148:5;11115:19;:39::i;:::-;11239:74;;;;;-1:-1:-1;;;;;24826:55:121;;;11239:74:88;;;24808::121;24898:18;;;24891:34;;;24961:55;;;24941:18;;;24934:83;11094:60:88;;-1:-1:-1;11239:35:88;;;;;;24781:18:121;;11239:74:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;11323:72;11343:5;11350;11357:14;11373:11;11386:8;11323:19;:72::i;:::-;-1:-1:-1;;;;;11466:23:88;;11462:187;;11523:115;;;;;-1:-1:-1;;;;;11523:32:88;;;;;:115;;11573:11;;11586:14;;11602:10;;11614;;11523:115;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11505:133;;11462:187;11006:649;;10767:888;;;;;;;;:::o;1618:188:19:-;1745:53;;-1:-1:-1;;;;;21997:55:121;;;1745:53:19;;;21979:74:121;22089:55;;;22069:18;;;22062:83;22161:18;;;22154:34;;;1718:81:19;;1738:5;;1760:18;;;;;21952::121;;1745:53:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1718:19;:81::i;:::-;1618:188;;;;:::o;1219:160::-;1328:43;;-1:-1:-1;;;;;23511:55:121;;;1328:43:19;;;23493:74:121;23583:18;;;23576:34;;;1301:71:19;;1321:5;;1343:14;;;;;23466:18:121;;1328:43:19;23319:297:121;1301:71:19;1219:160;;;:::o;798:180:98:-;-1:-1:-1;;;;;889:28:98;;885:41;;798:180;;;:::o;885:41::-;936:35;-1:-1:-1;;;;;936:18:98;;955:7;964:6;936:18;:35::i;2605:552:89:-;2702:21;2726:19;2739:5;2726:12;:19::i;:::-;2702:43;-1:-1:-1;2756:22:89;;2788:290;2808:25;;;2788:290;;;20885:42:75;2854:29:89;2891:14;;2906:1;2891:17;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;:27;;;2937:14;;2952:1;2937:17;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;:23;;;;;;;:::i;:::-;2962:14;;2977:1;2962:17;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;:29;;;;;;;:::i;:::-;2993:1;2854:154;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3040:14;;3055:1;3040:17;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;3022:45;;3040:27;;;3022:45;;:::i;:::-;;-1:-1:-1;2835:3:89;;2788:290;;;;3108:14;3095:9;:27;3087:63;;;;;;;29264:2:121;3087:63:89;;;29246:21:121;29303:2;29283:18;;;29276:30;29342:25;29322:18;;;29315:53;29385:18;;3087:63:89;29062:347:121;9683:994:88;9896:22;-1:-1:-1;;;;;9934:25:88;;;9930:673;;10065:51;-1:-1:-1;;;;;10065:25:88;;10091:11;10104;10065:25;:51::i;:::-;10147:64;;;;;-1:-1:-1;;;;;14104:55:121;;;10147:64:88;;;14086:74:121;14196:55;;;14176:18;;;14169:83;10147:47:88;;;;;14059:18:121;;10147:64:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10399:9;;;;;;;;-1:-1:-1;10399:9:88;;10274:148;;;;;10130:81;;-1:-1:-1;;;;;;10274:40:88;;;;;:148;;10332:8;;10342:5;;10357:4;;10130:81;;10380:17;;10399:9;10274:148;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9930:673;;;10453:40;;;;;-1:-1:-1;;;;;23511:55:121;;;10453:40:88;;;23493:74:121;23583:18;;;23576:34;;;10453:20:88;;;;;23466:18:121;;10453:40:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;10524:68:88;;;;;-1:-1:-1;;;;;10524:32:88;;;;;:68;;10557:11;;10570:8;;10580:11;;10524:68;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10507:85;;9930:673;10613:57;10631:8;10641:5;10648;10655:14;10613:17;:57::i;:::-;9683:994;;;;;;;;:::o;11744:364::-;11872:17;11901:18;11922:35;11942:7;11951:5;11922:19;:35::i;:::-;11901:56;;11971:10;11985:1;11971:15;11967:52;;11995:24;;;;;;;;;;;;;;11967:52;12036:65;;;;;-1:-1:-1;;;;;12036:38:88;;;;;:65;;12075:7;;12084:10;;12096:4;;;;12036:65;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;2312:125:89:-;2376:2;2426:1;2415:13;;;;;;;;:::i;:::-;;;;;;;;;;;;;2405:24;;;;;;2390:40;;2312:125;;;:::o;8712:458::-;8914:26;8952:21;8976:19;8989:5;8976:12;:19::i;:::-;8952:43;;19626:42:75;-1:-1:-1;;;;;9035:16:89;;9065:1;9068:16;9086:17;9105:11;9129:1;:11;;;9142:10;9118:35;;;;;;;;-1:-1:-1;;;;;14104:55:121;;;14086:74;;14196:55;;14191:2;14176:18;;14169:83;14074:2;14059:18;;13912:346;9118:35:89;;;;;;;;;;;;;9035:128;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;9005:158:89;8712:458;-1:-1:-1;;;;;;;8712:458:89:o;2982:1020:88:-;3209:13;3240:5;-1:-1:-1;;;;;3225:27:88;;:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3353:41;;;;;-1:-1:-1;;;;;16795:55:121;;;3353:41:88;;;16777:74:121;3209:45:88;;-1:-1:-1;3397:1:88;;3353:31;;;;;16750:18:121;;3353:41:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:45;3349:79;;;3407:21;;;;;;;;;;;;;;3349:79;3443:22;;3439:194;;3546:76;-1:-1:-1;;;;;3546:29:88;;3576:10;3596:4;3603:18;3546:29;:76::i;:::-;3647:16;;3643:297;;3679:134;3717:8;3727:5;3734;3741:18;3761:12;3775:11;3788;3679:20;:134::i;:::-;3643:297;;;3844:85;3860:8;3870:5;3877;3884:18;3904:11;3917;3844:15;:85::i;:::-;;3643:297;3950:45;;;;;-1:-1:-1;;;;;14104:55:121;;;3950:45:88;;;14086:74:121;14196:55;;14176:18;;;14169:83;676:42:97;;3950:28:88;;14059:18:121;;3950:45:88;;;;;;;;;;;;;;;;;;;8933:394;9043:50;;;;;-1:-1:-1;;;;;14104:55:121;;;9043:50:88;;;14086:74:121;14196:55;;14176:18;;;14169:83;9011:29:88;;676:42:97;;9043:33:88;;14059:18:121;;9043:50:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9107:22;;9011:82;;-1:-1:-1;;;;;;9107:39:88;9141:4;9107:39;9103:72;;9155:20;;;;;;;;;;;;;;9103:72;415:9:97;9207:8:88;:22;;;9189:40;;:15;:40;;;;:::i;:::-;:58;9185:136;;;9270:40;;;;;;;;;;;;;;6281:914:89;6534:21;6558:26;6571:5;6578;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2083:21:89;;;;;;:14;:21;;;;;;;2054:50;;;;;;;;;;;;;;;;;;;;;;2122:177;;;;;;;;;;;;;;;;;;;;;;2247:10;2122:177;;;;;;;;;;2277:11;;2122:177;;;;;1948:358;6558:26;6534:50;;6595:21;6646:17;6630:12;:33;6626:283;;19626:42:75;6817:15:89;6833:11;6842:1;6833:8;:11::i;:::-;6817:38;;;;;;;;;;;;;17316:25:121;;;;-1:-1:-1;;;;;17377:55:121;;17357:18;;;17350:83;17289:18;;6817:38:89;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:51;;;6801:67;;;;6897:1;6882:16;;6626:283;6919:22;6968:8;6978:5;6985;6992:8;7002:14;7018:10;;7030:20;7041:8;7030:10;:20::i;:::-;6944:116;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;6919:141;;19626:42:75;-1:-1:-1;;;;;7123:12:89;;7136:1;7139:12;7153:13;7168:8;7178:9;7123:65;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;9435:161:88:-;9496:4;-1:-1:-1;;;;;9519:22:88;;9531:10;9519:22;:70;;;;-1:-1:-1;9545:44:88;;;;;9578:10;9545:44;;;16777:74:121;676:42:97;;9545:32:88;;16750:18:121;;9545:44:88;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;5945:330:89:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2083:21:89;;;;;;:14;:21;;;;;;;2054:50;;;;;;;;;;;;;;;;;;;;;;2122:177;;;;;;;;;;;;;;;;;;;;;;2247:10;;2122:177;;;;;;;;;;2277:11;2122:177;;;;;;;6201:67;;;;;19626:42:75;;6201:25:89;;:67;;2122:177;;6230:14;;6246:11;;6259:8;;6201:67;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8370:720:19;8450:18;8478:19;8616:4;8613:1;8606:4;8600:11;8593:4;8587;8583:15;8580:1;8573:5;8566;8561:60;8673:7;8663:176;;8717:4;8711:11;8762:16;8759:1;8754:3;8739:40;8808:16;8803:3;8796:29;8663:176;-1:-1:-1;;8916:1:19;8910:8;8866:16;;-1:-1:-1;8942:15:19;;:68;;8994:11;9009:1;8994:16;;8942:68;;;-1:-1:-1;;;;;8960:26:19;;;:31;8942:68;8938:146;;;9033:40;;;;;-1:-1:-1;;;;;16795:55:121;;9033:40:19;;;16777:74:121;16750:18;;9033:40:19;16631:226:121;5084:380:19;5199:47;;;-1:-1:-1;;;;;23511:55:121;;5199:47:19;;;23493:74:121;23583:18;;;;23576:34;;;5199:47:19;;;;;;;;;;23466:18:121;;;;5199:47:19;;;;;;;;;;;;;;5262:44;5214:13;5199:47;5262:23;:44::i;:::-;5257:201;;5349:43;;-1:-1:-1;;;;;23511:55:121;;;5349:43:19;;;23493:74:121;5389:1:19;23583:18:121;;;23576:34;5322:71:19;;5342:5;;5364:13;;;;;23466:18:121;;5349:43:19;23319:297:121;5322:71:19;5407:40;5427:5;5434:12;5407:19;:40::i;5374:565:89:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2083:21:89;;;;;;:14;:21;;;;;;2054:50;;;;;;;;;;;;;;;;;;;;;;2122:177;;;;;;;;;;;;;;;;;;;;;;;;2247:10;2122:177;;;;;;;;;2277:11;;2122:177;;;;;;;5678:78;;;;;19626:42:75;5678:78:89;;;24808:74:121;24898:18;;;24891:34;;;24961:55;;;24941:18;;;24934:83;5678:78:89;;2122:177;;5678:35;;24781:18:121;;;;;-1:-1:-1;5678:78:89;;;;;-1:-1:-1;2083:21:89;5678:78;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5813:53:89;;;;;19626:42:75;5813:53:89;;;23493:74:121;23583:18;;;23576:34;;;-1:-1:-1;;;;;5813:20:89;;;-1:-1:-1;5813:20:89;;-1:-1:-1;23466:18:121;;5813:53:89;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;5876:56:89;;;;;19626:42:75;;5876:23:89;;:56;;5900:1;;5903:14;;5919:8;;5876:56;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;3924:616;4340:26;4393:8;4403:5;4410;4417:18;4437:11;4450;4369:102;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;4369:102:89;;;;;;;;;;4481:52;;;4369:102;-1:-1:-1;19626:42:75;;4481:16:89;;:52;;4498:5;;4505:12;;4369:102;;4481:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;9592:480:19;9675:4;9691:12;9713:18;9741:19;9875:4;9872:1;9865:4;9859:11;9852:4;9846;9842:15;9839:1;9832:5;9825;9820:60;9809:71;;9907:16;9893:30;;9957:1;9951:8;9936:23;;9985:7;:80;;;;-1:-1:-1;9997:15:19;;:67;;10048:11;10063:1;10048:16;9997:67;;;-1:-1:-1;;;;;;;;;;10015:26:19;;:30;;;9592:480::o;14:347:121:-;65:8;75:6;129:3;122:4;114:6;110:17;106:27;96:55;;147:1;144;137:12;96:55;-1:-1:-1;170:20:121;;213:18;202:30;;199:50;;;245:1;242;235:12;199:50;282:4;274:6;270:17;258:29;;334:3;327:4;318:6;310;306:19;302:30;299:39;296:59;;;351:1;348;341:12;296:59;14:347;;;;;:::o;366:523::-;445:6;453;461;514:2;502:9;493:7;489:23;485:32;482:52;;;530:1;527;520:12;482:52;575:23;;;-1:-1:-1;673:2:121;658:18;;645:32;700:18;689:30;;686:50;;;732:1;729;722:12;686:50;771:58;821:7;812:6;801:9;797:22;771:58;:::i;:::-;366:523;;848:8;;-1:-1:-1;745:84:121;;-1:-1:-1;;;;366:523:121:o;894:154::-;-1:-1:-1;;;;;973:5:121;969:54;962:5;959:65;949:93;;1038:1;1035;1028:12;949:93;894:154;:::o;1053:392::-;1141:8;1151:6;1205:3;1198:4;1190:6;1186:17;1182:27;1172:55;;1223:1;1220;1213:12;1172:55;-1:-1:-1;1246:20:121;;1289:18;1278:30;;1275:50;;;1321:1;1318;1311:12;1275:50;1358:4;1350:6;1346:17;1334:29;;1418:3;1411:4;1401:6;1398:1;1394:14;1386:6;1382:27;1378:38;1375:47;1372:67;;;1435:1;1432;1425:12;1450:917;1600:6;1608;1616;1624;1632;1685:3;1673:9;1664:7;1660:23;1656:33;1653:53;;;1702:1;1699;1692:12;1653:53;1741:9;1728:23;1760:31;1785:5;1760:31;:::i;:::-;1810:5;-1:-1:-1;1867:2:121;1852:18;;1839:32;1880:33;1839:32;1880:33;:::i;:::-;1932:7;-1:-1:-1;1991:2:121;1976:18;;1963:32;2004:33;1963:32;2004:33;:::i;:::-;2056:7;-1:-1:-1;2114:2:121;2099:18;;2086:32;2141:18;2130:30;;2127:50;;;2173:1;2170;2163:12;2127:50;2212:95;2299:7;2290:6;2279:9;2275:22;2212:95;:::i;:::-;1450:917;;;;-1:-1:-1;1450:917:121;;-1:-1:-1;2326:8:121;;2186:121;1450:917;-1:-1:-1;;;1450:917:121:o;2372:685::-;2460:6;2468;2476;2484;2537:2;2525:9;2516:7;2512:23;2508:32;2505:52;;;2553:1;2550;2543:12;2505:52;2592:9;2579:23;2611:31;2636:5;2611:31;:::i;:::-;2661:5;-1:-1:-1;2718:2:121;2703:18;;2690:32;2731:33;2690:32;2731:33;:::i;:::-;2783:7;-1:-1:-1;2841:2:121;2826:18;;2813:32;2868:18;2857:30;;2854:50;;;2900:1;2897;2890:12;2854:50;2939:58;2989:7;2980:6;2969:9;2965:22;2939:58;:::i;:::-;2372:685;;;;-1:-1:-1;3016:8:121;-1:-1:-1;;;;2372:685:121:o;3244:508::-;3321:6;3329;3337;3390:2;3378:9;3369:7;3365:23;3361:32;3358:52;;;3406:1;3403;3396:12;3358:52;3445:9;3432:23;3464:31;3489:5;3464:31;:::i;:::-;3514:5;-1:-1:-1;3571:2:121;3556:18;;3543:32;3584:33;3543:32;3584:33;:::i;:::-;3244:508;;3636:7;;-1:-1:-1;;;3716:2:121;3701:18;;;;3688:32;;3244:508::o;3757:388::-;3825:6;3833;3886:2;3874:9;3865:7;3861:23;3857:32;3854:52;;;3902:1;3899;3892:12;3854:52;3941:9;3928:23;3960:31;3985:5;3960:31;:::i;:::-;4010:5;-1:-1:-1;4067:2:121;4052:18;;4039:32;4080:33;4039:32;4080:33;:::i;:::-;4132:7;4122:17;;;3757:388;;;;;:::o;4666:629::-;4752:6;4760;4768;4776;4829:3;4817:9;4808:7;4804:23;4800:33;4797:53;;;4846:1;4843;4836:12;4797:53;4885:9;4872:23;4904:31;4929:5;4904:31;:::i;:::-;4954:5;-1:-1:-1;5011:2:121;4996:18;;4983:32;5024:33;4983:32;5024:33;:::i;:::-;4666:629;;5076:7;;-1:-1:-1;;;;5156:2:121;5141:18;;5128:32;;5259:2;5244:18;5231:32;;4666:629::o;5300:529::-;5377:6;5385;5393;5446:2;5434:9;5425:7;5421:23;5417:32;5414:52;;;5462:1;5459;5452:12;5414:52;5501:9;5488:23;5520:31;5545:5;5520:31;:::i;:::-;5570:5;-1:-1:-1;5627:2:121;5612:18;;5599:32;5640:33;5599:32;5640:33;:::i;:::-;5692:7;-1:-1:-1;5751:2:121;5736:18;;5723:32;5764:33;5723:32;5764:33;:::i;:::-;5816:7;5806:17;;;5300:529;;;;;:::o;5834:247::-;5893:6;5946:2;5934:9;5925:7;5921:23;5917:32;5914:52;;;5962:1;5959;5952:12;5914:52;6001:9;5988:23;6020:31;6045:5;6020:31;:::i;:::-;6070:5;5834:247;-1:-1:-1;;;5834:247:121:o;6608:264::-;6804:3;6789:19;;6817:49;6793:9;6848:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;6877:118;6963:5;6956:13;6949:21;6942:5;6939:32;6929:60;;6985:1;6982;6975:12;7000:382;7065:6;7073;7126:2;7114:9;7105:7;7101:23;7097:32;7094:52;;;7142:1;7139;7132:12;7094:52;7181:9;7168:23;7200:31;7225:5;7200:31;:::i;:::-;7250:5;-1:-1:-1;7307:2:121;7292:18;;7279:32;7320:30;7279:32;7320:30;:::i;7387:927::-;7493:6;7501;7509;7517;7525;7533;7586:3;7574:9;7565:7;7561:23;7557:33;7554:53;;;7603:1;7600;7593:12;7554:53;7642:9;7629:23;7661:31;7686:5;7661:31;:::i;:::-;7711:5;-1:-1:-1;7768:2:121;7753:18;;7740:32;7781:33;7740:32;7781:33;:::i;:::-;7833:7;-1:-1:-1;7913:2:121;7898:18;;7885:32;;-1:-1:-1;8016:2:121;8001:18;;7988:32;;-1:-1:-1;8097:3:121;8082:19;;8069:33;8125:18;8114:30;;8111:50;;;8157:1;8154;8147:12;8111:50;8196:58;8246:7;8237:6;8226:9;8222:22;8196:58;:::i;:::-;7387:927;;;;-1:-1:-1;7387:927:121;;-1:-1:-1;7387:927:121;;8273:8;;7387:927;-1:-1:-1;;;7387:927:121:o;8319:611::-;8509:2;8521:21;;;8591:13;;8494:18;;;8613:22;;;8461:4;;8692:15;;;8666:2;8651:18;;;8461:4;8735:169;8749:6;8746:1;8743:13;8735:169;;;8810:13;;8798:26;;8853:2;8879:15;;;;8844:12;;;;8771:1;8764:9;8735:169;;;-1:-1:-1;8921:3:121;;8319:611;-1:-1:-1;;;;;8319:611:121:o;8935:1069::-;9050:6;9058;9066;9074;9082;9090;9098;9151:3;9139:9;9130:7;9126:23;9122:33;9119:53;;;9168:1;9165;9158:12;9119:53;9207:9;9194:23;9226:31;9251:5;9226:31;:::i;:::-;9276:5;-1:-1:-1;9333:2:121;9318:18;;9305:32;9346:33;9305:32;9346:33;:::i;:::-;9398:7;-1:-1:-1;9457:2:121;9442:18;;9429:32;9470:33;9429:32;9470:33;:::i;:::-;9522:7;-1:-1:-1;9602:2:121;9587:18;;9574:32;;-1:-1:-1;9705:3:121;9690:19;;9677:33;;-1:-1:-1;9787:3:121;9772:19;;9759:33;9815:18;9804:30;;9801:50;;;9847:1;9844;9837:12;9801:50;9886:58;9936:7;9927:6;9916:9;9912:22;9886:58;:::i;:::-;8935:1069;;;;-1:-1:-1;8935:1069:121;;-1:-1:-1;8935:1069:121;;;;9860:84;;-1:-1:-1;;;8935:1069:121:o;10009:1321::-;10188:6;10196;10204;10212;10220;10228;10236;10244;10297:3;10285:9;10276:7;10272:23;10268:33;10265:53;;;10314:1;10311;10304:12;10265:53;10353:9;10340:23;10372:31;10397:5;10372:31;:::i;:::-;10422:5;-1:-1:-1;10479:2:121;10464:18;;10451:32;10492:33;10451:32;10492:33;:::i;:::-;10544:7;-1:-1:-1;10624:2:121;10609:18;;10596:32;;-1:-1:-1;10727:2:121;10712:18;;10699:32;;-1:-1:-1;10808:3:121;10793:19;;10780:33;10836:18;10825:30;;10822:50;;;10868:1;10865;10858:12;10822:50;10907:58;10957:7;10948:6;10937:9;10933:22;10907:58;:::i;:::-;10984:8;;-1:-1:-1;10881:84:121;-1:-1:-1;;11072:3:121;11057:19;;11044:33;11102:18;11089:32;;11086:52;;;11134:1;11131;11124:12;11086:52;11173:97;11262:7;11251:8;11240:9;11236:24;11173:97;:::i;:::-;10009:1321;;;;-1:-1:-1;10009:1321:121;;-1:-1:-1;10009:1321:121;;;;;;11289:8;-1:-1:-1;;;10009:1321:121:o;11335:184::-;11387:77;11384:1;11377:88;11484:4;11481:1;11474:15;11508:4;11505:1;11498:15;11524:334;11595:2;11589:9;11651:2;11641:13;;-1:-1:-1;;11637:86:121;11625:99;;11754:18;11739:34;;11775:22;;;11736:62;11733:88;;;11801:18;;:::i;:::-;11837:2;11830:22;11524:334;;-1:-1:-1;11524:334:121:o;11863:617::-;11905:5;11958:3;11951:4;11943:6;11939:17;11935:27;11925:55;;11976:1;11973;11966:12;11925:55;12016:6;12003:20;12046:18;12038:6;12035:30;12032:56;;;12068:18;;:::i;:::-;12112:118;12224:4;-1:-1:-1;;12148:4:121;12140:6;12136:17;12132:90;12128:101;12112:118;:::i;:::-;12255:6;12246:7;12239:23;12309:3;12302:4;12293:6;12285;12281:19;12277:30;12274:39;12271:59;;;12326:1;12323;12316:12;12271:59;12391:6;12384:4;12376:6;12372:17;12365:4;12356:7;12352:18;12339:59;12447:1;12418:20;;;12440:4;12414:31;12407:42;;;;12422:7;11863:617;-1:-1:-1;;;11863:617:121:o;12485:1169::-;12636:6;12644;12652;12660;12668;12676;12684;12737:3;12725:9;12716:7;12712:23;12708:33;12705:53;;;12754:1;12751;12744:12;12705:53;12793:9;12780:23;12812:31;12837:5;12812:31;:::i;:::-;12862:5;-1:-1:-1;12919:2:121;12904:18;;12891:32;12932:33;12891:32;12932:33;:::i;:::-;12984:7;-1:-1:-1;13043:2:121;13028:18;;13015:32;13056:33;13015:32;13056:33;:::i;:::-;13108:7;-1:-1:-1;13167:2:121;13152:18;;13139:32;13180:33;13139:32;13180:33;:::i;:::-;13232:7;-1:-1:-1;13312:3:121;13297:19;;13284:33;;-1:-1:-1;13394:3:121;13379:19;;13366:33;13422:18;13411:30;;13408:50;;;13454:1;13451;13444:12;13408:50;13477:49;13518:7;13509:6;13498:9;13494:22;13477:49;:::i;:::-;13467:59;;;13578:3;13567:9;13563:19;13550:33;13592:30;13614:7;13592:30;:::i;:::-;13641:7;13631:17;;;12485:1169;;;;;;;;;;:::o;14263:1033::-;14408:6;14416;14424;14432;14440;14448;14501:3;14489:9;14480:7;14476:23;14472:33;14469:53;;;14518:1;14515;14508:12;14469:53;14557:9;14544:23;14576:31;14601:5;14576:31;:::i;:::-;14626:5;-1:-1:-1;14683:2:121;14668:18;;14655:32;14696:33;14655:32;14696:33;:::i;:::-;14748:7;-1:-1:-1;14807:2:121;14792:18;;14779:32;14820:33;14779:32;14820:33;:::i;:::-;14872:7;-1:-1:-1;14952:2:121;14937:18;;14924:32;;-1:-1:-1;15033:3:121;15018:19;;15005:33;15061:18;15050:30;;15047:50;;;15093:1;15090;15083:12;15047:50;15116:49;15157:7;15148:6;15137:9;15133:22;15116:49;:::i;:::-;15106:59;;;15217:3;15206:9;15202:19;15189:33;15231;15256:7;15231:33;:::i;:::-;15283:7;15273:17;;;14263:1033;;;;;;;;:::o;15301:184::-;15353:77;15350:1;15343:88;15450:4;15447:1;15440:15;15474:4;15471:1;15464:15;15490:125;15555:9;;;15576:10;;;15573:36;;;15589:18;;:::i;15620:658::-;15936:3;15921:19;;15949:49;15925:9;15980:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;15949:49;16029:3;16014:19;;16007:35;;;;16073:3;16058:19;;16051:35;;;;-1:-1:-1;;;;;16123:55:121;;;16117:3;16102:19;;16095:84;16216:55;16210:3;16195:19;;;16188:84;15620:658;;-1:-1:-1;15620:658:121:o;16283:343::-;16362:6;16370;16423:2;16411:9;16402:7;16398:23;16394:32;16391:52;;;16439:1;16436;16429:12;16391:52;-1:-1:-1;;16484:16:121;;16590:2;16575:18;;;16569:25;16484:16;;16569:25;;-1:-1:-1;16283:343:121:o;16862:251::-;16932:6;16985:2;16973:9;16964:7;16960:23;16956:32;16953:52;;;17001:1;16998;16991:12;16953:52;17033:9;17027:16;17052:31;17077:5;17052:31;:::i;17444:146::-;17530:34;17523:5;17519:46;17512:5;17509:57;17499:85;;17580:1;17577;17570:12;17595:811;17692:6;17752:2;17740:9;17731:7;17727:23;17723:32;17767:2;17764:22;;;17782:1;17779;17772:12;17764:22;-1:-1:-1;17851:2:121;17845:9;17893:2;17881:15;;17926:18;17911:34;;17947:22;;;17908:62;17905:88;;;17973:18;;:::i;:::-;18009:2;18002:22;18065:16;;18090:21;;18156:2;18141:18;;18135:25;18169:33;18135:25;18169:33;:::i;:::-;18230:2;18218:15;;18211:32;18288:2;18273:18;;18267:25;18301:33;18267:25;18301:33;:::i;:::-;18362:2;18350:15;;18343:32;18354:6;17595:811;-1:-1:-1;;;17595:811:121:o;18617:1232::-;18712:6;18772:3;18760:9;18751:7;18747:23;18743:33;18788:2;18785:22;;;18803:1;18800;18793:12;18785:22;-1:-1:-1;18872:2:121;18866:9;18914:3;18902:16;;18948:18;18933:34;;18969:22;;;18930:62;18927:88;;;18995:18;;:::i;:::-;19031:2;19024:22;19068:16;;19093:31;19068:16;19093:31;:::i;:::-;19133:21;;19199:2;19184:18;;19178:25;19212:33;19178:25;19212:33;:::i;:::-;19273:2;19261:15;;19254:32;19331:2;19316:18;;19310:25;19344:33;19310:25;19344:33;:::i;:::-;19405:2;19393:15;;19386:32;19463:2;19448:18;;19442:25;19476:33;19442:25;19476:33;:::i;:::-;19537:2;19525:15;;19518:32;19595:3;19580:19;;19574:26;19609:33;19574:26;19609:33;:::i;:::-;19670:3;19658:16;;19651:33;19729:3;19714:19;;19708:26;19743:33;19708:26;19743:33;:::i;:::-;19804:3;19792:16;;19785:33;19796:6;18617:1232;-1:-1:-1;;;18617:1232:121:o;19854:168::-;19927:9;;;19958;;19975:15;;;19969:22;;19955:37;19945:71;;19996:18;;:::i;20027:274::-;20067:1;20093;20083:189;;20128:77;20125:1;20118:88;20229:4;20226:1;20219:15;20257:4;20254:1;20247:15;20083:189;-1:-1:-1;20286:9:121;;20027:274::o;20306:230::-;20376:6;20429:2;20417:9;20408:7;20404:23;20400:32;20397:52;;;20445:1;20442;20435:12;20397:52;-1:-1:-1;20490:16:121;;20306:230;-1:-1:-1;20306:230:121:o;20541:737::-;20643:6;20703:2;20691:9;20682:7;20678:23;20674:32;20718:2;20715:22;;;20733:1;20730;20723:12;20715:22;-1:-1:-1;20802:2:121;20796:9;;;20832:15;;20877:18;20862:34;;20898:22;;;20859:62;20856:88;;;20924:18;;:::i;:::-;20960:2;20953:22;20997:16;;21022:31;20997:16;21022:31;:::i;:::-;21062:21;;21128:2;21113:18;;21107:25;21176:10;21163:24;;21151:37;;21141:65;;21202:1;21199;21192:12;21141:65;21234:2;21222:15;;21215:32;21226:6;20541:737;-1:-1:-1;;;20541:737:121:o;22199:245::-;22266:6;22319:2;22307:9;22298:7;22294:23;22290:32;22287:52;;;22335:1;22332;22325:12;22287:52;22367:9;22361:16;22386:28;22408:5;22386:28;:::i;22449:456::-;22537:6;22545;22553;22606:2;22594:9;22585:7;22581:23;22577:32;22574:52;;;22622:1;22619;22612:12;22574:52;-1:-1:-1;;22667:16:121;;22773:2;22758:18;;22752:25;22869:2;22854:18;;;22848:25;22667:16;;22752:25;;-1:-1:-1;22848:25:121;22449:456;-1:-1:-1;22449:456:121:o;23621:980::-;23716:6;23769:2;23757:9;23748:7;23744:23;23740:32;23737:52;;;23785:1;23782;23775:12;23737:52;23818:9;23812:16;23851:18;23843:6;23840:30;23837:50;;;23883:1;23880;23873:12;23837:50;23906:22;;23959:4;23951:13;;23947:27;-1:-1:-1;23937:55:121;;23988:1;23985;23978:12;23937:55;24021:2;24015:9;24047:18;24039:6;24036:30;24033:56;;;24069:18;;:::i;:::-;24115:6;24112:1;24108:14;24142:28;24166:2;24162;24158:11;24142:28;:::i;:::-;24204:19;;;24248:2;24278:11;;;24274:20;;;24239:12;;;;24306:19;;;24303:39;;;24338:1;24335;24328:12;24303:39;24370:2;24366;24362:11;24351:22;;24382:189;24398:6;24393:3;24390:15;24382:189;;;24488:10;;24511:18;;;24558:2;24415:12;;;;24488:10;;-1:-1:-1;24549:12:121;;;;24382:189;;;24590:5;23621:980;-1:-1:-1;;;;;;;23621:980:121:o;25028:347::-;25069:3;25107:5;25101:12;25134:6;25129:3;25122:19;25190:6;25183:4;25176:5;25172:16;25165:4;25160:3;25156:14;25150:47;25242:1;25235:4;25226:6;25221:3;25217:16;25213:27;25206:38;25364:4;-1:-1:-1;;25289:2:121;25281:6;25277:15;25273:88;25268:3;25264:98;25260:109;25253:116;;;25028:347;;;;:::o;25380:481::-;-1:-1:-1;;;;;25615:6:121;25611:55;25600:9;25593:74;25703:6;25698:2;25687:9;25683:18;25676:34;25746:6;25741:2;25730:9;25726:18;25719:34;25789:3;25784:2;25773:9;25769:18;25762:31;25574:4;25810:45;25850:3;25839:9;25835:19;25827:6;25810:45;:::i;25866:184::-;25918:77;25915:1;25908:88;26015:4;26012:1;26005:15;26039:4;26036:1;26029:15;26055:393;26158:4;26216:11;26203:25;26306:66;26295:8;26279:14;26275:29;26271:102;26251:18;26247:127;26237:155;;26388:1;26385;26378:12;26237:155;26409:33;;;;;26055:393;-1:-1:-1;;26055:393:121:o;26453:638::-;26577:4;26583:6;26643:11;26630:25;26733:66;26722:8;26706:14;26702:29;26698:102;26678:18;26674:127;26664:155;;26815:1;26812;26805:12;26664:155;26842:33;;26894:20;;;-1:-1:-1;26937:18:121;26926:30;;26923:50;;;26969:1;26966;26959:12;26923:50;27002:4;26990:17;;-1:-1:-1;27061:4:121;27049:17;;27033:14;27029:38;27019:49;;27016:69;;;27081:1;27078;27071:12;27096:1961;-1:-1:-1;;;;;27505:55:121;;27487:74;;27474:3;27592:2;27577:18;;27570:31;;;27459:19;;27636:22;;;27426:4;27716:6;27689:3;27674:19;;27426:4;27750:1212;27764:6;27761:1;27758:13;27750:1212;;;27839:6;27826:20;27859:31;27884:5;27859:31;:::i;:::-;-1:-1:-1;;;;;27915:54:121;27903:67;;28023:2;28011:15;;27998:29;28040:33;27998:29;28040:33;:::i;:::-;-1:-1:-1;;;;;28107:56:121;28102:2;28093:12;;28086:78;28217:4;28205:17;;28192:31;28236:33;28192:31;28236:33;:::i;:::-;-1:-1:-1;;;;;28305:56:121;28298:4;28289:14;;28282:80;28415:4;28403:17;;28390:31;28434:33;28390:31;28434:33;:::i;:::-;-1:-1:-1;;;;;28503:56:121;28496:4;28487:14;;28480:80;28638:4;28626:17;;;28613:31;28664:14;;;28657:31;28741:4;28729:17;;28716:31;28760:33;28716:31;28760:33;:::i;:::-;28842:34;28829:48;28822:4;28813:14;;28806:72;28907:4;28935:17;;;;28898:14;;;;27786:1;27779:9;27750:1212;;;-1:-1:-1;28979:3:121;-1:-1:-1;28991:60:121;;-1:-1:-1;29045:4:121;29030:20;;29022:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;28991:60;27096:1961;;;;;;;:::o;29414:723::-;-1:-1:-1;;;;;29705:6:121;29701:55;29690:9;29683:74;-1:-1:-1;;;;;29797:6:121;29793:55;29788:2;29777:9;29773:18;29766:83;-1:-1:-1;;;;;29889:6:121;29885:55;29880:2;29869:9;29865:18;29858:83;29977:6;29972:2;29961:9;29957:18;29950:34;30021:6;30015:3;30004:9;30000:19;29993:35;30065:3;30059;30048:9;30044:19;30037:32;29664:4;30086:45;30126:3;30115:9;30111:19;30103:6;30086:45;:::i;:::-;30078:53;29414:723;-1:-1:-1;;;;;;;;29414:723:121:o;30142:408::-;30345:6;30334:9;30327:25;-1:-1:-1;;;;;30392:6:121;30388:55;30383:2;30372:9;30368:18;30361:83;30480:2;30475;30464:9;30460:18;30453:30;30308:4;30500:44;30540:2;30529:9;30525:18;30517:6;30500:44;:::i;30555:325::-;30643:6;30638:3;30631:19;30695:6;30688:5;30681:4;30676:3;30672:14;30659:43;;30747:1;30740:4;30731:6;30726:3;30722:16;30718:27;30711:38;30613:3;30869:4;-1:-1:-1;;30794:2:121;30786:6;30782:15;30778:88;30773:3;30769:98;30765:109;30758:116;;30555:325;;;;:::o;30885:435::-;-1:-1:-1;;;;;31102:6:121;31098:55;31087:9;31080:74;31190:6;31185:2;31174:9;31170:18;31163:34;31233:2;31228;31217:9;31213:18;31206:30;31061:4;31253:61;31310:2;31299:9;31295:18;31287:6;31279;31253:61;:::i;31325:642::-;31628:49;31667:9;31659:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;31628:49;-1:-1:-1;;;;;31718:6:121;31714:55;31708:3;31697:9;31693:19;31686:84;31807:6;31801:3;31790:9;31786:19;31779:35;31851:6;31845:3;31834:9;31830:19;31823:35;31895:3;31889;31878:9;31874:19;31867:32;31609:4;31916:45;31956:3;31945:9;31941:19;31933:6;31916:45;:::i;31972:128::-;32039:9;;;32060:11;;;32057:37;;;32074:18;;:::i;32105:881::-;-1:-1:-1;;;;;32428:6:121;32424:55;32413:9;32406:74;-1:-1:-1;;;;;32520:6:121;32516:55;32511:2;32500:9;32496:18;32489:83;-1:-1:-1;;;;;32612:6:121;32608:55;32603:2;32592:9;32588:18;32581:83;-1:-1:-1;;;;;32704:6:121;32700:55;32695:2;32684:9;32680:18;32673:83;32793:6;32787:3;32776:9;32772:19;32765:35;32837:3;32831;32820:9;32816:19;32809:32;32387:4;32858:62;32915:3;32904:9;32900:19;32892:6;32884;32858:62;:::i;:::-;32850:70;;32971:6;32964:14;32957:22;32951:3;32940:9;32936:19;32929:51;32105:881;;;;;;;;;;;:::o;32991:642::-;33294:49;33333:9;33325:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;33294:49;33380:6;33374:3;33363:9;33359:19;33352:35;33424:6;33418:3;33407:9;33403:19;33396:35;-1:-1:-1;;;;;33472:6:121;33468:55;33462:3;33451:9;33447:19;33440:84;33561:3;33555;33544:9;33540:19;33533:32;33275:4;33582:45;33622:3;33611:9;33607:19;33599:6;33582:45;:::i;33638:578::-;33918:3;33903:19;;33931:49;33907:9;33962:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;33931:49;34011:3;33996:19;;33989:35;;;;-1:-1:-1;;;;;34061:55:121;;;34055:3;34040:19;;34033:84;34154:55;;34148:3;34133:19;;;34126:84;33638:578;;-1:-1:-1;33638:578:121:o;34531:637::-;34860:49;34899:9;34891:6;-1:-1:-1;;;;;6174:5:121;6168:12;6164:61;6159:3;6152:74;-1:-1:-1;;;;;6279:4:121;6272:5;6268:16;6262:23;6258:72;6251:4;6246:3;6242:14;6235:96;-1:-1:-1;;;;;6384:4:121;6377:5;6373:16;6367:23;6363:72;6356:4;6351:3;6347:14;6340:96;-1:-1:-1;;;;;6489:4:121;6482:5;6478:16;6472:23;6468:72;6461:4;6456:3;6452:14;6445:96;6590:4;6583:5;6579:16;6573:23;6566:4;6561:3;6557:14;6550:47;;;6086:517;34860:49;34940:3;34925:19;;34918:35;;;;-1:-1:-1;;;;;34990:55:121;34984:3;34969:19;;34962:84;35083:3;35077;35062:19;;35055:32;;;-1:-1:-1;35103:19:121;;;35096:30;35158:3;35143:19;;34531:637;-1:-1:-1;34531:637:121:o;35173:772::-;-1:-1:-1;;;;;35464:6:121;35460:55;35449:9;35442:74;-1:-1:-1;;;;;35556:6:121;35552:55;35547:2;35536:9;35532:18;35525:83;-1:-1:-1;;;;;35648:6:121;35644:55;35639:2;35628:9;35624:18;35617:83;35736:6;35731:2;35720:9;35716:18;35709:34;35780:3;35774;35763:9;35759:19;35752:32;35423:4;35801:45;35841:3;35830:9;35826:19;35818:6;35801:45;:::i;:::-;35793:53;;-1:-1:-1;;;;;35887:6:121;35883:55;35877:3;35866:9;35862:19;35855:84;35173:772;;;;;;;;;:::o;35950:408::-;-1:-1:-1;;;;;36157:6:121;36153:55;36142:9;36135:74;36245:6;36240:2;36229:9;36225:18;36218:34;36288:2;36283;36272:9;36268:18;36261:30;36116:4;36308:44;36348:2;36337:9;36333:18;36325:6;36308:44;:::i", "linkReferences": {}}, "methodIdentifiers": {"allocateAndEnterPosition(address,address,uint256,uint256,bytes,(address,uint256,((address,address,address,address,uint256),uint128)[])[])": "f7d6f6da", "allocateAndMigratePosition(address,address,address,(address,uint256,((address,address,address,address,uint256),uint128)[])[])": "1e164b63", "balanceOfCollateral(address,address)": "da3a855f", "claimRewards(address)": "ef5cfb8c", "enterPosition(address,address,uint256,uint256,bytes)": "de13c617", "exitPosition(address,address,address,uint256,uint256,bytes)": "ef9a0b61", "forceWithdraw(address,address,bytes)": "4a7f87e8", "healthFactor(address,address)": "576f5c40", "initializeMarket(address,address,uint256)": "4fb2a320", "initiateWithdraw(address,address,bytes)": "c0b5de76", "isApproved(address,address)": "a389783e", "liquidate(address,address,uint256,uint256)": "c1342574", "marketParams(address)": "da35e283", "migratePosition(address,address,address)": "c139cc98", "onMorphoFlashLoan(uint256,bytes)": "31f57072", "onMorphoLiquidate(uint256,bytes)": "cf7ea196", "onMorphoRepay(uint256,bytes)": "05b4591c", "setApproval(address,bool)": "db9b7170"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.29+commit.ab55807c\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"CannotEnterPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotExitPositionWithinCooldownPeriod\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"CannotForceWithdraw\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotLiquidateZeroShares\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assetsToRepay\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"assetsWithdrawn\",\"type\":\"uint256\"}],\"name\":\"InsufficientAssetsForRepayment\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientSharesHeld\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidLendingRouter\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LiquidatorHasPosition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoExistingPosition\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"NotAuthorized\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositAssetAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"depositData\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"feeAmount\",\"type\":\"uint256\"},{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"}],\"internalType\":\"struct Withdrawal[]\",\"name\":\"withdrawals\",\"type\":\"tuple[]\"}],\"internalType\":\"struct MorphoAllocation[]\",\"name\":\"allocationData\",\"type\":\"tuple[]\"}],\"name\":\"allocateAndEnterPosition\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"migrateFrom\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"feeAmount\",\"type\":\"uint256\"},{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"marketParams\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"}],\"internalType\":\"struct Withdrawal[]\",\"name\":\"withdrawals\",\"type\":\"tuple[]\"}],\"internalType\":\"struct MorphoAllocation[]\",\"name\":\"allocationData\",\"type\":\"tuple[]\"}],\"name\":\"allocateAndMigratePosition\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"balanceOfCollateral\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"collateralBalance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"claimRewards\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"rewards\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositAssetAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"depositData\",\"type\":\"bytes\"}],\"name\":\"enterPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToRedeem\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"assetToRepay\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"redeemData\",\"type\":\"bytes\"}],\"name\":\"exitPosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"forceWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"healthFactor\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"borrowed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"collateralValue\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxBorrow\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"name\":\"initializeMarket\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initiateWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApproved\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"liquidateAccount\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"debtToRepay\",\"type\":\"uint256\"}],\"name\":\"liquidate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"sharesToLiquidator\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"marketParams\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"loanToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"collateralToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"irm\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"lltv\",\"type\":\"uint256\"}],\"internalType\":\"struct MarketParams\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"migrateFrom\",\"type\":\"address\"}],\"name\":\"migratePosition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"onMorphoFlashLoan\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"repaidAssets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"onMorphoLiquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assetToRepay\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"onMorphoRepay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApproval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"balanceOfCollateral(address,address)\":{\"details\":\"Returns the balance of collateral of a user for a given vault.\",\"params\":{\"account\":\"The address of the account.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"collateralBalance\":\"The balance of collateral.\"}},\"claimRewards(address)\":{\"details\":\"Claims rewards for a user for a given vault.\",\"params\":{\"vault\":\"The address of the vault.\"},\"returns\":{\"rewards\":\"The rewards.\"}},\"enterPosition(address,address,uint256,uint256,bytes)\":{\"details\":\"Enters a position in the lending market.\",\"params\":{\"borrowAmount\":\"The amount of assets to borrow.\",\"depositAssetAmount\":\"The amount of margin to deposit.\",\"depositData\":\"The data to pass to the deposit function.\",\"onBehalf\":\"The address of the user to enter the position on behalf of.\",\"vault\":\"The address of the vault.\"}},\"exitPosition(address,address,address,uint256,uint256,bytes)\":{\"details\":\"Exits a position in the lending market. Can be called by the user or another lending router to migrate a position.\",\"params\":{\"assetToRepay\":\"The amount of assets to repay, if set to uint256.max the full debt will be repaid.\",\"onBehalf\":\"The address of the user to exit the position on behalf of.\",\"receiver\":\"The address of the receiver.\",\"redeemData\":\"Vault specific instructions for the exit.\",\"sharesToRedeem\":\"The amount of shares to redeem.\",\"vault\":\"The address of the vault.\"}},\"forceWithdraw(address,address,bytes)\":{\"details\":\"Forces a withdraw for a user for a given vault, only allowed if the health factor is negative.\",\"params\":{\"account\":\"The address of the account.\",\"data\":\"Vault specific instructions for the withdraw.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"requestId\":\"The request id.\"}},\"healthFactor(address,address)\":{\"details\":\"Returns the health factor of a user for a given vault.\",\"params\":{\"borrower\":\"The address of the borrower.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"borrowed\":\"The borrowed amount.\",\"collateralValue\":\"The collateral value.\",\"maxBorrow\":\"The max borrow amount.\"}},\"initiateWithdraw(address,address,bytes)\":{\"details\":\"Initiates a withdraw request for a user for a given vault.\",\"params\":{\"data\":\"Vault specific instructions for the withdraw.\",\"onBehalf\":\"The address of the user to initiate the withdraw on behalf of.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"requestId\":\"The request id.\"}},\"isApproved(address,address)\":{\"details\":\"Returns the authorization status of an address.\",\"params\":{\"operator\":\"The address to check the authorization status of.\",\"user\":\"The address to check the authorization status of.\"},\"returns\":{\"_0\":\"The authorization status.\"}},\"liquidate(address,address,uint256,uint256)\":{\"details\":\"Liquidates a position in the lending market.\",\"params\":{\"liquidateAccount\":\"The address of the account to liquidate.\",\"repaidShares\":\"The amount of shares to repay.\",\"seizedAssets\":\"The amount of assets to seize.\",\"vault\":\"The address of the vault.\"},\"returns\":{\"sharesToLiquidator\":\"The amount of shares to liquidator.\"}},\"migratePosition(address,address,address)\":{\"details\":\"Migrates a position to the lending market.\",\"params\":{\"migrateFrom\":\"The address of the lending router to migrate the position from.\",\"onBehalf\":\"The address of the user to migrate the position on behalf of.\",\"vault\":\"The address of the vault.\"}},\"onMorphoFlashLoan(uint256,bytes)\":{\"details\":\"The callback is called only if data is not empty.\",\"params\":{\"assets\":\"The amount of assets that was flash loaned.\",\"data\":\"Arbitrary data passed to the `flashLoan` function.\"}},\"onMorphoLiquidate(uint256,bytes)\":{\"details\":\"The callback is called only if data is not empty.\",\"params\":{\"data\":\"Arbitrary data passed to the `liquidate` function.\",\"repaidAssets\":\"The amount of repaid assets.\"}},\"setApproval(address,bool)\":{\"details\":\"Authorizes an address to manage a user's position.\",\"params\":{\"approved\":\"The authorization status.\",\"operator\":\"The address to authorize.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"onMorphoFlashLoan(uint256,bytes)\":{\"notice\":\"Callback called when a flash loan occurs.\"},\"onMorphoLiquidate(uint256,bytes)\":{\"notice\":\"Callback called when a liquidation occurs.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/routers/MorphoLendingRouter.sol\":\"MorphoLendingRouter\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":10000},\"remappings\":[\":@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/\",\":forge-std/=node_modules/forge-std/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100\",\"dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037\",\"dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d\",\"dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol\":{\"keccak256\":\"0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336\",\"dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c\",\"dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e\",\"dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol\":{\"keccak256\":\"0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e\",\"dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3\"]},\"node_modules/@openzeppelin/contracts/utils/TransientSlot.sol\":{\"keccak256\":\"0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de\",\"dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"src/AbstractYieldStrategy.sol\":{\"keccak256\":\"0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2\",\"dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP\"]},\"src/interfaces/AggregatorV2V3Interface.sol\":{\"keccak256\":\"0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd\",\"dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr\"]},\"src/interfaces/Errors.sol\":{\"keccak256\":\"0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d\",\"dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1\"]},\"src/interfaces/ILendingRouter.sol\":{\"keccak256\":\"0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3\",\"dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV\"]},\"src/interfaces/IRewardManager.sol\":{\"keccak256\":\"0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23\",\"dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys\"]},\"src/interfaces/ITradingModule.sol\":{\"keccak256\":\"0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69\",\"dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp\"]},\"src/interfaces/IWETH.sol\":{\"keccak256\":\"0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e\",\"dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR\"]},\"src/interfaces/IWithdrawRequestManager.sol\":{\"keccak256\":\"0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4\",\"dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j\"]},\"src/interfaces/IYieldStrategy.sol\":{\"keccak256\":\"0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e\",\"dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh\"]},\"src/interfaces/Morpho/IMorpho.sol\":{\"keccak256\":\"0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad\",\"dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY\"]},\"src/interfaces/Morpho/IMorphoCallbacks.sol\":{\"keccak256\":\"0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c\",\"dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H\"]},\"src/interfaces/Morpho/IOracle.sol\":{\"keccak256\":\"0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816\",\"dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223\"]},\"src/proxy/AddressRegistry.sol\":{\"keccak256\":\"0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e\",\"dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3\"]},\"src/proxy/Initializable.sol\":{\"keccak256\":\"0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf\",\"dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB\"]},\"src/rewards/RewardManagerMixin.sol\":{\"keccak256\":\"0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d\",\"dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez\"]},\"src/routers/AbstractLendingRouter.sol\":{\"keccak256\":\"0x76c82e77d6495512ba1a397c63c10bed272b0b7e4c1427b382e0f1e03caac6e0\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b2a5aef92cf9f8b4366747cd0ebb8c0e99439f1e9f0f3de466e5e2aad7161788\",\"dweb:/ipfs/QmR7tnF6L6YwB6UnU61NX3N7NZTY3GuKFyfGfUnGphjJ23\"]},\"src/routers/MorphoLendingRouter.sol\":{\"keccak256\":\"0xbbc17116830e10ccefbb483822f1213f6a5a4974fe5db1462affd12a9798de17\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b33ba907950b9bd634742029b62923de3ee765b9f2a2bfc8b575773152351828\",\"dweb:/ipfs/QmY9fAU3rF7gEnJ1ABGV6L4smbaRBNyL7o5KFHX1ozAZYM\"]},\"src/utils/Constants.sol\":{\"keccak256\":\"0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041\",\"dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA\"]},\"src/utils/TokenUtils.sol\":{\"keccak256\":\"0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829\",\"dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.29+commit.ab55807c"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "CannotEnterPosition"}, {"inputs": [], "type": "error", "name": "CannotExitPositionWithinCooldownPeriod"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "CannotForceWithdraw"}, {"inputs": [], "type": "error", "name": "CannotLiquidateZeroShares"}, {"inputs": [{"internalType": "uint256", "name": "assetsToRepay", "type": "uint256"}, {"internalType": "uint256", "name": "assetsWithdrawn", "type": "uint256"}], "type": "error", "name": "InsufficientAssetsForRepayment"}, {"inputs": [], "type": "error", "name": "InsufficientSharesHeld"}, {"inputs": [], "type": "error", "name": "InvalidLendingRouter"}, {"inputs": [], "type": "error", "name": "LiquidatorHasPosition"}, {"inputs": [], "type": "error", "name": "NoExistingPosition"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "type": "error", "name": "NotAuthorized"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "depositAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}, {"internalType": "bytes", "name": "depositData", "type": "bytes"}, {"internalType": "struct MorphoAllocation[]", "name": "allocationData", "type": "tuple[]", "components": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "feeAmount", "type": "uint256"}, {"internalType": "struct <PERSON><PERSON><PERSON>[]", "name": "withdrawals", "type": "tuple[]", "components": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint128", "name": "amount", "type": "uint128"}]}]}], "stateMutability": "payable", "type": "function", "name": "allocateAndEnterPosition"}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "migrateFrom", "type": "address"}, {"internalType": "struct MorphoAllocation[]", "name": "allocationData", "type": "tuple[]", "components": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "feeAmount", "type": "uint256"}, {"internalType": "struct <PERSON><PERSON><PERSON>[]", "name": "withdrawals", "type": "tuple[]", "components": [{"internalType": "struct MarketParams", "name": "marketParams", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}, {"internalType": "uint128", "name": "amount", "type": "uint128"}]}]}], "stateMutability": "payable", "type": "function", "name": "allocateAndMigratePosition"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOfCollateral", "outputs": [{"internalType": "uint256", "name": "collateralBalance", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimRewards", "outputs": [{"internalType": "uint256[]", "name": "rewards", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "depositAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "borrowAmount", "type": "uint256"}, {"internalType": "bytes", "name": "depositData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "enterPosition"}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "sharesToRedeem", "type": "uint256"}, {"internalType": "uint256", "name": "assetToRepay", "type": "uint256"}, {"internalType": "bytes", "name": "redeemData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "exitPosition"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "forceWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "healthFactor", "outputs": [{"internalType": "uint256", "name": "borrowed", "type": "uint256"}, {"internalType": "uint256", "name": "collateralValue", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "initializeMarket"}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initiateWithdraw", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "liquidateAccount", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "uint256", "name": "sharesToLiquidate", "type": "uint256"}, {"internalType": "uint256", "name": "debtToRepay", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate", "outputs": [{"internalType": "uint256", "name": "sharesToLiquidator", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "marketParams", "outputs": [{"internalType": "struct MarketParams", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "loanToken", "type": "address"}, {"internalType": "address", "name": "collateralToken", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "address", "name": "irm", "type": "address"}, {"internalType": "uint256", "name": "lltv", "type": "uint256"}]}]}, {"inputs": [{"internalType": "address", "name": "onBehalf", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "migrateFrom", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "migratePosition"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "onMorphoFlashLoan"}, {"inputs": [{"internalType": "uint256", "name": "repaidAssets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "onMorphoLiquidate"}, {"inputs": [{"internalType": "uint256", "name": "assetToRepay", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "onMorphoRepay"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "set<PERSON><PERSON><PERSON>al"}], "devdoc": {"kind": "dev", "methods": {"balanceOfCollateral(address,address)": {"details": "Returns the balance of collateral of a user for a given vault.", "params": {"account": "The address of the account.", "vault": "The address of the vault."}, "returns": {"collateralBalance": "The balance of collateral."}}, "claimRewards(address)": {"details": "Claims rewards for a user for a given vault.", "params": {"vault": "The address of the vault."}, "returns": {"rewards": "The rewards."}}, "enterPosition(address,address,uint256,uint256,bytes)": {"details": "Enters a position in the lending market.", "params": {"borrowAmount": "The amount of assets to borrow.", "depositAssetAmount": "The amount of margin to deposit.", "depositData": "The data to pass to the deposit function.", "onBehalf": "The address of the user to enter the position on behalf of.", "vault": "The address of the vault."}}, "exitPosition(address,address,address,uint256,uint256,bytes)": {"details": "Exits a position in the lending market. Can be called by the user or another lending router to migrate a position.", "params": {"assetToRepay": "The amount of assets to repay, if set to uint256.max the full debt will be repaid.", "onBehalf": "The address of the user to exit the position on behalf of.", "receiver": "The address of the receiver.", "redeemData": "Vault specific instructions for the exit.", "sharesToRedeem": "The amount of shares to redeem.", "vault": "The address of the vault."}}, "forceWithdraw(address,address,bytes)": {"details": "Forces a withdraw for a user for a given vault, only allowed if the health factor is negative.", "params": {"account": "The address of the account.", "data": "<PERSON><PERSON> specific instructions for the withdraw.", "vault": "The address of the vault."}, "returns": {"requestId": "The request id."}}, "healthFactor(address,address)": {"details": "Returns the health factor of a user for a given vault.", "params": {"borrower": "The address of the borrower.", "vault": "The address of the vault."}, "returns": {"borrowed": "The borrowed amount.", "collateralValue": "The collateral value.", "maxBorrow": "The max borrow amount."}}, "initiateWithdraw(address,address,bytes)": {"details": "Initiates a withdraw request for a user for a given vault.", "params": {"data": "<PERSON><PERSON> specific instructions for the withdraw.", "onBehalf": "The address of the user to initiate the withdraw on behalf of.", "vault": "The address of the vault."}, "returns": {"requestId": "The request id."}}, "isApproved(address,address)": {"details": "Returns the authorization status of an address.", "params": {"operator": "The address to check the authorization status of.", "user": "The address to check the authorization status of."}, "returns": {"_0": "The authorization status."}}, "liquidate(address,address,uint256,uint256)": {"details": "Liquidates a position in the lending market.", "params": {"liquidateAccount": "The address of the account to liquidate.", "repaidShares": "The amount of shares to repay.", "seizedAssets": "The amount of assets to seize.", "vault": "The address of the vault."}, "returns": {"sharesToLiquidator": "The amount of shares to liquidator."}}, "migratePosition(address,address,address)": {"details": "Migrates a position to the lending market.", "params": {"migrateFrom": "The address of the lending router to migrate the position from.", "onBehalf": "The address of the user to migrate the position on behalf of.", "vault": "The address of the vault."}}, "onMorphoFlashLoan(uint256,bytes)": {"details": "The callback is called only if data is not empty.", "params": {"assets": "The amount of assets that was flash loaned.", "data": "Arbitrary data passed to the `flashLoan` function."}}, "onMorphoLiquidate(uint256,bytes)": {"details": "The callback is called only if data is not empty.", "params": {"data": "Arbitrary data passed to the `liquidate` function.", "repaidAssets": "The amount of repaid assets."}}, "setApproval(address,bool)": {"details": "Authorizes an address to manage a user's position.", "params": {"approved": "The authorization status.", "operator": "The address to authorize."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"onMorphoFlashLoan(uint256,bytes)": {"notice": "Callback called when a flash loan occurs."}, "onMorphoLiquidate(uint256,bytes)": {"notice": "Callback called when a liquidation occurs."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/", "forge-std/=node_modules/forge-std/"], "optimizer": {"enabled": true, "runs": 10000}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/routers/MorphoLendingRouter.sol": "MorphoLendingR<PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"keccak256": "0xd5ea07362ab630a6a3dee4285a74cf2377044ca2e4be472755ad64d7c5d4b69d", "urls": ["bzz-raw://da5e832b40fc5c3145d3781e2e5fa60ac2052c9d08af7e300dc8ab80c4343100", "dweb:/ipfs/QmTzf7N5ZUdh5raqtzbM11yexiUoLC9z3Ws632MCuycq1d"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"keccak256": "0x0afcb7e740d1537b252cb2676f600465ce6938398569f09ba1b9ca240dde2dfc", "urls": ["bzz-raw://1c299900ac4ec268d4570ecef0d697a3013cd11a6eb74e295ee3fbc945056037", "dweb:/ipfs/Qmab9owJoxcA7vJT5XNayCMaUR1qxqj1NDzzisduwaJMcZ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"keccak256": "0x1a6221315ce0307746c2c4827c125d821ee796c74a676787762f4778671d4f44", "urls": ["bzz-raw://1bb2332a7ee26dd0b0de9b7fe266749f54820c99ab6a3bcb6f7e6b751d47ee2d", "dweb:/ipfs/QmcRWpaBeCYkhy68PR3B4AgD7asuQk7PwkWxrvJbZcikLF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC20Metadata.sol": {"keccak256": "0xd735962e3d6660884153ba8a972b5f100dde4c482f2ff1c525ba7fdefb154cbd", "urls": ["bzz-raw://5a264d17b093f585844b0d977e9f60555b8c8d6513b304fde863cdf652a0d336", "dweb:/ipfs/QmWXfaJisjVnrjTUjZGryZpMob9wKivvtbodLS3PTc1ttq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x86b7b71a6aedefdad89b607378eeab1dcc5389b9ea7d17346d08af01d7190994", "urls": ["bzz-raw://1dc2db8d94a21eac8efe03adf574c419b08536409b416057a2b5b95cb772c43c", "dweb:/ipfs/QmZfqJCKVU1ScuX2A7s8WZdQEaikwJbDH5JBrBdKTUT4Gu"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2", "urls": ["bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303", "dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xd6fa4088198f04eef10c5bce8a2f4d60554b7ec4b987f684393c01bf79b94d9f", "urls": ["bzz-raw://f95ee0bbd4dd3ac730d066ba3e785ded4565e890dbec2fa7d3b9fe3bad9d0d6e", "dweb:/ipfs/QmSLr6bHkPFWT7ntj34jmwfyskpwo97T9jZUrk5sz3sdtR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/ReentrancyGuardTransient.sol": {"keccak256": "0xe56ff5015046505f81f9d62671a784e933dd099db4c3a8fa8de598f20af2c5a3", "urls": ["bzz-raw://355863359b4a250f7016836ef9a9672578e898503896f70a0d42b80141586f3e", "dweb:/ipfs/QmXXzvoMSFNQf8nRbcyRap5qzcbekWuzbXDY5C8f68JiG3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/TransientSlot.sol": {"keccak256": "0xac673fa1e374d9e6107504af363333e3e5f6344d2e83faf57d9bfd41d77cc946", "urls": ["bzz-raw://5982478dbbb218e9dd5a6e83f5c0e8d1654ddf20178484b43ef21dd2246809de", "dweb:/ipfs/QmaB1hS68n2kG8vTbt7EPEzmrGhkUbfiFyykGGLsAr9X22"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "src/AbstractYieldStrategy.sol": {"keccak256": "0xdc21ff9c2705505b9b8e7dce444c903087565e1d7df82f9a24abe1b3bbe2fedb", "urls": ["bzz-raw://4ef8198a474bdcea0e8127d6f6f33bf08f7ed474118c01ff6198235212ab2ea2", "dweb:/ipfs/QmXZU5V8JsEKjSshpfvm58JuMvNbACDcSTmXCzLGNj9cKP"], "license": "BUSL-1.1"}, "src/interfaces/AggregatorV2V3Interface.sol": {"keccak256": "0x67fc9937e759a9d8572d797da6962f2b334fd5da0b09eb03e789d02cd0f19a08", "urls": ["bzz-raw://4605faf99ec4516d4009d4bbee45b968841c600976362b6f97a3ad55f6795ecd", "dweb:/ipfs/QmY4qpBM7S52sSsmm3ZcXKPa2z5fviFPgBQPMqzmD8u5rr"], "license": "MIT"}, "src/interfaces/Errors.sol": {"keccak256": "0xc43614ae290a6700d6187f303e2a3db3bf3a0958bd3b58ca9f661c05a94395b9", "urls": ["bzz-raw://c0f8e64ae8e51108dbe859ada760a2ff345169915e4ff0d5a591ab97f106d58d", "dweb:/ipfs/QmPD45Lz6d8Bqi7CDgkkNA6btS1Rj3RTqaUtaQMhvbMgd1"], "license": "BUSL-1.1"}, "src/interfaces/ILendingRouter.sol": {"keccak256": "0x5682155972be6ede251bc94f203062b7394cf360f324b4671fe67ad0310b8e66", "urls": ["bzz-raw://35dc181001b71801d680706d10c16c7ce3fdd7f85e2fe31a626f0a33b19ee4c3", "dweb:/ipfs/QmQf2A6KSWtz4qBaCFSNo1FftKNvMzEgPqZk1v71iRY2SV"], "license": "BUSL-1.1"}, "src/interfaces/IRewardManager.sol": {"keccak256": "0x3cc8cf0ae97a70bc42b4844dd5d7b58ae096a668a246db38008de098de2e8cfb", "urls": ["bzz-raw://7c96f6155621367cc69b5e9cf15ffebecb97b1e9072f08b1cae517ac8c2ddc23", "dweb:/ipfs/QmaCS4jwZyY1KS2QWEf9MEcGqYiqr47GGQZA8hB111T3ys"], "license": "BUSL-1.1"}, "src/interfaces/ITradingModule.sol": {"keccak256": "0x357a18c29b731ec65fcae0e671010f313f1100aa8fd4d3bf9d8a5379e7cba982", "urls": ["bzz-raw://f653ac1f0e24e45a44955ed565b36dc8b3358a5f5e95c7cf01bfc8c76ba08b69", "dweb:/ipfs/QmcUhncvDS7yShv5vzMQ5RJ9n5A1kSY1p3oyFqQmgFrewp"], "license": "MIT"}, "src/interfaces/IWETH.sol": {"keccak256": "0x5659dbc0c6a1f1f92e07ad5d5f8e6e564f719ea1434b6cead6ada19adeba8516", "urls": ["bzz-raw://cd0cce4ff224fec747b9e43ccc8a588425e1f3b62a2b2dd87aa497876efa2e1e", "dweb:/ipfs/QmSdjaHXvCFzdv6Cg85ynPNoPzgcrNM8JyappHg6TrUmCR"], "license": "BUSL-1.1"}, "src/interfaces/IWithdrawRequestManager.sol": {"keccak256": "0xd365cb30c23a9d0fb2eb378ac69149184aa9b0830270370e3e4f251f61aee704", "urls": ["bzz-raw://7f32c188e477768fa15e1a8ae03db323879ef485542b9c87313e5c248ed64fe4", "dweb:/ipfs/QmUEnQH5B2uyZxvs19fh9oJ73cfMRx6U1aYpJy65exzC5j"], "license": "BUSL-1.1"}, "src/interfaces/IYieldStrategy.sol": {"keccak256": "0x12ed1d6f271aca0e3871b63b29034b968d88b218f4044f3ec49cd09748788b7d", "urls": ["bzz-raw://93b62b7a8fcf81bcd615feb5cf5ad6dcec767316cc1833ec1232e2b59f51130e", "dweb:/ipfs/QmTd9P5RcFVgYKraGRgpM1S6UrNv8rkQUVDMetf2oFzeRh"], "license": "BUSL-1.1"}, "src/interfaces/Morpho/IMorpho.sol": {"keccak256": "0xaee7826b29bd6336aac7694a7def971f2652ea278e37b0910e512e40c746c4b6", "urls": ["bzz-raw://2b866f45960a54883e5c9ead5285e5232860b8253088f7e4d0fb6544e24331ad", "dweb:/ipfs/QmdDcqr5RLcVLu2oJ2PrBrLv7oRqTjbXombEK7QvVKRPJY"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IMorphoCallbacks.sol": {"keccak256": "0x6baa2f223dac77e9a6cc9f729951467332bf6fda9f3dcf92d6f70b86692b12bd", "urls": ["bzz-raw://b2e1cd9d9a4b4a63f95d35938c3fd2ab2a69797674d444c23441e0afa121d11c", "dweb:/ipfs/QmU4sqFX974a2YAsyYsCuomrC9r67aQxnh9pBnBKmmd68H"], "license": "GPL-2.0-or-later"}, "src/interfaces/Morpho/IOracle.sol": {"keccak256": "0xddca994a05092a5c0f49e24ca63109149de7a7d655f9e3710dc2558079765b35", "urls": ["bzz-raw://13975f1d2fad5b823b5b3a4a1e3e2d92dfad056cf62ecaa60fb18d7a65f5b816", "dweb:/ipfs/QmTDjRoMmC2Rt5JzkPbrwVf9vGKSLkDg9JtxbpeAkgw223"], "license": "GPL-2.0-or-later"}, "src/proxy/AddressRegistry.sol": {"keccak256": "0x149a49d754bb62a7c1ef1191b44fa4f3d20078a8d28223163f23ece21f47b2c4", "urls": ["bzz-raw://426a2ab62f2f90190858dccc53d879ffba5b628b692bbae0132e3d86042b4a8e", "dweb:/ipfs/QmT8rF2vUcMpEJNye9VWJks6eBUVnGtYRz9JedE8mc4uu3"], "license": "BUSL-1.1"}, "src/proxy/Initializable.sol": {"keccak256": "0x08681ddc378760c2373f74da7dfaf16318c93f75dc6198a8e13c907f0e43575d", "urls": ["bzz-raw://4a2f37f4227a23c5c2cfbe04406a7aa5104aa34d869922619028c67d161663cf", "dweb:/ipfs/QmZV47P3f5xqWxBocRj1JUFKcfD9915bq6by7KQyHW7GLB"], "license": "BUSL-1.1"}, "src/rewards/RewardManagerMixin.sol": {"keccak256": "0x93add14ba3ddfb6744c67bec72033c948fcd0d3315b16d35595ea989c7109dab", "urls": ["bzz-raw://c8095a3993318f7746e2527fc8780dc261d5fe904a680c88088e2de332b7c49d", "dweb:/ipfs/QmSSX1yZMumLrujhJ94X3ScW7Tq8VDcsXnvFVvJPvjceez"], "license": "BUSL-1.1"}, "src/routers/AbstractLendingRouter.sol": {"keccak256": "0x76c82e77d6495512ba1a397c63c10bed272b0b7e4c1427b382e0f1e03caac6e0", "urls": ["bzz-raw://b2a5aef92cf9f8b4366747cd0ebb8c0e99439f1e9f0f3de466e5e2aad7161788", "dweb:/ipfs/QmR7tnF6L6YwB6UnU61NX3N7NZTY3GuKFyfGfUnGphjJ23"], "license": "BUSL-1.1"}, "src/routers/MorphoLendingRouter.sol": {"keccak256": "0xbbc17116830e10ccefbb483822f1213f6a5a4974fe5db1462affd12a9798de17", "urls": ["bzz-raw://b33ba907950b9bd634742029b62923de3ee765b9f2a2bfc8b575773152351828", "dweb:/ipfs/QmY9fAU3rF7gEnJ1ABGV6L4smbaRBNyL7o5KFHX1ozAZYM"], "license": "BUSL-1.1"}, "src/utils/Constants.sol": {"keccak256": "0xb696fdeb73d58fbb5732d91b277419c91e32b51daf04c3a46d8728a463e92670", "urls": ["bzz-raw://b42fe5d7c79e7c74e05cc41269e2805a0dce0dea7a161436c3d3b7105b478041", "dweb:/ipfs/QmQRyDgaV9xj79priCBYav1nKZF7gAs7znewrzvTVxbAKA"], "license": "BUSL-1.1"}, "src/utils/TokenUtils.sol": {"keccak256": "0x25368ec9bb126997cb536f7724c2a9d01e3389efe4d04f8d95db2e6ff6c5c66f", "urls": ["bzz-raw://a7582eb78a7cc15a62191419d9f47dd44da40ce1225fb7794bdee6ad43453829", "dweb:/ipfs/QmX9dUa1xhAEGj82rJTauendA9VBnvjaRTp5Y13sYeVmKS"], "license": "BUSL-1.1"}}, "version": 1}, "id": 89}