{"_format": "hh-sol-artifact-1", "contractName": "ERC6909", "sourceName": "contracts/token/ERC6909/draft-ERC6909.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC6909InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC6909InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC6909InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC6909InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "OperatorSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}