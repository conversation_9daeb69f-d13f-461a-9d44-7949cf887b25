{"_format": "hh-sol-artifact-1", "contractName": "ERC6909ContentURI", "sourceName": "contracts/token/ERC6909/extensions/draft-ERC6909ContentURI.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ERC6909InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC6909InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC6909InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC6909InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC6909InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [], "name": "ContractURIUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "OperatorSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "value", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "URI", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "contractURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}