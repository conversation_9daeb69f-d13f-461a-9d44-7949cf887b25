{"_format": "hh-sol-artifact-1", "contractName": "IERC7913SignatureVerifier", "sourceName": "contracts/interfaces/IERC7913.sol", "abi": [{"inputs": [{"internalType": "bytes", "name": "key", "type": "bytes"}, {"internalType": "bytes32", "name": "hash", "type": "bytes32"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "verify", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}