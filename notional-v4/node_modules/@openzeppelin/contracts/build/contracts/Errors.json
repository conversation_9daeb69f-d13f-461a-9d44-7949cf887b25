{"_format": "hh-sol-artifact-1", "contractName": "Errors", "sourceName": "contracts/utils/Errors.sol", "abi": [{"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "FailedDeployment", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "MissingPrecompile", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212203674537990f67e9f6c04733daf526fbb00a8b67fab7890e388d422b34bcefe7e64736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea26469706673582212203674537990f67e9f6c04733daf526fbb00a8b67fab7890e388d422b34bcefe7e64736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}