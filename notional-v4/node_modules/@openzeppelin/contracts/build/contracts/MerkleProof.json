{"_format": "hh-sol-artifact-1", "contractName": "MerkleProof", "sourceName": "contracts/utils/cryptography/MerkleProof.sol", "abi": [{"inputs": [], "name": "MerkleProofInvalidMultiproof", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220d1ce122d98b28b2410080eca30b264cbc114fab9c6cbad1353fd110c499357e464736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220d1ce122d98b28b2410080eca30b264cbc114fab9c6cbad1353fd110c499357e464736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}