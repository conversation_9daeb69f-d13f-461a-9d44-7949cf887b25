{"_format": "hh-sol-artifact-1", "contractName": "EnumerableMap", "sourceName": "contracts/utils/structs/EnumerableMap.sol", "abi": [{"inputs": [{"internalType": "bytes", "name": "key", "type": "bytes"}], "name": "EnumerableMapNonexistentBytesKey", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}], "name": "EnumerableMapNonexistentKey", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220b53ceded6a25e67b8a78704b61dbe0db2708e1c58ff3b6b36fb019a115fce18264736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220b53ceded6a25e67b8a78704b61dbe0db2708e1c58ff3b6b36fb019a115fce18264736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}