var Fu=Object.create;var pt=Object.defineProperty;var pu=Object.getOwnPropertyDescriptor;var du=Object.getOwnPropertyNames;var mu=Object.getPrototypeOf,Eu=Object.prototype.hasOwnProperty;var er=e=>{throw TypeError(e)};var Cu=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),dt=(e,t)=>{for(var r in t)pt(e,r,{get:t[r],enumerable:!0})},hu=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let u of du(t))!Eu.call(e,u)&&u!==r&&pt(e,u,{get:()=>t[u],enumerable:!(n=pu(t,u))||n.enumerable});return e};var gu=(e,t,r)=>(r=e!=null?Fu(mu(e)):{},hu(t||!e||!e.__esModule?pt(r,"default",{value:e,enumerable:!0}):r,e));var yu=(e,t,r)=>t.has(e)||er("Cannot "+r);var tr=(e,t,r)=>t.has(e)?er("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r);var fe=(e,t,r)=>(yu(e,t,"access private method"),r);var Pn=Cu(Mt=>{"use strict";Object.defineProperty(Mt,"__esModule",{value:!0});function Co(){return new Proxy({},{get:()=>e=>e})}var On=/\r\n|[\n\r\u2028\u2029]/;function ho(e,t,r){let n=Object.assign({column:0,line:-1},e.start),u=Object.assign({},n,e.end),{linesAbove:o=2,linesBelow:i=3}=r||{},s=n.line,a=n.column,c=u.line,D=u.column,p=Math.max(s-(o+1),0),l=Math.min(t.length,c+i);s===-1&&(p=0),c===-1&&(l=t.length);let F=c-s,f={};if(F)for(let d=0;d<=F;d++){let m=d+s;if(!a)f[m]=!0;else if(d===0){let C=t[m-1].length;f[m]=[a,C-a+1]}else if(d===F)f[m]=[0,D];else{let C=t[m-d].length;f[m]=[0,C]}}else a===D?a?f[s]=[a,0]:f[s]=!0:f[s]=[a,D-a];return{start:p,end:l,markerLines:f}}function go(e,t,r={}){let u=Co(!1),o=e.split(On),{start:i,end:s,markerLines:a}=ho(t,o,r),c=t.start&&typeof t.start.column=="number",D=String(s).length,l=e.split(On,s).slice(i,s).map((F,f)=>{let d=i+1+f,C=` ${` ${d}`.slice(-D)} |`,E=a[d],h=!a[d+1];if(E){let x="";if(Array.isArray(E)){let A=F.slice(0,Math.max(E[0]-1,0)).replace(/[^\t]/g," "),$=E[1]||1;x=[`
 `,u.gutter(C.replace(/\d/g," "))," ",A,u.marker("^").repeat($)].join(""),h&&r.message&&(x+=" "+u.message(r.message))}return[u.marker(">"),u.gutter(C),F.length>0?` ${F}`:"",x].join("")}else return` ${u.gutter(C)}${F.length>0?` ${F}`:""}`}).join(`
`);return r.message&&!c&&(l=`${" ".repeat(D+1)}${r.message}
${l}`),l}Mt.codeFrameColumns=go});var Zt={};dt(Zt,{__debug:()=>ui,check:()=>ri,doc:()=>qt,format:()=>fu,formatWithCursor:()=>cu,getSupportInfo:()=>ni,util:()=>Qt,version:()=>tu});var Au=(e,t,r,n)=>{if(!(e&&t==null))return t.replaceAll?t.replaceAll(r,n):r.global?t.replace(r,n):t.split(r).join(n)},te=Au;var _e=class{diff(t,r,n={}){let u;typeof n=="function"?(u=n,n={}):"callback"in n&&(u=n.callback);let o=this.castInput(t,n),i=this.castInput(r,n),s=this.removeEmpty(this.tokenize(o,n)),a=this.removeEmpty(this.tokenize(i,n));return this.diffWithOptionsObj(s,a,n,u)}diffWithOptionsObj(t,r,n,u){var o;let i=E=>{if(E=this.postProcess(E,n),u){setTimeout(function(){u(E)},0);return}else return E},s=r.length,a=t.length,c=1,D=s+a;n.maxEditLength!=null&&(D=Math.min(D,n.maxEditLength));let p=(o=n.timeout)!==null&&o!==void 0?o:1/0,l=Date.now()+p,F=[{oldPos:-1,lastComponent:void 0}],f=this.extractCommon(F[0],r,t,0,n);if(F[0].oldPos+1>=a&&f+1>=s)return i(this.buildValues(F[0].lastComponent,r,t));let d=-1/0,m=1/0,C=()=>{for(let E=Math.max(d,-c);E<=Math.min(m,c);E+=2){let h,x=F[E-1],A=F[E+1];x&&(F[E-1]=void 0);let $=!1;if(A){let Be=A.oldPos-E;$=A&&0<=Be&&Be<s}let ue=x&&x.oldPos+1<a;if(!$&&!ue){F[E]=void 0;continue}if(!ue||$&&x.oldPos<A.oldPos?h=this.addToPath(A,!0,!1,0,n):h=this.addToPath(x,!1,!0,1,n),f=this.extractCommon(h,r,t,E,n),h.oldPos+1>=a&&f+1>=s)return i(this.buildValues(h.lastComponent,r,t))||!0;F[E]=h,h.oldPos+1>=a&&(m=Math.min(m,E-1)),f+1>=s&&(d=Math.max(d,E+1))}c++};if(u)(function E(){setTimeout(function(){if(c>D||Date.now()>l)return u(void 0);C()||E()},0)})();else for(;c<=D&&Date.now()<=l;){let E=C();if(E)return E}}addToPath(t,r,n,u,o){let i=t.lastComponent;return i&&!o.oneChangePerToken&&i.added===r&&i.removed===n?{oldPos:t.oldPos+u,lastComponent:{count:i.count+1,added:r,removed:n,previousComponent:i.previousComponent}}:{oldPos:t.oldPos+u,lastComponent:{count:1,added:r,removed:n,previousComponent:i}}}extractCommon(t,r,n,u,o){let i=r.length,s=n.length,a=t.oldPos,c=a-u,D=0;for(;c+1<i&&a+1<s&&this.equals(n[a+1],r[c+1],o);)c++,a++,D++,o.oneChangePerToken&&(t.lastComponent={count:1,previousComponent:t.lastComponent,added:!1,removed:!1});return D&&!o.oneChangePerToken&&(t.lastComponent={count:D,previousComponent:t.lastComponent,added:!1,removed:!1}),t.oldPos=a,c}equals(t,r,n){return n.comparator?n.comparator(t,r):t===r||!!n.ignoreCase&&t.toLowerCase()===r.toLowerCase()}removeEmpty(t){let r=[];for(let n=0;n<t.length;n++)t[n]&&r.push(t[n]);return r}castInput(t,r){return t}tokenize(t,r){return Array.from(t)}join(t){return t.join("")}postProcess(t,r){return t}get useLongestToken(){return!1}buildValues(t,r,n){let u=[],o;for(;t;)u.push(t),o=t.previousComponent,delete t.previousComponent,t=o;u.reverse();let i=u.length,s=0,a=0,c=0;for(;s<i;s++){let D=u[s];if(D.removed)D.value=this.join(n.slice(c,c+D.count)),c+=D.count;else{if(!D.added&&this.useLongestToken){let p=r.slice(a,a+D.count);p=p.map(function(l,F){let f=n[c+F];return f.length>l.length?f:l}),D.value=this.join(p)}else D.value=this.join(r.slice(a,a+D.count));a+=D.count,D.added||(c+=D.count)}}return u}};var mt=class extends _e{tokenize(t){return t.slice()}join(t){return t}removeEmpty(t){return t}},rr=new mt;function Et(e,t,r){return rr.diff(e,t,r)}function nr(e){let t=e.indexOf("\r");return t!==-1?e.charAt(t+1)===`
`?"crlf":"cr":"lf"}function xe(e){switch(e){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function Ct(e,t){let r;switch(t){case`
`:r=/\n/gu;break;case"\r":r=/\r/gu;break;case`\r
`:r=/\r\n/gu;break;default:throw new Error(`Unexpected "eol" ${JSON.stringify(t)}.`)}let n=e.match(r);return n?n.length:0}function ur(e){return te(!1,e,/\r\n?/gu,`
`)}var W="string",Y="array",j="cursor",N="indent",O="align",P="trim",B="group",k="fill",_="if-break",v="indent-if-break",L="line-suffix",I="line-suffix-boundary",g="line",S="label",w="break-parent",Ue=new Set([j,N,O,P,B,k,_,v,L,I,g,S,w]);var Bu=(e,t,r)=>{if(!(e&&t==null))return Array.isArray(t)||typeof t=="string"?t[r<0?t.length+r:r]:t.at(r)},y=Bu;function or(e){let t=e.length;for(;t>0&&(e[t-1]==="\r"||e[t-1]===`
`);)t--;return t<e.length?e.slice(0,t):e}function _u(e){if(typeof e=="string")return W;if(Array.isArray(e))return Y;if(!e)return;let{type:t}=e;if(Ue.has(t))return t}var M=_u;var xu=e=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(e);function wu(e){let t=e===null?"null":typeof e;if(t!=="string"&&t!=="object")return`Unexpected doc '${t}', 
Expected it to be 'string' or 'object'.`;if(M(e))throw new Error("doc is valid.");let r=Object.prototype.toString.call(e);if(r!=="[object Object]")return`Unexpected doc '${r}'.`;let n=xu([...Ue].map(u=>`'${u}'`));return`Unexpected doc.type '${e.type}'.
Expected it to be ${n}.`}var ht=class extends Error{name="InvalidDocError";constructor(t){super(wu(t)),this.doc=t}},q=ht;var ir={};function bu(e,t,r,n){let u=[e];for(;u.length>0;){let o=u.pop();if(o===ir){r(u.pop());continue}r&&u.push(o,ir);let i=M(o);if(!i)throw new q(o);if((t==null?void 0:t(o))!==!1)switch(i){case Y:case k:{let s=i===Y?o:o.parts;for(let a=s.length,c=a-1;c>=0;--c)u.push(s[c]);break}case _:u.push(o.flatContents,o.breakContents);break;case B:if(n&&o.expandedStates)for(let s=o.expandedStates.length,a=s-1;a>=0;--a)u.push(o.expandedStates[a]);else u.push(o.contents);break;case O:case N:case v:case S:case L:u.push(o.contents);break;case W:case j:case P:case I:case g:case w:break;default:throw new q(o)}}}var le=bu;function be(e,t){if(typeof e=="string")return t(e);let r=new Map;return n(e);function n(o){if(r.has(o))return r.get(o);let i=u(o);return r.set(o,i),i}function u(o){switch(M(o)){case Y:return t(o.map(n));case k:return t({...o,parts:o.parts.map(n)});case _:return t({...o,breakContents:n(o.breakContents),flatContents:n(o.flatContents)});case B:{let{expandedStates:i,contents:s}=o;return i?(i=i.map(n),s=i[0]):s=n(s),t({...o,contents:s,expandedStates:i})}case O:case N:case v:case S:case L:return t({...o,contents:n(o.contents)});case W:case j:case P:case I:case g:case w:return t(o);default:throw new q(o)}}}function Ve(e,t,r){let n=r,u=!1;function o(i){if(u)return!1;let s=t(i);s!==void 0&&(u=!0,n=s)}return le(e,o),n}function ku(e){if(e.type===B&&e.break||e.type===g&&e.hard||e.type===w)return!0}function Dr(e){return Ve(e,ku,!1)}function sr(e){if(e.length>0){let t=y(!1,e,-1);!t.expandedStates&&!t.break&&(t.break="propagated")}return null}function cr(e){let t=new Set,r=[];function n(o){if(o.type===w&&sr(r),o.type===B){if(r.push(o),t.has(o))return!1;t.add(o)}}function u(o){o.type===B&&r.pop().break&&sr(r)}le(e,n,u,!0)}function Su(e){return e.type===g&&!e.hard?e.soft?"":" ":e.type===_?e.flatContents:e}function fr(e){return be(e,Su)}function ar(e){for(e=[...e];e.length>=2&&y(!1,e,-2).type===g&&y(!1,e,-1).type===w;)e.length-=2;if(e.length>0){let t=we(y(!1,e,-1));e[e.length-1]=t}return e}function we(e){switch(M(e)){case N:case v:case B:case L:case S:{let t=we(e.contents);return{...e,contents:t}}case _:return{...e,breakContents:we(e.breakContents),flatContents:we(e.flatContents)};case k:return{...e,parts:ar(e.parts)};case Y:return ar(e);case W:return or(e);case O:case j:case P:case I:case g:case w:break;default:throw new q(e)}return e}function $e(e){return we(Nu(e))}function Tu(e){switch(M(e)){case k:if(e.parts.every(t=>t===""))return"";break;case B:if(!e.contents&&!e.id&&!e.break&&!e.expandedStates)return"";if(e.contents.type===B&&e.contents.id===e.id&&e.contents.break===e.break&&e.contents.expandedStates===e.expandedStates)return e.contents;break;case O:case N:case v:case L:if(!e.contents)return"";break;case _:if(!e.flatContents&&!e.breakContents)return"";break;case Y:{let t=[];for(let r of e){if(!r)continue;let[n,...u]=Array.isArray(r)?r:[r];typeof n=="string"&&typeof y(!1,t,-1)=="string"?t[t.length-1]+=n:t.push(n),t.push(...u)}return t.length===0?"":t.length===1?t[0]:t}case W:case j:case P:case I:case g:case S:case w:break;default:throw new q(e)}return e}function Nu(e){return be(e,t=>Tu(t))}function lr(e,t=We){return be(e,r=>typeof r=="string"?ke(t,r.split(`
`)):r)}function Ou(e){if(e.type===g)return!0}function Fr(e){return Ve(e,Ou,!1)}function Fe(e,t){return e.type===S?{...e,contents:t(e.contents)}:t(e)}var gt=()=>{},K=gt,yt=gt,pr=gt;function ie(e){return K(e),{type:N,contents:e}}function oe(e,t){return K(t),{type:O,contents:t,n:e}}function At(e,t={}){return K(e),yt(t.expandedStates,!0),{type:B,id:t.id,contents:e,break:!!t.shouldBreak,expandedStates:t.expandedStates}}function dr(e){return oe(Number.NEGATIVE_INFINITY,e)}function mr(e){return oe({type:"root"},e)}function Er(e){return oe(-1,e)}function Cr(e,t){return At(e[0],{...t,expandedStates:e})}function hr(e){return pr(e),{type:k,parts:e}}function gr(e,t="",r={}){return K(e),t!==""&&K(t),{type:_,breakContents:e,flatContents:t,groupId:r.groupId}}function yr(e,t){return K(e),{type:v,contents:e,groupId:t.groupId,negate:t.negate}}function Se(e){return K(e),{type:L,contents:e}}var Ar={type:I},pe={type:w},Br={type:P},Te={type:g,hard:!0},Bt={type:g,hard:!0,literal:!0},Me={type:g},_r={type:g,soft:!0},z=[Te,pe],We=[Bt,pe],X={type:j};function ke(e,t){K(e),yt(t);let r=[];for(let n=0;n<t.length;n++)n!==0&&r.push(e),r.push(t[n]);return r}function Ge(e,t,r){K(e);let n=e;if(t>0){for(let u=0;u<Math.floor(t/r);++u)n=ie(n);n=oe(t%r,n),n=oe(Number.NEGATIVE_INFINITY,n)}return n}function xr(e,t){return K(t),e?{type:S,label:e,contents:t}:t}function Q(e){var t;if(!e)return"";if(Array.isArray(e)){let r=[];for(let n of e)if(Array.isArray(n))r.push(...Q(n));else{let u=Q(n);u!==""&&r.push(u)}return r}return e.type===_?{...e,breakContents:Q(e.breakContents),flatContents:Q(e.flatContents)}:e.type===B?{...e,contents:Q(e.contents),expandedStates:(t=e.expandedStates)==null?void 0:t.map(Q)}:e.type===k?{type:"fill",parts:e.parts.map(Q)}:e.contents?{...e,contents:Q(e.contents)}:e}function wr(e){let t=Object.create(null),r=new Set;return n(Q(e));function n(o,i,s){var a,c;if(typeof o=="string")return JSON.stringify(o);if(Array.isArray(o)){let D=o.map(n).filter(Boolean);return D.length===1?D[0]:`[${D.join(", ")}]`}if(o.type===g){let D=((a=s==null?void 0:s[i+1])==null?void 0:a.type)===w;return o.literal?D?"literalline":"literallineWithoutBreakParent":o.hard?D?"hardline":"hardlineWithoutBreakParent":o.soft?"softline":"line"}if(o.type===w)return((c=s==null?void 0:s[i-1])==null?void 0:c.type)===g&&s[i-1].hard?void 0:"breakParent";if(o.type===P)return"trim";if(o.type===N)return"indent("+n(o.contents)+")";if(o.type===O)return o.n===Number.NEGATIVE_INFINITY?"dedentToRoot("+n(o.contents)+")":o.n<0?"dedent("+n(o.contents)+")":o.n.type==="root"?"markAsRoot("+n(o.contents)+")":"align("+JSON.stringify(o.n)+", "+n(o.contents)+")";if(o.type===_)return"ifBreak("+n(o.breakContents)+(o.flatContents?", "+n(o.flatContents):"")+(o.groupId?(o.flatContents?"":', ""')+`, { groupId: ${u(o.groupId)} }`:"")+")";if(o.type===v){let D=[];o.negate&&D.push("negate: true"),o.groupId&&D.push(`groupId: ${u(o.groupId)}`);let p=D.length>0?`, { ${D.join(", ")} }`:"";return`indentIfBreak(${n(o.contents)}${p})`}if(o.type===B){let D=[];o.break&&o.break!=="propagated"&&D.push("shouldBreak: true"),o.id&&D.push(`id: ${u(o.id)}`);let p=D.length>0?`, { ${D.join(", ")} }`:"";return o.expandedStates?`conditionalGroup([${o.expandedStates.map(l=>n(l)).join(",")}]${p})`:`group(${n(o.contents)}${p})`}if(o.type===k)return`fill([${o.parts.map(D=>n(D)).join(", ")}])`;if(o.type===L)return"lineSuffix("+n(o.contents)+")";if(o.type===I)return"lineSuffixBoundary";if(o.type===S)return`label(${JSON.stringify(o.label)}, ${n(o.contents)})`;if(o.type===j)return"cursor";throw new Error("Unknown doc type "+o.type)}function u(o){if(typeof o!="symbol")return JSON.stringify(String(o));if(o in t)return t[o];let i=o.description||"symbol";for(let s=0;;s++){let a=i+(s>0?` #${s}`:"");if(!r.has(a))return r.add(a),t[o]=`Symbol.for(${JSON.stringify(a)})`}}}var br=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function kr(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function Sr(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9776&&e<=9783||e>=9800&&e<=9811||e===9855||e>=9866&&e<=9871||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}var Tr=e=>!(kr(e)||Sr(e));var Pu=/[^\x20-\x7F]/u;function vu(e){if(!e)return 0;if(!Pu.test(e))return e.length;e=e.replace(br(),"  ");let t=0;for(let r of e){let n=r.codePointAt(0);n<=31||n>=127&&n<=159||n>=768&&n<=879||(t+=Tr(n)?1:2)}return t}var Ne=vu;var R=Symbol("MODE_BREAK"),H=Symbol("MODE_FLAT"),de=Symbol("cursor"),_t=Symbol("DOC_FILL_PRINTED_LENGTH");function Nr(){return{value:"",length:0,queue:[]}}function Lu(e,t){return xt(e,{type:"indent"},t)}function Iu(e,t,r){return t===Number.NEGATIVE_INFINITY?e.root||Nr():t<0?xt(e,{type:"dedent"},r):t?t.type==="root"?{...e,root:e}:xt(e,{type:typeof t=="string"?"stringAlign":"numberAlign",n:t},r):e}function xt(e,t,r){let n=t.type==="dedent"?e.queue.slice(0,-1):[...e.queue,t],u="",o=0,i=0,s=0;for(let f of n)switch(f.type){case"indent":D(),r.useTabs?a(1):c(r.tabWidth);break;case"stringAlign":D(),u+=f.n,o+=f.n.length;break;case"numberAlign":i+=1,s+=f.n;break;default:throw new Error(`Unexpected type '${f.type}'`)}return l(),{...e,value:u,length:o,queue:n};function a(f){u+="	".repeat(f),o+=r.tabWidth*f}function c(f){u+=" ".repeat(f),o+=f}function D(){r.useTabs?p():l()}function p(){i>0&&a(i),F()}function l(){s>0&&c(s),F()}function F(){i=0,s=0}}function wt(e){let t=0,r=0,n=e.length;e:for(;n--;){let u=e[n];if(u===de){r++;continue}for(let o=u.length-1;o>=0;o--){let i=u[o];if(i===" "||i==="	")t++;else{e[n]=u.slice(0,o+1);break e}}}if(t>0||r>0)for(e.length=n+1;r-- >0;)e.push(de);return t}function Ke(e,t,r,n,u,o){if(r===Number.POSITIVE_INFINITY)return!0;let i=t.length,s=[e],a=[];for(;r>=0;){if(s.length===0){if(i===0)return!0;s.push(t[--i]);continue}let{mode:c,doc:D}=s.pop(),p=M(D);switch(p){case W:a.push(D),r-=Ne(D);break;case Y:case k:{let l=p===Y?D:D.parts,F=D[_t]??0;for(let f=l.length-1;f>=F;f--)s.push({mode:c,doc:l[f]});break}case N:case O:case v:case S:s.push({mode:c,doc:D.contents});break;case P:r+=wt(a);break;case B:{if(o&&D.break)return!1;let l=D.break?R:c,F=D.expandedStates&&l===R?y(!1,D.expandedStates,-1):D.contents;s.push({mode:l,doc:F});break}case _:{let F=(D.groupId?u[D.groupId]||H:c)===R?D.breakContents:D.flatContents;F&&s.push({mode:c,doc:F});break}case g:if(c===R||D.hard)return!0;D.soft||(a.push(" "),r--);break;case L:n=!0;break;case I:if(n)return!1;break}}return!1}function me(e,t){let r={},n=t.printWidth,u=xe(t.endOfLine),o=0,i=[{ind:Nr(),mode:R,doc:e}],s=[],a=!1,c=[],D=0;for(cr(e);i.length>0;){let{ind:l,mode:F,doc:f}=i.pop();switch(M(f)){case W:{let d=u!==`
`?te(!1,f,`
`,u):f;s.push(d),i.length>0&&(o+=Ne(d));break}case Y:for(let d=f.length-1;d>=0;d--)i.push({ind:l,mode:F,doc:f[d]});break;case j:if(D>=2)throw new Error("There are too many 'cursor' in doc.");s.push(de),D++;break;case N:i.push({ind:Lu(l,t),mode:F,doc:f.contents});break;case O:i.push({ind:Iu(l,f.n,t),mode:F,doc:f.contents});break;case P:o-=wt(s);break;case B:switch(F){case H:if(!a){i.push({ind:l,mode:f.break?R:H,doc:f.contents});break}case R:{a=!1;let d={ind:l,mode:H,doc:f.contents},m=n-o,C=c.length>0;if(!f.break&&Ke(d,i,m,C,r))i.push(d);else if(f.expandedStates){let E=y(!1,f.expandedStates,-1);if(f.break){i.push({ind:l,mode:R,doc:E});break}else for(let h=1;h<f.expandedStates.length+1;h++)if(h>=f.expandedStates.length){i.push({ind:l,mode:R,doc:E});break}else{let x=f.expandedStates[h],A={ind:l,mode:H,doc:x};if(Ke(A,i,m,C,r)){i.push(A);break}}}else i.push({ind:l,mode:R,doc:f.contents});break}}f.id&&(r[f.id]=y(!1,i,-1).mode);break;case k:{let d=n-o,m=f[_t]??0,{parts:C}=f,E=C.length-m;if(E===0)break;let h=C[m+0],x=C[m+1],A={ind:l,mode:H,doc:h},$={ind:l,mode:R,doc:h},ue=Ke(A,[],d,c.length>0,r,!0);if(E===1){ue?i.push(A):i.push($);break}let Be={ind:l,mode:H,doc:x},lt={ind:l,mode:R,doc:x};if(E===2){ue?i.push(Be,A):i.push(lt,$);break}let lu=C[m+2],Ft={ind:l,mode:F,doc:{...f,[_t]:m+2}};Ke({ind:l,mode:H,doc:[h,x,lu]},[],d,c.length>0,r,!0)?i.push(Ft,Be,A):ue?i.push(Ft,lt,A):i.push(Ft,lt,$);break}case _:case v:{let d=f.groupId?r[f.groupId]:F;if(d===R){let m=f.type===_?f.breakContents:f.negate?f.contents:ie(f.contents);m&&i.push({ind:l,mode:F,doc:m})}if(d===H){let m=f.type===_?f.flatContents:f.negate?ie(f.contents):f.contents;m&&i.push({ind:l,mode:F,doc:m})}break}case L:c.push({ind:l,mode:F,doc:f.contents});break;case I:c.length>0&&i.push({ind:l,mode:F,doc:Te});break;case g:switch(F){case H:if(f.hard)a=!0;else{f.soft||(s.push(" "),o+=1);break}case R:if(c.length>0){i.push({ind:l,mode:F,doc:f},...c.reverse()),c.length=0;break}f.literal?l.root?(s.push(u,l.root.value),o=l.root.length):(s.push(u),o=0):(o-=wt(s),s.push(u+l.value),o=l.length);break}break;case S:i.push({ind:l,mode:F,doc:f.contents});break;case w:break;default:throw new q(f)}i.length===0&&c.length>0&&(i.push(...c.reverse()),c.length=0)}let p=s.indexOf(de);if(p!==-1){let l=s.indexOf(de,p+1);if(l===-1)return{formatted:s.filter(m=>m!==de).join("")};let F=s.slice(0,p).join(""),f=s.slice(p+1,l).join(""),d=s.slice(l+1).join("");return{formatted:F+f+d,cursorNodeStart:F.length,cursorNodeText:f}}return{formatted:s.join("")}}function Ru(e,t,r=0){let n=0;for(let u=r;u<e.length;++u)e[u]==="	"?n=n+t-n%t:n++;return n}var Ee=Ru;var Z,kt,ze,bt=class{constructor(t){tr(this,Z);this.stack=[t]}get key(){let{stack:t,siblings:r}=this;return y(!1,t,r===null?-2:-4)??null}get index(){return this.siblings===null?null:y(!1,this.stack,-2)}get node(){return y(!1,this.stack,-1)}get parent(){return this.getNode(1)}get grandparent(){return this.getNode(2)}get isInArray(){return this.siblings!==null}get siblings(){let{stack:t}=this,r=y(!1,t,-3);return Array.isArray(r)?r:null}get next(){let{siblings:t}=this;return t===null?null:t[this.index+1]}get previous(){let{siblings:t}=this;return t===null?null:t[this.index-1]}get isFirst(){return this.index===0}get isLast(){let{siblings:t,index:r}=this;return t!==null&&r===t.length-1}get isRoot(){return this.stack.length===1}get root(){return this.stack[0]}get ancestors(){return[...fe(this,Z,ze).call(this)]}getName(){let{stack:t}=this,{length:r}=t;return r>1?y(!1,t,-2):null}getValue(){return y(!1,this.stack,-1)}getNode(t=0){let r=fe(this,Z,kt).call(this,t);return r===-1?null:this.stack[r]}getParentNode(t=0){return this.getNode(t+1)}call(t,...r){let{stack:n}=this,{length:u}=n,o=y(!1,n,-1);for(let i of r)o=o[i],n.push(i,o);try{return t(this)}finally{n.length=u}}callParent(t,r=0){let n=fe(this,Z,kt).call(this,r+1),u=this.stack.splice(n+1);try{return t(this)}finally{this.stack.push(...u)}}each(t,...r){let{stack:n}=this,{length:u}=n,o=y(!1,n,-1);for(let i of r)o=o[i],n.push(i,o);try{for(let i=0;i<o.length;++i)n.push(i,o[i]),t(this,i,o),n.length-=2}finally{n.length=u}}map(t,...r){let n=[];return this.each((u,o,i)=>{n[o]=t(u,o,i)},...r),n}match(...t){let r=this.stack.length-1,n=null,u=this.stack[r--];for(let o of t){if(u===void 0)return!1;let i=null;if(typeof n=="number"&&(i=n,n=this.stack[r--],u=this.stack[r--]),o&&!o(u,n,i))return!1;n=this.stack[r--],u=this.stack[r--]}return!0}findAncestor(t){for(let r of fe(this,Z,ze).call(this))if(t(r))return r}hasAncestor(t){for(let r of fe(this,Z,ze).call(this))if(t(r))return!0;return!1}};Z=new WeakSet,kt=function(t){let{stack:r}=this;for(let n=r.length-1;n>=0;n-=2)if(!Array.isArray(r[n])&&--t<0)return n;return-1},ze=function*(){let{stack:t}=this;for(let r=t.length-3;r>=0;r-=2){let n=t[r];Array.isArray(n)||(yield n)}};var Or=bt;var Pr=new Proxy(()=>{},{get:()=>Pr}),Oe=Pr;function Yu(e){return e!==null&&typeof e=="object"}var vr=Yu;function*Ce(e,t){let{getVisitorKeys:r,filter:n=()=>!0}=t,u=o=>vr(o)&&n(o);for(let o of r(e)){let i=e[o];if(Array.isArray(i))for(let s of i)u(s)&&(yield s);else u(i)&&(yield i)}}function*Lr(e,t){let r=[e];for(let n=0;n<r.length;n++){let u=r[n];for(let o of Ce(u,t))yield o,r.push(o)}}function Ir(e,t){return Ce(e,t).next().done}function he(e){return(t,r,n)=>{let u=!!(n!=null&&n.backwards);if(r===!1)return!1;let{length:o}=t,i=r;for(;i>=0&&i<o;){let s=t.charAt(i);if(e instanceof RegExp){if(!e.test(s))return i}else if(!e.includes(s))return i;u?i--:i++}return i===-1||i===o?i:!1}}var Rr=he(/\s/u),T=he(" 	"),He=he(",; 	"),Je=he(/[^\n\r]/u);function ju(e,t,r){let n=!!(r!=null&&r.backwards);if(t===!1)return!1;let u=e.charAt(t);if(n){if(e.charAt(t-1)==="\r"&&u===`
`)return t-2;if(u===`
`||u==="\r"||u==="\u2028"||u==="\u2029")return t-1}else{if(u==="\r"&&e.charAt(t+1)===`
`)return t+2;if(u===`
`||u==="\r"||u==="\u2028"||u==="\u2029")return t+1}return t}var U=ju;function Uu(e,t,r={}){let n=T(e,r.backwards?t-1:t,r),u=U(e,n,r);return n!==u}var G=Uu;function Vu(e){return Array.isArray(e)&&e.length>0}var qe=Vu;var Yr=new Set(["tokens","comments","parent","enclosingNode","precedingNode","followingNode"]),$u=e=>Object.keys(e).filter(t=>!Yr.has(t));function Wu(e){return e?t=>e(t,Yr):$u}var J=Wu;function Mu(e){let t=e.type||e.kind||"(unknown type)",r=String(e.name||e.id&&(typeof e.id=="object"?e.id.name:e.id)||e.key&&(typeof e.key=="object"?e.key.name:e.key)||e.value&&(typeof e.value=="object"?"":String(e.value))||e.operator||"");return r.length>20&&(r=r.slice(0,19)+"\u2026"),t+(r?" "+r:"")}function St(e,t){(e.comments??(e.comments=[])).push(t),t.printed=!1,t.nodeDescription=Mu(e)}function se(e,t){t.leading=!0,t.trailing=!1,St(e,t)}function ee(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),St(e,t)}function ae(e,t){t.leading=!1,t.trailing=!0,St(e,t)}var Tt=new WeakMap;function Xe(e,t){if(Tt.has(e))return Tt.get(e);let{printer:{getCommentChildNodes:r,canAttachComment:n,getVisitorKeys:u},locStart:o,locEnd:i}=t;if(!n)return[];let s=((r==null?void 0:r(e,t))??[...Ce(e,{getVisitorKeys:J(u)})]).flatMap(a=>n(a)?[a]:Xe(a,t));return s.sort((a,c)=>o(a)-o(c)||i(a)-i(c)),Tt.set(e,s),s}function Ur(e,t,r,n){let{locStart:u,locEnd:o}=r,i=u(t),s=o(t),a=Xe(e,r),c,D,p=0,l=a.length;for(;p<l;){let F=p+l>>1,f=a[F],d=u(f),m=o(f);if(d<=i&&s<=m)return Ur(f,t,r,f);if(m<=i){c=f,p=F+1;continue}if(s<=d){D=f,l=F;continue}throw new Error("Comment location overlaps with node location")}if((n==null?void 0:n.type)==="TemplateLiteral"){let{quasis:F}=n,f=Ot(F,t,r);c&&Ot(F,c,r)!==f&&(c=null),D&&Ot(F,D,r)!==f&&(D=null)}return{enclosingNode:n,precedingNode:c,followingNode:D}}var Nt=()=>!1;function Vr(e,t){let{comments:r}=e;if(delete e.comments,!qe(r)||!t.printer.canAttachComment)return;let n=[],{printer:{experimentalFeatures:{avoidAstMutation:u=!1}={},handleComments:o={}},originalText:i}=t,{ownLine:s=Nt,endOfLine:a=Nt,remaining:c=Nt}=o,D=r.map((p,l)=>({...Ur(e,p,t),comment:p,text:i,options:t,ast:e,isLastComment:r.length-1===l}));for(let[p,l]of D.entries()){let{comment:F,precedingNode:f,enclosingNode:d,followingNode:m,text:C,options:E,ast:h,isLastComment:x}=l,A;if(u?A=[l]:(F.enclosingNode=d,F.precedingNode=f,F.followingNode=m,A=[F,C,E,h,x]),Gu(C,E,D,p))F.placement="ownLine",s(...A)||(m?se(m,F):f?ae(f,F):d?ee(d,F):ee(h,F));else if(Ku(C,E,D,p))F.placement="endOfLine",a(...A)||(f?ae(f,F):m?se(m,F):d?ee(d,F):ee(h,F));else if(F.placement="remaining",!c(...A))if(f&&m){let $=n.length;$>0&&n[$-1].followingNode!==m&&jr(n,E),n.push(l)}else f?ae(f,F):m?se(m,F):d?ee(d,F):ee(h,F)}if(jr(n,t),!u)for(let p of r)delete p.precedingNode,delete p.enclosingNode,delete p.followingNode}var $r=e=>!/[\S\n\u2028\u2029]/u.test(e);function Gu(e,t,r,n){let{comment:u,precedingNode:o}=r[n],{locStart:i,locEnd:s}=t,a=i(u);if(o)for(let c=n-1;c>=0;c--){let{comment:D,precedingNode:p}=r[c];if(p!==o||!$r(e.slice(s(D),a)))break;a=i(D)}return G(e,a,{backwards:!0})}function Ku(e,t,r,n){let{comment:u,followingNode:o}=r[n],{locStart:i,locEnd:s}=t,a=s(u);if(o)for(let c=n+1;c<r.length;c++){let{comment:D,followingNode:p}=r[c];if(p!==o||!$r(e.slice(a,i(D))))break;a=s(D)}return G(e,a)}function jr(e,t){var s,a;let r=e.length;if(r===0)return;let{precedingNode:n,followingNode:u}=e[0],o=t.locStart(u),i;for(i=r;i>0;--i){let{comment:c,precedingNode:D,followingNode:p}=e[i-1];Oe.strictEqual(D,n),Oe.strictEqual(p,u);let l=t.originalText.slice(t.locEnd(c),o);if(((a=(s=t.printer).isGap)==null?void 0:a.call(s,l,t))??/^[\s(]*$/u.test(l))o=t.locStart(c);else break}for(let[c,{comment:D}]of e.entries())c<i?ae(n,D):se(u,D);for(let c of[n,u])c.comments&&c.comments.length>1&&c.comments.sort((D,p)=>t.locStart(D)-t.locStart(p));e.length=0}function Ot(e,t,r){let n=r.locStart(t)-1;for(let u=1;u<e.length;++u)if(n<r.locStart(e[u]))return u-1;return 0}function zu(e,t){let r=t-1;r=T(e,r,{backwards:!0}),r=U(e,r,{backwards:!0}),r=T(e,r,{backwards:!0});let n=U(e,r,{backwards:!0});return r!==n}var Pe=zu;function Wr(e,t){let r=e.node;return r.printed=!0,t.printer.printComment(e,t)}function Hu(e,t){var D;let r=e.node,n=[Wr(e,t)],{printer:u,originalText:o,locStart:i,locEnd:s}=t;if((D=u.isBlockComment)==null?void 0:D.call(u,r)){let p=G(o,s(r))?G(o,i(r),{backwards:!0})?z:Me:" ";n.push(p)}else n.push(z);let c=U(o,T(o,s(r)));return c!==!1&&G(o,c)&&n.push(z),n}function Ju(e,t,r){var c;let n=e.node,u=Wr(e,t),{printer:o,originalText:i,locStart:s}=t,a=(c=o.isBlockComment)==null?void 0:c.call(o,n);if(r!=null&&r.hasLineSuffix&&!(r!=null&&r.isBlock)||G(i,s(n),{backwards:!0})){let D=Pe(i,s(n));return{doc:Se([z,D?z:"",u]),isBlock:a,hasLineSuffix:!0}}return!a||r!=null&&r.hasLineSuffix?{doc:[Se([" ",u]),pe],isBlock:a,hasLineSuffix:!0}:{doc:[" ",u],isBlock:a,hasLineSuffix:!1}}function qu(e,t){let r=e.node;if(!r)return{};let n=t[Symbol.for("printedComments")];if((r.comments||[]).filter(a=>!n.has(a)).length===0)return{leading:"",trailing:""};let o=[],i=[],s;return e.each(()=>{let a=e.node;if(n!=null&&n.has(a))return;let{leading:c,trailing:D}=a;c?o.push(Hu(e,t)):D&&(s=Ju(e,t,s),i.push(s.doc))},"comments"),{leading:o,trailing:i}}function Mr(e,t,r){let{leading:n,trailing:u}=qu(e,r);return!n&&!u?t:Fe(t,o=>[n,o,u])}function Gr(e){let{[Symbol.for("comments")]:t,[Symbol.for("printedComments")]:r}=e;for(let n of t){if(!n.printed&&!r.has(n))throw new Error('Comment "'+n.value.trim()+'" was not printed. Please report this error!');delete n.printed}}function Xu(e){return()=>{}}var Kr=Xu;var ve=class extends Error{name="ConfigError"},Le=class extends Error{name="UndefinedParserError"};var zr={checkIgnorePragma:{category:"Special",type:"boolean",default:!1,description:"Check whether the file's first docblock comment contains '@noprettier' or '@noformat' to determine if it should be formatted.",cliCategory:"Other"},cursorOffset:{category:"Special",type:"int",default:-1,range:{start:-1,end:1/0,step:1},description:"Print (to stderr) where a cursor at the given position would move to after formatting.",cliCategory:"Editor"},endOfLine:{category:"Global",type:"choice",default:"lf",description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:`Maintain existing
(mixed values within one file are normalised by looking at what's used after the first line)`}]},filepath:{category:"Special",type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:"Other",cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{category:"Special",type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:"Other"},parser:{category:"Global",type:"choice",default:void 0,description:"Which parser to use.",exception:e=>typeof e=="string"||typeof e=="function",choices:[{value:"flow",description:"Flow"},{value:"babel",description:"JavaScript"},{value:"babel-flow",description:"Flow"},{value:"babel-ts",description:"TypeScript"},{value:"typescript",description:"TypeScript"},{value:"acorn",description:"JavaScript"},{value:"espree",description:"JavaScript"},{value:"meriyah",description:"JavaScript"},{value:"css",description:"CSS"},{value:"less",description:"Less"},{value:"scss",description:"SCSS"},{value:"json",description:"JSON"},{value:"json5",description:"JSON5"},{value:"jsonc",description:"JSON with Comments"},{value:"json-stringify",description:"JSON.stringify"},{value:"graphql",description:"GraphQL"},{value:"markdown",description:"Markdown"},{value:"mdx",description:"MDX"},{value:"vue",description:"Vue"},{value:"yaml",description:"YAML"},{value:"glimmer",description:"Ember / Handlebars"},{value:"html",description:"HTML"},{value:"angular",description:"Angular"},{value:"lwc",description:"Lightning Web Components"},{value:"mjml",description:"MJML"}]},plugins:{type:"path",array:!0,default:[{value:[]}],category:"Global",description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:e=>typeof e=="string"||typeof e=="object",cliName:"plugin",cliCategory:"Config"},printWidth:{category:"Global",type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:1/0,step:1}},rangeEnd:{category:"Special",type:"int",default:1/0,range:{start:0,end:1/0,step:1},description:`Format code ending at a given character offset (exclusive).
The range will extend forwards to the end of the selected statement.`,cliCategory:"Editor"},rangeStart:{category:"Special",type:"int",default:0,range:{start:0,end:1/0,step:1},description:`Format code starting at a given character offset.
The range will extend backwards to the start of the first line containing the selected statement.`,cliCategory:"Editor"},requirePragma:{category:"Special",type:"boolean",default:!1,description:"Require either '@prettier' or '@format' to be present in the file's first docblock comment in order for it to be formatted.",cliCategory:"Other"},tabWidth:{type:"int",category:"Global",default:2,description:"Number of spaces per indentation level.",range:{start:0,end:1/0,step:1}},useTabs:{category:"Global",type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{category:"Global",type:"choice",default:"auto",description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};function Qe({plugins:e=[],showDeprecated:t=!1}={}){let r=e.flatMap(u=>u.languages??[]),n=[];for(let u of Zu(Object.assign({},...e.map(({options:o})=>o),zr)))!t&&u.deprecated||(Array.isArray(u.choices)&&(t||(u.choices=u.choices.filter(o=>!o.deprecated)),u.name==="parser"&&(u.choices=[...u.choices,...Qu(u.choices,r,e)])),u.pluginDefaults=Object.fromEntries(e.filter(o=>{var i;return((i=o.defaultOptions)==null?void 0:i[u.name])!==void 0}).map(o=>[o.name,o.defaultOptions[u.name]])),n.push(u));return{languages:r,options:n}}function*Qu(e,t,r){let n=new Set(e.map(u=>u.value));for(let u of t)if(u.parsers){for(let o of u.parsers)if(!n.has(o)){n.add(o);let i=r.find(a=>a.parsers&&Object.prototype.hasOwnProperty.call(a.parsers,o)),s=u.name;i!=null&&i.name&&(s+=` (plugin: ${i.name})`),yield{value:o,description:s}}}}function Zu(e){let t=[];for(let[r,n]of Object.entries(e)){let u={name:r,...n};Array.isArray(u.default)&&(u.default=y(!1,u.default,-1).value),t.push(u)}return t}var eo=(e,t)=>{if(!(e&&t==null))return t.toReversed||!Array.isArray(t)?t.toReversed():[...t].reverse()},Hr=eo;var Jr,qr,Xr,Qr,Zr,to=((Jr=globalThis.Deno)==null?void 0:Jr.build.os)==="windows"||((Xr=(qr=globalThis.navigator)==null?void 0:qr.platform)==null?void 0:Xr.startsWith("Win"))||((Zr=(Qr=globalThis.process)==null?void 0:Qr.platform)==null?void 0:Zr.startsWith("win"))||!1;function en(e){if(e=e instanceof URL?e:new URL(e),e.protocol!=="file:")throw new TypeError(`URL must be a file URL: received "${e.protocol}"`);return e}function ro(e){return e=en(e),decodeURIComponent(e.pathname.replace(/%(?![0-9A-Fa-f]{2})/g,"%25"))}function no(e){e=en(e);let t=decodeURIComponent(e.pathname.replace(/\//g,"\\").replace(/%(?![0-9A-Fa-f]{2})/g,"%25")).replace(/^\\*([A-Za-z]:)(\\|$)/,"$1\\");return e.hostname!==""&&(t=`\\\\${e.hostname}${t}`),t}function tn(e){return to?no(e):ro(e)}var rn=tn;var uo=e=>String(e).split(/[/\\]/u).pop();function nn(e,t){if(!t)return;let r=uo(t).toLowerCase();return e.find(({filenames:n})=>n==null?void 0:n.some(u=>u.toLowerCase()===r))??e.find(({extensions:n})=>n==null?void 0:n.some(u=>r.endsWith(u)))}function oo(e,t){if(t)return e.find(({name:r})=>r.toLowerCase()===t)??e.find(({aliases:r})=>r==null?void 0:r.includes(t))??e.find(({extensions:r})=>r==null?void 0:r.includes(`.${t}`))}function un(e,t){if(t){if(String(t).startsWith("file:"))try{t=rn(t)}catch{return}if(typeof t=="string")return e.find(({isSupported:r})=>r==null?void 0:r({filepath:t}))}}function io(e,t){let r=Hr(!1,e.plugins).flatMap(u=>u.languages??[]),n=oo(r,t.language)??nn(r,t.physicalFile)??nn(r,t.file)??un(r,t.physicalFile)??un(r,t.file)??(t.physicalFile,void 0);return n==null?void 0:n.parsers[0]}var on=io;var re={key:e=>/^[$_a-zA-Z][$_a-zA-Z0-9]*$/.test(e)?e:JSON.stringify(e),value(e){if(e===null||typeof e!="object")return JSON.stringify(e);if(Array.isArray(e))return`[${e.map(r=>re.value(r)).join(", ")}]`;let t=Object.keys(e);return t.length===0?"{}":`{ ${t.map(r=>`${re.key(r)}: ${re.value(e[r])}`).join(", ")} }`},pair:({key:e,value:t})=>re.value({[e]:t})};var sn=new Proxy(String,{get:()=>sn}),V=sn;var an=(e,t,{descriptor:r})=>{let n=[`${V.yellow(typeof e=="string"?r.key(e):r.pair(e))} is deprecated`];return t&&n.push(`we now treat it as ${V.blue(typeof t=="string"?r.key(t):r.pair(t))}`),n.join("; ")+"."};var Ze=Symbol.for("vnopts.VALUE_NOT_EXIST"),ge=Symbol.for("vnopts.VALUE_UNCHANGED");var Dn=" ".repeat(2),fn=(e,t,r)=>{let{text:n,list:u}=r.normalizeExpectedResult(r.schemas[e].expected(r)),o=[];return n&&o.push(cn(e,t,n,r.descriptor)),u&&o.push([cn(e,t,u.title,r.descriptor)].concat(u.values.map(i=>ln(i,r.loggerPrintWidth))).join(`
`)),Fn(o,r.loggerPrintWidth)};function cn(e,t,r,n){return[`Invalid ${V.red(n.key(e))} value.`,`Expected ${V.blue(r)},`,`but received ${t===Ze?V.gray("nothing"):V.red(n.value(t))}.`].join(" ")}function ln({text:e,list:t},r){let n=[];return e&&n.push(`- ${V.blue(e)}`),t&&n.push([`- ${V.blue(t.title)}:`].concat(t.values.map(u=>ln(u,r-Dn.length).replace(/^|\n/g,`$&${Dn}`))).join(`
`)),Fn(n,r)}function Fn(e,t){if(e.length===1)return e[0];let[r,n]=e,[u,o]=e.map(i=>i.split(`
`,1)[0].length);return u>t&&u>o?n:r}var Pt=[],pn=[];function vt(e,t){if(e===t)return 0;let r=e;e.length>t.length&&(e=t,t=r);let n=e.length,u=t.length;for(;n>0&&e.charCodeAt(~-n)===t.charCodeAt(~-u);)n--,u--;let o=0;for(;o<n&&e.charCodeAt(o)===t.charCodeAt(o);)o++;if(n-=o,u-=o,n===0)return u;let i,s,a,c,D=0,p=0;for(;D<n;)pn[D]=e.charCodeAt(o+D),Pt[D]=++D;for(;p<u;)for(i=t.charCodeAt(o+p),a=p++,s=p,D=0;D<n;D++)c=i===pn[D]?a:a+1,a=Pt[D],s=Pt[D]=a>s?c>s?s+1:c:c>a?a+1:c;return s}var et=(e,t,{descriptor:r,logger:n,schemas:u})=>{let o=[`Ignored unknown option ${V.yellow(r.pair({key:e,value:t}))}.`],i=Object.keys(u).sort().find(s=>vt(e,s)<3);i&&o.push(`Did you mean ${V.blue(r.key(i))}?`),n.warn(o.join(" "))};var so=["default","expected","validate","deprecated","forward","redirect","overlap","preprocess","postprocess"];function ao(e,t){let r=new e(t),n=Object.create(r);for(let u of so)u in t&&(n[u]=Do(t[u],r,b.prototype[u].length));return n}var b=class{static create(t){return ao(this,t)}constructor(t){this.name=t.name}default(t){}expected(t){return"nothing"}validate(t,r){return!1}deprecated(t,r){return!1}forward(t,r){}redirect(t,r){}overlap(t,r,n){return t}preprocess(t,r){return t}postprocess(t,r){return ge}};function Do(e,t,r){return typeof e=="function"?(...n)=>e(...n.slice(0,r-1),t,...n.slice(r-1)):()=>e}var tt=class extends b{constructor(t){super(t),this._sourceName=t.sourceName}expected(t){return t.schemas[this._sourceName].expected(t)}validate(t,r){return r.schemas[this._sourceName].validate(t,r)}redirect(t,r){return this._sourceName}};var rt=class extends b{expected(){return"anything"}validate(){return!0}};var nt=class extends b{constructor({valueSchema:t,name:r=t.name,...n}){super({...n,name:r}),this._valueSchema=t}expected(t){let{text:r,list:n}=t.normalizeExpectedResult(this._valueSchema.expected(t));return{text:r&&`an array of ${r}`,list:n&&{title:"an array of the following values",values:[{list:n}]}}}validate(t,r){if(!Array.isArray(t))return!1;let n=[];for(let u of t){let o=r.normalizeValidateResult(this._valueSchema.validate(u,r),u);o!==!0&&n.push(o.value)}return n.length===0?!0:{value:n}}deprecated(t,r){let n=[];for(let u of t){let o=r.normalizeDeprecatedResult(this._valueSchema.deprecated(u,r),u);o!==!1&&n.push(...o.map(({value:i})=>({value:[i]})))}return n}forward(t,r){let n=[];for(let u of t){let o=r.normalizeForwardResult(this._valueSchema.forward(u,r),u);n.push(...o.map(dn))}return n}redirect(t,r){let n=[],u=[];for(let o of t){let i=r.normalizeRedirectResult(this._valueSchema.redirect(o,r),o);"remain"in i&&n.push(i.remain),u.push(...i.redirect.map(dn))}return n.length===0?{redirect:u}:{redirect:u,remain:n}}overlap(t,r){return t.concat(r)}};function dn({from:e,to:t}){return{from:[e],to:t}}var ut=class extends b{expected(){return"true or false"}validate(t){return typeof t=="boolean"}};function En(e,t){let r=Object.create(null);for(let n of e){let u=n[t];if(r[u])throw new Error(`Duplicate ${t} ${JSON.stringify(u)}`);r[u]=n}return r}function Cn(e,t){let r=new Map;for(let n of e){let u=n[t];if(r.has(u))throw new Error(`Duplicate ${t} ${JSON.stringify(u)}`);r.set(u,n)}return r}function hn(){let e=Object.create(null);return t=>{let r=JSON.stringify(t);return e[r]?!0:(e[r]=!0,!1)}}function gn(e,t){let r=[],n=[];for(let u of e)t(u)?r.push(u):n.push(u);return[r,n]}function yn(e){return e===Math.floor(e)}function An(e,t){if(e===t)return 0;let r=typeof e,n=typeof t,u=["undefined","object","boolean","number","string"];return r!==n?u.indexOf(r)-u.indexOf(n):r!=="string"?Number(e)-Number(t):e.localeCompare(t)}function Bn(e){return(...t)=>{let r=e(...t);return typeof r=="string"?new Error(r):r}}function Lt(e){return e===void 0?{}:e}function It(e){if(typeof e=="string")return{text:e};let{text:t,list:r}=e;return co((t||r)!==void 0,"Unexpected `expected` result, there should be at least one field."),r?{text:t,list:{title:r.title,values:r.values.map(It)}}:{text:t}}function Rt(e,t){return e===!0?!0:e===!1?{value:t}:e}function Yt(e,t,r=!1){return e===!1?!1:e===!0?r?!0:[{value:t}]:"value"in e?[e]:e.length===0?!1:e}function mn(e,t){return typeof e=="string"||"key"in e?{from:t,to:e}:"from"in e?{from:e.from,to:e.to}:{from:t,to:e.to}}function ot(e,t){return e===void 0?[]:Array.isArray(e)?e.map(r=>mn(r,t)):[mn(e,t)]}function jt(e,t){let r=ot(typeof e=="object"&&"redirect"in e?e.redirect:e,t);return r.length===0?{remain:t,redirect:r}:typeof e=="object"&&"remain"in e?{remain:e.remain,redirect:r}:{redirect:r}}function co(e,t){if(!e)throw new Error(t)}var it=class extends b{constructor(t){super(t),this._choices=Cn(t.choices.map(r=>r&&typeof r=="object"?r:{value:r}),"value")}expected({descriptor:t}){let r=Array.from(this._choices.keys()).map(i=>this._choices.get(i)).filter(({hidden:i})=>!i).map(i=>i.value).sort(An).map(t.value),n=r.slice(0,-2),u=r.slice(-2);return{text:n.concat(u.join(" or ")).join(", "),list:{title:"one of the following values",values:r}}}validate(t){return this._choices.has(t)}deprecated(t){let r=this._choices.get(t);return r&&r.deprecated?{value:t}:!1}forward(t){let r=this._choices.get(t);return r?r.forward:void 0}redirect(t){let r=this._choices.get(t);return r?r.redirect:void 0}};var st=class extends b{expected(){return"a number"}validate(t,r){return typeof t=="number"}};var at=class extends st{expected(){return"an integer"}validate(t,r){return r.normalizeValidateResult(super.validate(t,r),t)===!0&&yn(t)}};var Ie=class extends b{expected(){return"a string"}validate(t){return typeof t=="string"}};var _n=re,xn=et,wn=fn,bn=an;var Dt=class{constructor(t,r){let{logger:n=console,loggerPrintWidth:u=80,descriptor:o=_n,unknown:i=xn,invalid:s=wn,deprecated:a=bn,missing:c=()=>!1,required:D=()=>!1,preprocess:p=F=>F,postprocess:l=()=>ge}=r||{};this._utils={descriptor:o,logger:n||{warn:()=>{}},loggerPrintWidth:u,schemas:En(t,"name"),normalizeDefaultResult:Lt,normalizeExpectedResult:It,normalizeDeprecatedResult:Yt,normalizeForwardResult:ot,normalizeRedirectResult:jt,normalizeValidateResult:Rt},this._unknownHandler=i,this._invalidHandler=Bn(s),this._deprecatedHandler=a,this._identifyMissing=(F,f)=>!(F in f)||c(F,f),this._identifyRequired=D,this._preprocess=p,this._postprocess=l,this.cleanHistory()}cleanHistory(){this._hasDeprecationWarned=hn()}normalize(t){let r={},u=[this._preprocess(t,this._utils)],o=()=>{for(;u.length!==0;){let i=u.shift(),s=this._applyNormalization(i,r);u.push(...s)}};o();for(let i of Object.keys(this._utils.schemas)){let s=this._utils.schemas[i];if(!(i in r)){let a=Lt(s.default(this._utils));"value"in a&&u.push({[i]:a.value})}}o();for(let i of Object.keys(this._utils.schemas)){if(!(i in r))continue;let s=this._utils.schemas[i],a=r[i],c=s.postprocess(a,this._utils);c!==ge&&(this._applyValidation(c,i,s),r[i]=c)}return this._applyPostprocess(r),this._applyRequiredCheck(r),r}_applyNormalization(t,r){let n=[],{knownKeys:u,unknownKeys:o}=this._partitionOptionKeys(t);for(let i of u){let s=this._utils.schemas[i],a=s.preprocess(t[i],this._utils);this._applyValidation(a,i,s);let c=({from:F,to:f})=>{n.push(typeof f=="string"?{[f]:F}:{[f.key]:f.value})},D=({value:F,redirectTo:f})=>{let d=Yt(s.deprecated(F,this._utils),a,!0);if(d!==!1)if(d===!0)this._hasDeprecationWarned(i)||this._utils.logger.warn(this._deprecatedHandler(i,f,this._utils));else for(let{value:m}of d){let C={key:i,value:m};if(!this._hasDeprecationWarned(C)){let E=typeof f=="string"?{key:f,value:m}:f;this._utils.logger.warn(this._deprecatedHandler(C,E,this._utils))}}};ot(s.forward(a,this._utils),a).forEach(c);let l=jt(s.redirect(a,this._utils),a);if(l.redirect.forEach(c),"remain"in l){let F=l.remain;r[i]=i in r?s.overlap(r[i],F,this._utils):F,D({value:F})}for(let{from:F,to:f}of l.redirect)D({value:F,redirectTo:f})}for(let i of o){let s=t[i];this._applyUnknownHandler(i,s,r,(a,c)=>{n.push({[a]:c})})}return n}_applyRequiredCheck(t){for(let r of Object.keys(this._utils.schemas))if(this._identifyMissing(r,t)&&this._identifyRequired(r))throw this._invalidHandler(r,Ze,this._utils)}_partitionOptionKeys(t){let[r,n]=gn(Object.keys(t).filter(u=>!this._identifyMissing(u,t)),u=>u in this._utils.schemas);return{knownKeys:r,unknownKeys:n}}_applyValidation(t,r,n){let u=Rt(n.validate(t,this._utils),t);if(u!==!0)throw this._invalidHandler(r,u.value,this._utils)}_applyUnknownHandler(t,r,n,u){let o=this._unknownHandler(t,r,this._utils);if(o)for(let i of Object.keys(o)){if(this._identifyMissing(i,o))continue;let s=o[i];i in this._utils.schemas?u(i,s):n[i]=s}}_applyPostprocess(t){let r=this._postprocess(t,this._utils);if(r!==ge){if(r.delete)for(let n of r.delete)delete t[n];if(r.override){let{knownKeys:n,unknownKeys:u}=this._partitionOptionKeys(r.override);for(let o of n){let i=r.override[o];this._applyValidation(i,o,this._utils.schemas[o]),t[o]=i}for(let o of u){let i=r.override[o];this._applyUnknownHandler(o,i,t,(s,a)=>{let c=this._utils.schemas[s];this._applyValidation(a,s,c),t[s]=a})}}}}};var Ut;function lo(e,t,{logger:r=!1,isCLI:n=!1,passThrough:u=!1,FlagSchema:o,descriptor:i}={}){if(n){if(!o)throw new Error("'FlagSchema' option is required.");if(!i)throw new Error("'descriptor' option is required.")}else i=re;let s=u?Array.isArray(u)?(l,F)=>u.includes(l)?{[l]:F}:void 0:(l,F)=>({[l]:F}):(l,F,f)=>{let{_:d,...m}=f.schemas;return et(l,F,{...f,schemas:m})},a=Fo(t,{isCLI:n,FlagSchema:o}),c=new Dt(a,{logger:r,unknown:s,descriptor:i}),D=r!==!1;D&&Ut&&(c._hasDeprecationWarned=Ut);let p=c.normalize(e);return D&&(Ut=c._hasDeprecationWarned),p}function Fo(e,{isCLI:t,FlagSchema:r}){let n=[];t&&n.push(rt.create({name:"_"}));for(let u of e)n.push(po(u,{isCLI:t,optionInfos:e,FlagSchema:r})),u.alias&&t&&n.push(tt.create({name:u.alias,sourceName:u.name}));return n}function po(e,{isCLI:t,optionInfos:r,FlagSchema:n}){let{name:u}=e,o={name:u},i,s={};switch(e.type){case"int":i=at,t&&(o.preprocess=Number);break;case"string":i=Ie;break;case"choice":i=it,o.choices=e.choices.map(a=>a!=null&&a.redirect?{...a,redirect:{to:{key:e.name,value:a.redirect}}}:a);break;case"boolean":i=ut;break;case"flag":i=n,o.flags=r.flatMap(a=>[a.alias,a.description&&a.name,a.oppositeDescription&&`no-${a.name}`].filter(Boolean));break;case"path":i=Ie;break;default:throw new Error(`Unexpected type ${e.type}`)}if(e.exception?o.validate=(a,c,D)=>e.exception(a)||c.validate(a,D):o.validate=(a,c,D)=>a===void 0||c.validate(a,D),e.redirect&&(s.redirect=a=>a?{to:typeof e.redirect=="string"?e.redirect:{key:e.redirect.option,value:e.redirect.value}}:void 0),e.deprecated&&(s.deprecated=!0),t&&!e.array){let a=o.preprocess||(c=>c);o.preprocess=(c,D,p)=>D.preprocess(a(Array.isArray(c)?y(!1,c,-1):c),p)}return e.array?nt.create({...t?{preprocess:a=>Array.isArray(a)?a:[a]}:{},...s,valueSchema:i.create(o)}):i.create({...o,...s})}var kn=lo;var mo=(e,t,r)=>{if(!(e&&t==null)){if(t.findLast)return t.findLast(r);for(let n=t.length-1;n>=0;n--){let u=t[n];if(r(u,n,t))return u}}},Vt=mo;function $t(e,t){if(!t)throw new Error("parserName is required.");let r=Vt(!1,e,u=>u.parsers&&Object.prototype.hasOwnProperty.call(u.parsers,t));if(r)return r;let n=`Couldn't resolve parser "${t}".`;throw n+=" Plugins must be explicitly added to the standalone bundle.",new ve(n)}function Sn(e,t){if(!t)throw new Error("astFormat is required.");let r=Vt(!1,e,u=>u.printers&&Object.prototype.hasOwnProperty.call(u.printers,t));if(r)return r;let n=`Couldn't find plugin for AST format "${t}".`;throw n+=" Plugins must be explicitly added to the standalone bundle.",new ve(n)}function Re({plugins:e,parser:t}){let r=$t(e,t);return Wt(r,t)}function Wt(e,t){let r=e.parsers[t];return typeof r=="function"?r():r}function Tn(e,t){let r=e.printers[t];return typeof r=="function"?r():r}var Nn={astFormat:"estree",printer:{},originalText:void 0,locStart:null,locEnd:null};async function Eo(e,t={}){var p;let r={...e};if(!r.parser)if(r.filepath){if(r.parser=on(r,{physicalFile:r.filepath}),!r.parser)throw new Le(`No parser could be inferred for file "${r.filepath}".`)}else throw new Le("No parser and no file path given, couldn't infer a parser.");let n=Qe({plugins:e.plugins,showDeprecated:!0}).options,u={...Nn,...Object.fromEntries(n.filter(l=>l.default!==void 0).map(l=>[l.name,l.default]))},o=$t(r.plugins,r.parser),i=await Wt(o,r.parser);r.astFormat=i.astFormat,r.locEnd=i.locEnd,r.locStart=i.locStart;let s=(p=o.printers)!=null&&p[i.astFormat]?o:Sn(r.plugins,i.astFormat),a=await Tn(s,i.astFormat);r.printer=a;let c=s.defaultOptions?Object.fromEntries(Object.entries(s.defaultOptions).filter(([,l])=>l!==void 0)):{},D={...u,...c};for(let[l,F]of Object.entries(D))(r[l]===null||r[l]===void 0)&&(r[l]=F);return r.parser==="json"&&(r.trailingComma="none"),kn(r,n,{passThrough:Object.keys(Nn),...t})}var ne=Eo;var vn=gu(Pn(),1);async function yo(e,t){let r=await Re(t),n=r.preprocess?r.preprocess(e,t):e;t.originalText=n;let u;try{u=await r.parse(n,t,t)}catch(o){Ao(o,e)}return{text:n,ast:u}}function Ao(e,t){let{loc:r}=e;if(r){let n=(0,vn.codeFrameColumns)(t,r,{highlightCode:!0});throw e.message+=`
`+n,e.codeFrame=n,e}throw e}var De=yo;async function Ln(e,t,r,n,u){let{embeddedLanguageFormatting:o,printer:{embed:i,hasPrettierIgnore:s=()=>!1,getVisitorKeys:a}}=r;if(!i||o!=="auto")return;if(i.length>2)throw new Error("printer.embed has too many parameters. The API changed in Prettier v3. Please update your plugin. See https://prettier.io/docs/plugins#optional-embed");let c=J(i.getVisitorKeys??a),D=[];F();let p=e.stack;for(let{print:f,node:d,pathStack:m}of D)try{e.stack=m;let C=await f(l,t,e,r);C&&u.set(d,C)}catch(C){if(globalThis.PRETTIER_DEBUG)throw C}e.stack=p;function l(f,d){return Bo(f,d,r,n)}function F(){let{node:f}=e;if(f===null||typeof f!="object"||s(e))return;for(let m of c(f))Array.isArray(f[m])?e.each(F,m):e.call(F,m);let d=i(e,r);if(d){if(typeof d=="function"){D.push({print:d,node:f,pathStack:[...e.stack]});return}u.set(f,d)}}}async function Bo(e,t,r,n){let u=await ne({...r,...t,parentParser:r.parser,originalText:e,cursorOffset:void 0,rangeStart:void 0,rangeEnd:void 0},{passThrough:!0}),{ast:o}=await De(e,u),i=await n(o,u);return $e(i)}function _o(e,t){let{originalText:r,[Symbol.for("comments")]:n,locStart:u,locEnd:o,[Symbol.for("printedComments")]:i}=t,{node:s}=e,a=u(s),c=o(s);for(let D of n)u(D)>=a&&o(D)<=c&&i.add(D);return r.slice(a,c)}var In=_o;async function Ye(e,t){({ast:e}=await Gt(e,t));let r=new Map,n=new Or(e),u=Kr(t),o=new Map;await Ln(n,s,t,Ye,o);let i=await Rn(n,t,s,void 0,o);if(Gr(t),t.cursorOffset>=0){if(t.nodeAfterCursor&&!t.nodeBeforeCursor)return[X,i];if(t.nodeBeforeCursor&&!t.nodeAfterCursor)return[i,X]}return i;function s(c,D){return c===void 0||c===n?a(D):Array.isArray(c)?n.call(()=>a(D),...c):n.call(()=>a(D),c)}function a(c){u(n);let D=n.node;if(D==null)return"";let p=D&&typeof D=="object"&&c===void 0;if(p&&r.has(D))return r.get(D);let l=Rn(n,t,s,c,o);return p&&r.set(D,l),l}}function Rn(e,t,r,n,u){var a;let{node:o}=e,{printer:i}=t,s;switch((a=i.hasPrettierIgnore)!=null&&a.call(i,e)?s=In(e,t):u.has(o)?s=u.get(o):s=i.print(e,t,r,n),o){case t.cursorNode:s=Fe(s,c=>[X,c,X]);break;case t.nodeBeforeCursor:s=Fe(s,c=>[c,X]);break;case t.nodeAfterCursor:s=Fe(s,c=>[X,c]);break}return i.printComment&&(!i.willPrintOwnComments||!i.willPrintOwnComments(e,t))&&(s=Mr(e,s,t)),s}async function Gt(e,t){let r=e.comments??[];t[Symbol.for("comments")]=r,t[Symbol.for("printedComments")]=new Set,Vr(e,t);let{printer:{preprocess:n}}=t;return e=n?await n(e,t):e,{ast:e,comments:r}}function xo(e,t){let{cursorOffset:r,locStart:n,locEnd:u}=t,o=J(t.printer.getVisitorKeys),i=F=>n(F)<=r&&u(F)>=r,s=e,a=[e];for(let F of Lr(e,{getVisitorKeys:o,filter:i}))a.push(F),s=F;if(Ir(s,{getVisitorKeys:o}))return{cursorNode:s};let c,D,p=-1,l=Number.POSITIVE_INFINITY;for(;a.length>0&&(c===void 0||D===void 0);){s=a.pop();let F=c!==void 0,f=D!==void 0;for(let d of Ce(s,{getVisitorKeys:o})){if(!F){let m=u(d);m<=r&&m>p&&(c=d,p=m)}if(!f){let m=n(d);m>=r&&m<l&&(D=d,l=m)}}}return{nodeBeforeCursor:c,nodeAfterCursor:D}}var Kt=xo;function wo(e,t){let{printer:{massageAstNode:r,getVisitorKeys:n}}=t;if(!r)return e;let u=J(n),o=r.ignoredProperties??new Set;return i(e);function i(s,a){if(!(s!==null&&typeof s=="object"))return s;if(Array.isArray(s))return s.map(l=>i(l,a)).filter(Boolean);let c={},D=new Set(u(s));for(let l in s)!Object.prototype.hasOwnProperty.call(s,l)||o.has(l)||(D.has(l)?c[l]=i(s[l],s):c[l]=s[l]);let p=r(s,c,a);if(p!==null)return p??c}}var Yn=wo;var bo=(e,t,r)=>{if(!(e&&t==null)){if(t.findLastIndex)return t.findLastIndex(r);for(let n=t.length-1;n>=0;n--){let u=t[n];if(r(u,n,t))return n}return-1}},jn=bo;var ko=({parser:e})=>e==="json"||e==="json5"||e==="jsonc"||e==="json-stringify";function So(e,t){let r=[e.node,...e.parentNodes],n=new Set([t.node,...t.parentNodes]);return r.find(u=>$n.has(u.type)&&n.has(u))}function Un(e){let t=jn(!1,e,r=>r.type!=="Program"&&r.type!=="File");return t===-1?e:e.slice(0,t+1)}function To(e,t,{locStart:r,locEnd:n}){let u=e.node,o=t.node;if(u===o)return{startNode:u,endNode:o};let i=r(e.node);for(let a of Un(t.parentNodes))if(r(a)>=i)o=a;else break;let s=n(t.node);for(let a of Un(e.parentNodes)){if(n(a)<=s)u=a;else break;if(u===o)break}return{startNode:u,endNode:o}}function zt(e,t,r,n,u=[],o){let{locStart:i,locEnd:s}=r,a=i(e),c=s(e);if(!(t>c||t<a||o==="rangeEnd"&&t===a||o==="rangeStart"&&t===c)){for(let D of Xe(e,r)){let p=zt(D,t,r,n,[e,...u],o);if(p)return p}if(!n||n(e,u[0]))return{node:e,parentNodes:u}}}function No(e,t){return t!=="DeclareExportDeclaration"&&e!=="TypeParameterDeclaration"&&(e==="Directive"||e==="TypeAlias"||e==="TSExportAssignment"||e.startsWith("Declare")||e.startsWith("TSDeclare")||e.endsWith("Statement")||e.endsWith("Declaration"))}var $n=new Set(["JsonRoot","ObjectExpression","ArrayExpression","StringLiteral","NumericLiteral","BooleanLiteral","NullLiteral","UnaryExpression","TemplateLiteral"]),Oo=new Set(["OperationDefinition","FragmentDefinition","VariableDefinition","TypeExtensionDefinition","ObjectTypeDefinition","FieldDefinition","DirectiveDefinition","EnumTypeDefinition","EnumValueDefinition","InputValueDefinition","InputObjectTypeDefinition","SchemaDefinition","OperationTypeDefinition","InterfaceTypeDefinition","UnionTypeDefinition","ScalarTypeDefinition"]);function Vn(e,t,r){if(!t)return!1;switch(e.parser){case"flow":case"hermes":case"babel":case"babel-flow":case"babel-ts":case"typescript":case"acorn":case"espree":case"meriyah":case"oxc":case"oxc-ts":case"__babel_estree":return No(t.type,r==null?void 0:r.type);case"json":case"json5":case"jsonc":case"json-stringify":return $n.has(t.type);case"graphql":return Oo.has(t.kind);case"vue":return t.tag!=="root"}return!1}function Wn(e,t,r){let{rangeStart:n,rangeEnd:u,locStart:o,locEnd:i}=t;Oe.ok(u>n);let s=e.slice(n,u).search(/\S/u),a=s===-1;if(!a)for(n+=s;u>n&&!/\S/u.test(e[u-1]);--u);let c=zt(r,n,t,(F,f)=>Vn(t,F,f),[],"rangeStart"),D=a?c:zt(r,u,t,F=>Vn(t,F),[],"rangeEnd");if(!c||!D)return{rangeStart:0,rangeEnd:0};let p,l;if(ko(t)){let F=So(c,D);p=F,l=F}else({startNode:p,endNode:l}=To(c,D,t));return{rangeStart:Math.min(o(p),o(l)),rangeEnd:Math.max(i(p),i(l))}}var zn="\uFEFF",Mn=Symbol("cursor");async function Hn(e,t,r=0){if(!e||e.trim().length===0)return{formatted:"",cursorOffset:-1,comments:[]};let{ast:n,text:u}=await De(e,t);t.cursorOffset>=0&&(t={...t,...Kt(n,t)});let o=await Ye(n,t,r);r>0&&(o=Ge([z,o],r,t.tabWidth));let i=me(o,t);if(r>0){let a=i.formatted.trim();i.cursorNodeStart!==void 0&&(i.cursorNodeStart-=i.formatted.indexOf(a),i.cursorNodeStart<0&&(i.cursorNodeStart=0,i.cursorNodeText=i.cursorNodeText.trimStart()),i.cursorNodeStart+i.cursorNodeText.length>a.length&&(i.cursorNodeText=i.cursorNodeText.trimEnd())),i.formatted=a+xe(t.endOfLine)}let s=t[Symbol.for("comments")];if(t.cursorOffset>=0){let a,c,D,p;if((t.cursorNode||t.nodeBeforeCursor||t.nodeAfterCursor)&&i.cursorNodeText)if(D=i.cursorNodeStart,p=i.cursorNodeText,t.cursorNode)a=t.locStart(t.cursorNode),c=u.slice(a,t.locEnd(t.cursorNode));else{if(!t.nodeBeforeCursor&&!t.nodeAfterCursor)throw new Error("Cursor location must contain at least one of cursorNode, nodeBeforeCursor, nodeAfterCursor");a=t.nodeBeforeCursor?t.locEnd(t.nodeBeforeCursor):0;let C=t.nodeAfterCursor?t.locStart(t.nodeAfterCursor):u.length;c=u.slice(a,C)}else a=0,c=u,D=0,p=i.formatted;let l=t.cursorOffset-a;if(c===p)return{formatted:i.formatted,cursorOffset:D+l,comments:s};let F=c.split("");F.splice(l,0,Mn);let f=p.split(""),d=Et(F,f),m=D;for(let C of d)if(C.removed){if(C.value.includes(Mn))break}else m+=C.count;return{formatted:i.formatted,cursorOffset:m,comments:s}}return{formatted:i.formatted,cursorOffset:-1,comments:s}}async function Po(e,t){let{ast:r,text:n}=await De(e,t),{rangeStart:u,rangeEnd:o}=Wn(n,t,r),i=n.slice(u,o),s=Math.min(u,n.lastIndexOf(`
`,u)+1),a=n.slice(s,u).match(/^\s*/u)[0],c=Ee(a,t.tabWidth),D=await Hn(i,{...t,rangeStart:0,rangeEnd:Number.POSITIVE_INFINITY,cursorOffset:t.cursorOffset>u&&t.cursorOffset<=o?t.cursorOffset-u:-1,endOfLine:"lf"},c),p=D.formatted.trimEnd(),{cursorOffset:l}=t;l>o?l+=p.length-i.length:D.cursorOffset>=0&&(l=D.cursorOffset+u);let F=n.slice(0,u)+p+n.slice(o);if(t.endOfLine!=="lf"){let f=xe(t.endOfLine);l>=0&&f===`\r
`&&(l+=Ct(F.slice(0,l),`
`)),F=te(!1,F,`
`,f)}return{formatted:F,cursorOffset:l,comments:D.comments}}function Ht(e,t,r){return typeof t!="number"||Number.isNaN(t)||t<0||t>e.length?r:t}function Gn(e,t){let{cursorOffset:r,rangeStart:n,rangeEnd:u}=t;return r=Ht(e,r,-1),n=Ht(e,n,0),u=Ht(e,u,e.length),{...t,cursorOffset:r,rangeStart:n,rangeEnd:u}}function Jn(e,t){let{cursorOffset:r,rangeStart:n,rangeEnd:u,endOfLine:o}=Gn(e,t),i=e.charAt(0)===zn;if(i&&(e=e.slice(1),r--,n--,u--),o==="auto"&&(o=nr(e)),e.includes("\r")){let s=a=>Ct(e.slice(0,Math.max(a,0)),`\r
`);r-=s(r),n-=s(n),u-=s(u),e=ur(e)}return{hasBOM:i,text:e,options:Gn(e,{...t,cursorOffset:r,rangeStart:n,rangeEnd:u,endOfLine:o})}}async function Kn(e,t){let r=await Re(t);return!r.hasPragma||r.hasPragma(e)}async function vo(e,t){var n;let r=await Re(t);return(n=r.hasIgnorePragma)==null?void 0:n.call(r,e)}async function Jt(e,t){let{hasBOM:r,text:n,options:u}=Jn(e,await ne(t));if(u.rangeStart>=u.rangeEnd&&n!==""||u.requirePragma&&!await Kn(n,u)||u.checkIgnorePragma&&await vo(n,u))return{formatted:e,cursorOffset:t.cursorOffset,comments:[]};let o;return u.rangeStart>0||u.rangeEnd<n.length?o=await Po(n,u):(!u.requirePragma&&u.insertPragma&&u.printer.insertPragma&&!await Kn(n,u)&&(n=u.printer.insertPragma(n)),o=await Hn(n,u)),r&&(o.formatted=zn+o.formatted,o.cursorOffset>=0&&o.cursorOffset++),o}async function qn(e,t,r){let{text:n,options:u}=Jn(e,await ne(t)),o=await De(n,u);return r&&(r.preprocessForPrint&&(o.ast=await Gt(o.ast,u)),r.massage&&(o.ast=Yn(o.ast,u))),o}async function Xn(e,t){t=await ne(t);let r=await Ye(e,t);return me(r,t)}async function Qn(e,t){let r=wr(e),{formatted:n}=await Jt(r,{...t,parser:"__js_expression"});return n}async function Zn(e,t){t=await ne(t);let{ast:r}=await De(e,t);return t.cursorOffset>=0&&(t={...t,...Kt(r,t)}),Ye(r,t)}async function eu(e,t){return me(e,await ne(t))}var qt={};dt(qt,{builders:()=>Io,printer:()=>Ro,utils:()=>Yo});var Io={join:ke,line:Me,softline:_r,hardline:z,literalline:We,group:At,conditionalGroup:Cr,fill:hr,lineSuffix:Se,lineSuffixBoundary:Ar,cursor:X,breakParent:pe,ifBreak:gr,trim:Br,indent:ie,indentIfBreak:yr,align:oe,addAlignmentToDoc:Ge,markAsRoot:mr,dedentToRoot:dr,dedent:Er,hardlineWithoutBreakParent:Te,literallineWithoutBreakParent:Bt,label:xr,concat:e=>e},Ro={printDocToString:me},Yo={willBreak:Dr,traverseDoc:le,findInDoc:Ve,mapDoc:be,removeLines:fr,stripTrailingHardline:$e,replaceEndOfLine:lr,canBreak:Fr};var tu="3.6.2";var Qt={};dt(Qt,{addDanglingComment:()=>ee,addLeadingComment:()=>se,addTrailingComment:()=>ae,getAlignmentSize:()=>Ee,getIndentSize:()=>ru,getMaxContinuousCount:()=>nu,getNextNonSpaceNonCommentCharacter:()=>uu,getNextNonSpaceNonCommentCharacterIndex:()=>Xo,getPreferredQuote:()=>iu,getStringWidth:()=>Ne,hasNewline:()=>G,hasNewlineInRange:()=>su,hasSpaces:()=>au,isNextLineEmpty:()=>ti,isNextLineEmptyAfterIndex:()=>ct,isPreviousLineEmpty:()=>Zo,makeString:()=>Du,skip:()=>he,skipEverythingButNewLine:()=>Je,skipInlineComment:()=>ye,skipNewline:()=>U,skipSpaces:()=>T,skipToLineEnd:()=>He,skipTrailingComment:()=>Ae,skipWhitespace:()=>Rr});function jo(e,t){if(t===!1)return!1;if(e.charAt(t)==="/"&&e.charAt(t+1)==="*"){for(let r=t+2;r<e.length;++r)if(e.charAt(r)==="*"&&e.charAt(r+1)==="/")return r+2}return t}var ye=jo;function Uo(e,t){return t===!1?!1:e.charAt(t)==="/"&&e.charAt(t+1)==="/"?Je(e,t):t}var Ae=Uo;function Vo(e,t){let r=null,n=t;for(;n!==r;)r=n,n=T(e,n),n=ye(e,n),n=Ae(e,n),n=U(e,n);return n}var je=Vo;function $o(e,t){let r=null,n=t;for(;n!==r;)r=n,n=He(e,n),n=ye(e,n),n=T(e,n);return n=Ae(e,n),n=U(e,n),n!==!1&&G(e,n)}var ct=$o;function Wo(e,t){let r=e.lastIndexOf(`
`);return r===-1?0:Ee(e.slice(r+1).match(/^[\t ]*/u)[0],t)}var ru=Wo;function Xt(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function Mo(e,t){let r=e.match(new RegExp(`(${Xt(t)})+`,"gu"));return r===null?0:r.reduce((n,u)=>Math.max(n,u.length/t.length),0)}var nu=Mo;function Go(e,t){let r=je(e,t);return r===!1?"":e.charAt(r)}var uu=Go;var ft="'",ou='"';function Ko(e,t){let r=t===!0||t===ft?ft:ou,n=r===ft?ou:ft,u=0,o=0;for(let i of e)i===r?u++:i===n&&o++;return u>o?n:r}var iu=Ko;function zo(e,t,r){for(let n=t;n<r;++n)if(e.charAt(n)===`
`)return!0;return!1}var su=zo;function Ho(e,t,r={}){return T(e,r.backwards?t-1:t,r)!==t}var au=Ho;function Jo(e,t,r){let n=t==='"'?"'":'"',o=te(!1,e,/\\(.)|(["'])/gsu,(i,s,a)=>s===n?s:a===t?"\\"+a:a||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/u.test(s)?s:"\\"+s));return t+o+t}var Du=Jo;function qo(e,t,r){return je(e,r(t))}function Xo(e,t){return arguments.length===2||typeof t=="number"?je(e,t):qo(...arguments)}function Qo(e,t,r){return Pe(e,r(t))}function Zo(e,t){return arguments.length===2||typeof t=="number"?Pe(e,t):Qo(...arguments)}function ei(e,t,r){return ct(e,r(t))}function ti(e,t){return arguments.length===2||typeof t=="number"?ct(e,t):ei(...arguments)}function ce(e,t=1){return async(...r)=>{let n=r[t]??{},u=n.plugins??[];return r[t]={...n,plugins:Array.isArray(u)?u:Object.values(u)},e(...r)}}var cu=ce(Jt);async function fu(e,t){let{formatted:r}=await cu(e,{...t,cursorOffset:-1});return r}async function ri(e,t){return await fu(e,t)===e}var ni=ce(Qe,0),ui={parse:ce(qn),formatAST:ce(Xn),formatDoc:ce(Qn),printToDoc:ce(Zn),printDocToString:ce(eu)};var xf=Zt;export{ui as __debug,ri as check,xf as default,qt as doc,fu as format,cu as formatWithCursor,ni as getSupportInfo,Qt as util,tu as version};
