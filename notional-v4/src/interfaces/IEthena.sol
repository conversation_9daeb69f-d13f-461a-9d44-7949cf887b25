// SPDX-License-Identifier: BUSL-1.1
pragma solidity >=0.8.29;

import {IERC4626} from "@openzeppelin/contracts/interfaces/IERC4626.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

// Mainnet Ethena contract addresses
IsUSDe constant sUSDe = IsUSDe(******************************************);
ERC20 constant USDe = ERC20(******************************************);
// Dai and sDAI are required for trading out of sUSDe
ERC20 constant DAI = ERC20(******************************************);
IERC4626 constant sDAI = IERC4626(******************************************);

interface IsUSDe is IERC4626 {
    struct UserCooldown {
        uint104 cooldownEnd;
        uint152 underlyingAmount;
    }

    function cooldownDuration() external view returns (uint24);
    function cooldowns(address account) external view returns (UserCooldown memory);
    function cooldownShares(uint256 shares) external returns (uint256 assets);
    function unstake(address receiver) external;
}

